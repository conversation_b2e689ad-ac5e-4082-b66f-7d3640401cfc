{"name": "tbc-bo", "version": "1.0.0", "private": true, "scripts": {"__serve": "yarn install && vite serve --config vite.config.ts", "__serve:host": "yarn __serve --port 8081", "__serve:stage": "yarn __serve:host --mode=serve_stage", "__serve:testing": "yarn __serve:host --mode=serve_stage", "dev": "VITE_NO_INITIAL_SCHEMA=true yarn serve", "serve": "yarn __serve --mode=mock", "serve:tbc": "VITE_APP_DEFAULT_PROJECT=1 yarn serve", "serve:bcf": "VITE_APP_DEFAULT_PROJECT=5 yarn serve", "serve:tmg": "yarn serve", "serve:host": "yarn __serve:host --mode=mock", "serve:backend": "yarn __serve:host --mode=backend", "serve:stage": "yarn serve:stage:tmg", "serve:stage:tbc": "VITE_APP_DEFAULT_PROJECT=1 VITE_API_ORIGIN=https://staging-bo.travelbusinessclass.com yarn __serve:stage", "serve:stage:bcf": "VITE_APP_DEFAULT_PROJECT=5 VITE_API_ORIGIN=https://staging-bo.businessclassfares.com yarn __serve:stage", "serve:stage:tmg": "VITE_APP_DEFAULT_PROJECT=-1 VITE_API_ORIGIN=https://staging-bo.tmgbo.com yarn __serve:stage", "serve:testing": "yarn serve:testing:tmg", "serve:testing:tbc": "VITE_APP_DEFAULT_PROJECT=1 VITE_API_ORIGIN=https://testing-bo.travelbusinessclass.com yarn __serve:testing", "serve:testing:bcf": "VITE_APP_DEFAULT_PROJECT=5 VITE_API_ORIGIN=https://testing-bo.businessclassfares.com yarn __serve:testing", "serve:testing:tmg": "VITE_APP_DEFAULT_PROJECT=-1 VITE_API_ORIGIN=https://testing-bo.tmgbo.com yarn __serve:testing", "serve:pub1": "VITE_APP_DEFAULT_PROJECT=-1 VITE_API_ORIGIN=https://pub1-bo.tmgbo.com yarn __serve:stage", "serve:pub2": "VITE_APP_DEFAULT_PROJECT=-1 VITE_API_ORIGIN=https://pub2-bo.tmgbo.com yarn __serve:stage", "serve:pub3": "VITE_APP_DEFAULT_PROJECT=-1 VITE_API_ORIGIN=https://pub3-bo.tmgbo.com yarn __serve:stage", "serve:pub4": "VITE_APP_DEFAULT_PROJECT=-1 VITE_API_ORIGIN=https://pub4-bo.tmgbo.com yarn __serve:stage", "mock": "cross-env PORT=8090 yarn nitro dev", "mock:static": "cross-env PORT=8090 node .output/server/index.mjs", "build": "cross-env NODE_OPTIONS=--max-old-space-size=2560 vite build", "prod": "yarn build --mode=production", "preview": "vite preview public --port 8081", "preview:mock": "NODE_OPTIONS=--max-old-space-size=4096 VITE_API_ORIGIN=http://localhost:8090 VITE_API_BASE_PATH=http://localhost:8090/api VITE_AUTH_BASE_PATH=http://localhost:8090/api/auth vite build --mode mock && yarn preview", "preview:stage": "VITE_APP_DEFAULT_PROJECT=1 VITE_API_ORIGIN=https://staging-bo.travelbusinessclass.com yarn build --mode development && yarn preview", "preview:stage:prod": "VITE_APP_DEFAULT_PROJECT=1 VITE_API_ORIGIN=https://staging-bo.travelbusinessclass.com yarn prod && yarn preview", "lint": "eslint", "fix": "eslint --fix", "test": "vitest", "test:ui": "yarn test --ui", "test:api": "yarn test tests./src-new/Feature/Api", "coverage": "vitest run --coverage", "webstorm-integration": "vitest --watch --reporter=dot --reporter=json --outputFile=.vitest-result.json", "docs": "yarn install && http-server ./docs/dist --port 3300 -o", "make:model": "jiti node-scripts/make/model.ts", "make:seed": "jiti node-scripts/make/seed.ts", "make:resource": "jiti node-scripts/make/resource.ts", "make:factory": "jiti node-scripts/make/factory.ts", "analyze": "vite-bundle-visualizer", "seed": "./cli seed", "check-types": "tsc --noEmit -p tsconfig.check.json | loose-ts-check | jiti node/beautify-ts-errors.ts", "create-types:enrichment": "openapi-typescript https://lead-enrichment.test.tmgclick.com/openapi.json -o ./types/tmgclick/enrichment.ts"}, "dependencies": {"@casl/ability": "^6.5.0", "@casl/vue": "^2.1.1", "@ckeditor/ckeditor5-vue": "^6.0.0", "@left4code/tw-starter": "^1.1.0", "@marcoschulte/vue3-progress": "^0.0.6", "@popperjs/core": "^2.11.6", "@ringcentral/sdk": "^5.0.3", "@ringcentral/subscriptions": "^5.0.3", "@soerenmartius/vue3-clipboard": "^0.1.2", "@tmg/consolidator-tool-frontend": "link:./packages/@tmg/consolidator-tool-frontend", "@tmg/markup-tool-frontend": "link:./packages/@tmg/markup-tool-frontend", "@tmg/service-worker": "link:./packages/@tmg/service-worker", "@tmg/service-worker-orchestrator": "link:./packages/@tmg/service-worker-orchestrator", "@tmg/service-worker-ringcentral": "link:./packages/@tmg/service-worker-ringcentral", "@tmg/service-worker-telemetry": "link:./packages/@tmg/service-worker-telemetry", "@tmg/service-worker-test": "link:./packages/@tmg/service-worker-test", "@tmg/service-worker-auth": "link:./packages/@tmg/service-worker-auth", "@types/multer": "^1.4.7", "@vueform/multiselect": "2.4.2", "@vueform/slider": "^2.0.8", "@vueup/vue-quill": "1.0.0-beta.9", "@vueuse/core": "^10.9.0", "@zhuowenli/vue-feather-icons": "^5.0.2", "axios": "^0.22.0", "body-scroll-lock": "^4.0.0-beta.0", "card-validator": "^8.1.1", "centrifuge": "^5.2.2", "chart.js": "^4.4.2", "ckeditor5": "^42.0.0", "consola": "^3.2.2", "floating-vue": "^2.0.0-beta.20", "google-auth-library": "^9.2.0", "googleapis": "^128.0.0", "idb": "^8.0.1", "jsonwebtoken-esm": "^1.0.3", "laravel-echo": "^1.15.0", "libphonenumber-js": "^1.11.5", "marked": "^4.2.12", "maska": "^3.0.0", "minimatch": "^9.0.0", "ofetch": "^1.3.4", "pinia": "^2.0.33", "pusher-js": "^7.0.6", "qs": "^6.10.1", "query-string": "^8.1.0", "quill": "^1.3.7", "quill-delta": "^4.2.2", "quill-delta-to-html": "^0.12.1", "quill-mention": "^3.0.7", "shiki-es": "^0.2.0", "tippy.js": "^6.3.1", "unplugin-auto-import": "^0.12.0", "unplugin-vue-components": "^0.22.11", "v-calendar": "^3.0.0-alpha.8", "velocity-animate": "^1.5.2", "vite-plugin-pwa": "^0.20.0", "vue": "3.3.9", "vue-chart-3": "^3.1.8", "vue-imask": "^6.4.2", "vue-input-autowidth": "^2.2.0", "vue-pdf-embed": "^1.1.4", "vue-toast-notification": "^2.0", "vue-toastification": "^2.0.0-rc.5", "vue3-click-away": "^1.2.1", "vue3-google-login": "2.0.31", "vue3-observe-visibility2": "^0.0.3", "vuedraggable": "^4.1.0", "wavesurfer.js": "^6.6.3", "zoomist": "^2.1.1"}, "devDependencies": {"@faker-js/faker": "^8.3.0", "@tailwindcss/nesting": "^0.0.0-insiders.565cd3e", "@tbc/js-tools": "https://tbc_repo_frontend:<EMAIL>/travelbusinessclass/js-tools.git#^3.0.5", "@total-typescript/ts-reset": "^0.4.2", "@types/body-scroll-lock": "^3.1.0", "@types/fs-extra": "^11.0.1", "@types/intl": "^1.2.0", "@types/wavesurfer.js": "^6.0.4", "@types/ws": "^8.5.4", "@types/zoomist": "^1.1.0", "@vitejs/plugin-vue": "^4.2.3", "@vitest/ui": "^0.29.2", "@vueuse/components": "^10.3.0", "autoprefixer": "^10.4.8", "axios-mock-adapter": "^1.21.2", "citty": "^0.1.1", "console-log-colors": "^0.4.0", "cross-env": "^7.0.3", "fs-extra": "^11.1.1", "htmlparser2": "^8.0.2", "http-server": "^14.1.1", "jiti": "^1.18.2", "js-beautify": "^1.14.7", "jsdom": "^21.1.0", "loose-ts-check": "^2.0.0", "magic-string": "^0.30.0", "nitropack": "^2.11.11", "postcss": "^8.4.21", "postcss-fixes": "^3.0.0", "sass": "^1.58.3", "source-map-support": "^0.5.21", "tailwindcss": "2.2.19", "tree-sitter": "^0.21.1", "tree-sitter-javascript": "^0.21.4", "tsconfig-paths": "^4.2.0", "unimport": "3.0.8", "unplugin-auto-import": "^0.12.0", "unplugin-vue-components": "^0.22.11", "vite": "4.5.0", "vite-bundle-visualizer": "^0.7.0", "vite-plugin-ejs": "^1.6.4", "vite-plugin-generate-file": "^0.0.4", "vite-plugin-inspect": "^0.8.5", "vite-plugin-mkcert": "1.17.1", "vite-plugin-prebundle": "^0.0.4", "vite-svg-loader": "^4.0.0", "vitest": "^0.29.7", "vue-imask": "^6.4.2", "vue-router": "^4.1.6", "vue-tsc": "^1.8.19", "ws": "^8.13.0", "zod": "^4.0.0-beta.20250424T163858", "openapi-typescript": "^7.3.0"}, "gitHooks": {"pre-commit": ["yarn lint-staged --allow-empty"]}, "lint-staged": {"*.{js,ts,mjs,jsx,vue}": ["yarn fix"]}, "browserslist": ["defaults", "ios > 10"], "packageManager": "yarn@1.22.19", "engines": {"node": "^22.0"}, "resolutions": {"string-width": "4.2.3", "@rc-ex/ws": "link:./packages/@rc-ex/ws", "@ringcentral/sdk": "link:./packages/ringcentral-js/sdk"}}