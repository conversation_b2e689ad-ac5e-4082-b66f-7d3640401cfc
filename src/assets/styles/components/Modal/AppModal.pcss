.app-modal {
    @apply w-full h-full fixed z-50 flex flex-col bg-black/5 overflow-y-auto fancy-scroll;

    &--active {
        z-index: 51;
    }

    &__container {
        @apply m-auto;

        &--default {
            @apply w-full max-w-4xl bg-white relative rounded shadow-md dark:bg-dark-3;
        }

        &--wide {
            @apply w-full max-w-6xl bg-white relative rounded shadow-md dark:bg-dark-3;
        }

        &--flex {
            .app-modal {
                &__body {
                    display: flex;
                    flex-direction: column;
                }
            }
        }

        &--fixed-body {
            display: flex;
            flex-direction: column;

            .app-modal {
                &__body {
                    @apply flex-grow min-h-0;
                }
            }
        }

        /** @todo Move this to modal component */

        &--price-quote {
            @apply max-w-[660px] duration-200 flex flex-col;

            .app-modal__body {
                flex: 1
            }
        }

        /** @todo Move this to modal component */

        &--price-quote-wide {
            @apply max-w-[1300px]
        }

        &--stick-to-side {
            @apply h-full w-[fit-content];
        }

        &.app-modal__container--outer-button {
            .app-modal__close {
                @apply absolute right-full bg-white dark:bg-dark-3 text-gray-600 hover:text-gray-800 dark:hover:text-gray-400 rounded-l-full w-12 h-12 mx-0 top-0;

                &__corner {
                    @apply block absolute right-0 text-white dark:text-dark-3;

                    &--first {
                        @apply bottom-full;
                    }

                    &--second {
                        @apply top-full -rotate-90;
                    }
                }
            }
        }
    }

    &__header {
        @apply h-12 flex items-center justify-between flex-none;

        &--align-left {
            @apply text-left;
        }

        &--align-right {
            @apply text-right;
        }

        &--align-center {
            @apply text-center;
        }

        &__content {
            @apply flex-grow px-4;
        }

        &__title {
            @apply font-medium text-base flex items-center gap-x-4;
        }

        &--with-close-button {
            .app-modal__header__content {
                @apply pr-0;
            }
        }
    }

    &__footer {
        @apply px-4 py-3 mt-auto border-t border-gray-200 dark:border-dark-5;

        .button {
            @apply min-w-20;
        }
    }

    &.app-modal--absolute {
        @apply absolute;
    }

    &.app-modal--right {
        @apply top-0 right-0;

        & > .app-modal__container {
            @apply m-0 ml-auto;

            &--stick-to-side {
                @apply rounded-r-none;
            }

            &--outer-button {
                @apply rounded-tl-none;
            }

            & > .app-modal__body {
                @apply h-full;
            }
        }

        & > .app-modal__header {
            flex-direction: row-reverse;
        }
    }

    &.app-modal--right-bottom {
        @apply top-0 right-0;

        & > .app-modal__container {
            @apply m-0 ml-auto mt-auto;

            &--stick-to-side {
                @apply rounded-r-none;
            }

            &--outer-button {
                @apply rounded-tl-none;
            }

            & > .app-modal__body {
                @apply h-full;
            }
        }

        & > .app-modal__header {
            flex-direction: row-reverse;
        }
    }

    &.app-modal--bottom {
        & > .app-modal__container {
            @apply mt-auto;
        }
    }

    &.app-modal--left {
        @apply top-0 left-0;

        & > .app-modal__container {
            @apply m-0 mr-auto;

            &--stick-to-side {
                @apply rounded-l-none;
            }
        }
    }

    &.app-modal--center {
        @apply top-0 left-0 py-12;

        & > .app-modal__container {
            @apply my-auto;
        }
    }

    &.app-modal--top {
        @apply top-0 left-0;

        & > .app-modal__container {
            @apply my-12;
        }
    }

    &.app-modal--context-menu {
        @apply w-auto h-auto fixed z-[51] overflow-visible;
        background: none;
    }

    &.app-modal--free {
        @apply w-auto h-auto bg-none overflow-visible;
    }

    &.app-modal--no-overlay {
        background: none;
        pointer-events: none;

        & > .app-modal__container {
            pointer-events: auto;
        }
    }

    &__close {
        @apply h-10 w-10 mx-1 text-black dark:text-white flex items-center justify-center cursor-pointer hover:bg-black/5 rounded flex-none;

        &__corner {
            @apply hidden;
        }

        & + .app-modal__header__content {
            @apply text-left;
        }
    }

    &__loading {
        @apply flex items-center justify-center h-full w-full pointer-events-none;

        svg {
            @apply text-secondary-900/30 h-7 w-7;
        }
    }
}

.app-modal-enter-active,
.app-modal-leave-active {
    transition: transform .2s ease-out, opacity .2s ease-out;
    overflow: visible;

    .app-modal__container {
        transition: transform .2s ease-out, opacity .2s ease-out;
    }
}

.app-modal--right {
    &.app-modal-enter-from,
    &.app-modal-leave-to {
        .app-modal__container {
            opacity: 0;
            transform: translateX(48px);
        }
    }

    .app-modal-inner-enter-from,
    .app-modal-inner-leave-to {
        opacity: 0;
        transform: translateX(48px);
    }
}

.app-modal--right-bottom {
    .app-modal-enter-active,
    .app-modal-leave-active {
        transition: transform 5s ease-out, opacity 5s ease-out;
        overflow: visible;

        .app-modal__container {
            transition: transform 5s ease-out, opacity 5s ease-out;
        }
    }
    &.app-modal-enter-from,
    &.app-modal-leave-to {
        .app-modal__container {
            opacity: 0;
            transform: translateX(100%);
        }
    }

    .app-modal-inner-enter-from,
    .app-modal-inner-leave-to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.app-modal--left {
    &.app-modal-enter-from,
    &.app-modal-leave-to {
        .app-modal__container {
            transform: translateX(calc(-100% - 48px));
        }
    }

    .app-modal-inner-enter-from,
    .app-modal-inner-leave-to {
        transform: translateX(calc(-100% - 48px));
    }
}

.app-modal--center, .app-modal--context-menu, .app-modal--top {
    &.app-modal-enter-active,
    &.app-modal-leave-active {
        transition: transform .1s ease-out, opacity .1s ease-out;

        .app-modal__container {
            transition: transform .1s ease-out, opacity .1s ease-out;
        }
    }

    &.app-modal-enter-from,
    &.app-modal-leave-to {
        opacity: 0;

        .app-modal__container {
            opacity: 0;
            transform: translateY(10px);
        }
    }

    .app-modal-inner-enter-from,
    .app-modal-inner-leave-to {
        opacity: 0;
        transform: translateY(10px);
    }
}


.app-modal--free {
    &.app-modal-enter-active,
    &.app-modal-leave-active {
        transition: none;

        .app-modal__container {
            transition: none;
        }
    }

    .app-modal-inner-enter-active,
    .app-modal-inner-leave-active {
        transition: none;
    }
}

/**
 * Modal inner
 * ===========================
 */
.app-modal-inner-enter-active,
.app-modal-inner-leave-active {
    transition: transform .1s ease-out, opacity .1s ease-out;
}

