export type DateString = string // dd/mm/yyyy
export type DateRangeString = string // dd/mm/yyyy-dd/mm/yyyy
export type TimestampRangeString = string ///'123123-123123123' or '123123'

export type TimestampRange = {
    start: number,
    end: number
}

export type DateRange = {
    start: Date,
    end: Date
}

export const parseToDateRange = (value: TimestampRangeString): DateRange => {
    const range = parseToTimestampRange(value)

    return timestampRangeToDateRange(range)
}

/**
 * Parse date generated by dateRangeToDateRangeString()
 * @see dateRangeToDateRangeString
 */
export const parseDateStringToDateRange = (value: DateRangeString): DateRange => {
    // eslint-disable-next-line prefer-const
    let [start, end] = value.split('-')

    if (end === undefined) {
        end = start
    }

    return normalizeDateRange({
        start: Date.parseUTCDate(start),
        end: Date.parseUTCDate(end),
    })
}

export const normalizeDateRange = (value: DateRange): DateRange => {
    return {
        start: value.start.getDayStart(),
        end: value.end.getDayEnd(),
    }
}

export const parseToTimestampRange = (value: TimestampRangeString): TimestampRange => {
    if (value === '') {
        value = '-'
    }

    // eslint-disable-next-line prefer-const
    let [start, end] = value.split('-')

    if (end === undefined) {
        end = start
    }

    return {
        start: Number(start),
        end: Number(end),
    }
}

export const dateRangeToTimestampRange = (range: DateRange): TimestampRange => {
    return {
        start: range.start.unixTimestamp(),
        end: range.end.unixTimestamp(),
    }
}

export const timestampRangeToDateRange = (range: TimestampRange): DateRange => {
    return {
        start: Date.fromUnixTimestamp(range.start),
        end: Date.fromUnixTimestamp(range.end),
    }
}

export const timestampMsRangeToDateRange = (range: TimestampRange): DateRange => {
    return timestampRangeToDateRange({
        start: Date.calcUnixTimestamp(range.start),
        end: Date.calcUnixTimestamp(range.end),
    })
}

export const dateRangeToTimestampRangeString = (range: DateRange): TimestampRangeString => {
    const formattedStart = range.start.unixTimestamp()
    const formattedEnd = range.end.unixTimestamp()

    if (formattedStart === formattedEnd) {
        return `${formattedStart}`
    }

    return `${formattedStart}-${formattedEnd}`
}

/**
 * Can be parsed by parseDateStringToDateRange()
 * @see parseDateStringToDateRange
 */
export const dateRangeToDateRangeString = (range: DateRange): DateRangeString => {
    const formattedStart = range.start.formatUTCDate()
    const formattedEnd = range.end.formatUTCDate()

    if (formattedStart === formattedEnd) {
        return formattedStart
    }

    return `${formattedStart}-${formattedEnd}`
}

export const formatRange = (range: DateRange, format = 'dd/MM/yyyy'): string => {
    const formattedStart = range.start.toFormatUTC(format)
    const formattedEnd = range.end.toFormatUTC(format)

    if (formattedStart === formattedEnd) {
        return formattedStart
    }

    return `${formattedStart} - ${formattedEnd}`
}

export const dateRangeToInputValue = (range: DateRange): DateRangeString => {
    return dateRangeToDateRangeString(range)
}

export const getFullDaysCount = (unixTimestamp: number): number => {
    const result = unixTimestamp / 86400

    return Math.floor(result)
}

export const getDaysInRange = (range: DateRange): number => {
    const { start, end } = dateRangeToTimestampRange(range)

    return getFullDaysCount(end - start)
}

export const samePeriodInPast = (value: DateRange): DateRange => {
    const daysDelta = getDaysInRange(value)

    const result = {
        start: Date.fromUnixTimestamp(value.start.unixTimestamp()),
        end: Date.fromUnixTimestamp(value.end.unixTimestamp()),
    }

    const currentStartDay = value.start.getUTCDate()

    result.end.setUTCDate(currentStartDay - 1)

    if (daysDelta >= 28 && daysDelta <= 31 && currentStartDay === 1) {
        result.start.setUTCFullYear(result.end.getUTCFullYear(), result.end.getUTCMonth(), 1)
    } else if (daysDelta >= 364 && daysDelta <= 365 && currentStartDay === 1) {
        result.start.setUTCFullYear(result.end.getUTCFullYear() - 1, 0, 1)
        result.end.setUTCFullYear(result.start.getUTCFullYear(), 11, 31)
    } else if (daysDelta === 0) {
        result.start.setUTCDate(currentStartDay - 1)
    } else {
        result.start.setUTCDate(currentStartDay - daysDelta - 1)
    }

    return {
        start: result.start.getDayStart(),
        end: result.end.getDayEnd(),
    }
}

/**
 * @deprecated Don't use it at all
 */
export const getDateRangeRequestConditions = (range: DateRange, fieldName: string) => {
    return (q: any) => {
        q.gte(fieldName, range.start.unixTimestamp(), null)
        q.lte(fieldName, range.end.unixTimestamp(), null)
    }
}

/**
 * Returns a range that is similar to the rangeToCut, but is before the rangeToCompare
 *
 * @todo Describe the function in more detail
 */
export const getSimilarRange = (rangeToCut: DateRange, rangeToCompare: DateRange, timezone?: string): DateRange | null => {
    const iStart = rangeToCompare.start.unixTimestamp()
    const iEnd = rangeToCompare.end.unixTimestamp()
    const now = getCurrentDate(timezone).unixTimestamp()

    if (iEnd <= now) {
        return null
    }

    const cStart = rangeToCut.start.unixTimestamp()
    const cEnd = rangeToCut.end.unixTimestamp()

    const iDif = Math.min(iEnd, now) - iStart
    const cDif = Math.min(cEnd, now) - cStart
    const dif = Math.min(iDif, cDif)

    const start = cStart
    const end = cStart + dif

    return timestampRangeToDateRange({
        start,
        end,
    })
}

export const getUTCOffset = (timeZone: string, date?: Date): number => {
    const useDate = date || new Date()

    const offset = useDate.unixTimestamp() - new Date(useDate.toLocaleString('en-US', { timeZone }) + ' UTC').unixTimestamp()

    // if (timeZone === 'America/New_York') {
    //     const standardHoursOffset = -5
    //
    //     offset = 3600 * -standardHoursOffset
    //
    //     if (
    //         useDate.unixTimestamp() >= new Date(2022, 2, 13, 2 - standardHoursOffset, 0, 0).unixTimestamp() &&
    //         useDate.unixTimestamp() <= new Date(2022, 10, 6, 2 - (standardHoursOffset + 1)).unixTimestamp()
    //     ) {
    //         offset -= 3600
    //     }
    // }
    //
    // if (timeZone === 'Europe/London') {
    //     const standardHoursOffset = 0
    //
    //     offset = 3600 * -standardHoursOffset
    //
    //     if (
    //         useDate.unixTimestamp() >= new Date(useDate.getUTCFullYear(), 2, 27, 1 - standardHoursOffset, 0, 0).unixTimestamp() &&
    //         useDate.unixTimestamp() <= new Date(useDate.getUTCFullYear(), 9, 30, 1 - (standardHoursOffset + 1)).unixTimestamp()
    //     ) {
    //         offset += 3600
    //     }
    // }
    //
    // if (timeZone === 'Europe/Chisinau') {
    //     const standardHoursOffset = 3
    //
    //     offset = 3600 * -standardHoursOffset
    //
    //     if (
    //         useDate.unixTimestamp() >= new Date(useDate.getUTCFullYear(), 2, 27, 2 - standardHoursOffset, 0, 0).unixTimestamp() &&
    //         useDate.unixTimestamp() <= new Date(useDate.getUTCFullYear(), 9, 30, 2 - (standardHoursOffset + 1)).unixTimestamp()
    //     ) {
    //         offset += 3600
    //     }
    // }

    return offset
}

export const utcDateToTimeZone = (date: Date, timezone?: string): Date => {
    if (!timezone) {
        return date
    }

    const offset = getUTCOffset(timezone, date)

    return Date.fromUnixTimestamp(date.unixTimestamp() - offset)
}

export const moveDateRangeToTimeZone = (range: DateRange, timeZone: string): DateRange => {
    return {
        start: utcDateToTimeZone(range.start, timeZone),
        end: utcDateToTimeZone(range.end, timeZone),
    }
}

export const utcDateFromTimeZone = (date: Date, timezone: string | undefined): Date => {
    if (!timezone) {
        return date
    }

    const offset = getUTCOffset(timezone, date)

    return Date.fromUnixTimestamp(date.unixTimestamp() + offset)
}

export const moveDateRangeFromTimeZone = (range: DateRange, timezone: string): DateRange => {
    return {
        start: utcDateFromTimeZone(range.start, timezone),
        end: utcDateFromTimeZone(range.end, timezone),
    }
}

export const dateRangesAreEqual = (a: DateRange, b: DateRange): boolean => {
    return a.start.unixTimestamp() === b.start.unixTimestamp() && a.end.unixTimestamp() === b.end.unixTimestamp()
}

/**
 * @deprecated Use "predefinedRanges" instead
 * @todo Remove this function
 */
export const getDateRanges = (timezone: string | undefined = undefined) => {
    return [
        {
            name: 'Today',
            range: getRangeToday(timezone),
        },
        {
            name: 'Yesterday',
            range: getRangeYesterday(timezone),
        },
        {
            name: 'This week',
            range: getRangeThisWeek(timezone),
        },
        {
            name: 'Last week',
            range: getRangeLastWeek(timezone),
        },
        {
            name: 'This month',
            range: getRangeThisMonth(timezone),
        },
        {
            name: 'Last month',
            range: getRangeLastMonth(timezone),
        },
        {
            name: 'This year',
            range: getRangeThisYear(timezone),
        },
        {
            name: 'Last year',
            range: getRangeLastYear(timezone),
        },
    ]
}

/**
 * @deprecated Use getPredefinedRangeByValue() instead
 * @todo Remove this function
 */
export const getRangeLabel = (range: DateRange, todayTimezone: string): string | undefined => {
    return getPredefinedRangeByValue(range, todayTimezone)?.label
}

export function getPredefinedRangeByValue(range: DateRange, todayTimezone: string) {
    return Object.values(predefinedRanges).find(({ resolver }) => {
        return dateRangesAreEqual(resolver(todayTimezone), range)
    })
}

// ===============================================
// ======================== Predefined date ranges
// ===============================================

export type PredefinedDateRange = {
    label: string,
    resolver: (todayTimezone: string) => DateRange
}

export const predefinedRanges = {
    'today': {
        label: 'Today',
        resolver: (todayTimezone: string) => getRangeToday(todayTimezone),
    },
    'yesterday': {
        label: 'Yesterday',
        resolver: (todayTimezone: string) => getRangeYesterday(todayTimezone),
    },
    'thisWeek': {
        label: 'This week',
        resolver: (todayTimezone: string) => getRangeThisWeek(todayTimezone),
    },
    'lastWeek': {
        label: 'Last week',
        resolver: (todayTimezone: string) => getRangeLastWeek(todayTimezone),
    },
    'thisMonth': {
        label: 'This month',
        resolver: (todayTimezone: string) => getRangeThisMonth(todayTimezone),
    },
    'lastMonth': {
        label: 'Last month',
        resolver: (todayTimezone: string) => getRangeLastMonth(todayTimezone),
    },
    'thisYear': {
        label: 'This year',
        resolver: (todayTimezone: string) => getRangeThisYear(todayTimezone),
    },
    'lastYear': {
        label: 'Last year',
        resolver: (todayTimezone: string) => getRangeLastYear(todayTimezone),
    },
    'oneDayAfter': {
        label: '1 day',
        resolver: (todayTimezone: string) => getRangeDaysAfterToday(1, todayTimezone),
    },
    'twoDaysAfter': {
        label: '2 days',
        resolver: (todayTimezone: string) => getRangeDaysAfterToday(2, todayTimezone),
    },
    'threeDaysAfter': {
        label: '3 days',
        resolver: (todayTimezone: string) => getRangeDaysAfterToday(3, todayTimezone),
    },
    'oneWeekAfter': {
        'label': '1 week',
        resolver: (todayTimezone: string) => getRangeWeeksAfterToday(1, todayTimezone),
    },
    'twoWeeksAfter': {
        label: '2 weeks',
        resolver: (todayTimezone: string) => getRangeWeeksAfterToday(2, todayTimezone),
    },
    'oneMonthAfter': {
        label: '1 month',
        resolver: (todayTimezone: string) => getRangeMonthsAfterToday(1, todayTimezone),
    },
} satisfies Record<string, PredefinedDateRange>

/**
 * Get predefined date range by name
 *
 * @param {DateRangeName} name
 * @param {string} todayTimezone
 */
export function getPredefinedRange(name: DateRangeName, todayTimezone: string): DateRange {
    return predefinedRanges[name].resolver(todayTimezone)
}

export type DateRangeName = keyof typeof predefinedRanges

// ========================

export const getCurrentDate = (timezone?: string): Date => {
    // @ts-ignore
    const date = window.isTesting ? new Date(window.testingCurrentTimestamp) : new Date()

    return utcDateToTimeZone(date, timezone)
}

export const getRangeToday = (timezone?: string): DateRange => {
    const now = getCurrentDate(timezone)

    return {
        start: now.getDayStart(),
        end: now.getDayEnd(),
    }
}

export const getRangeYesterday = (timezone?: string): DateRange => {
    const now = getCurrentDate(timezone)
    const day = now.getUTCDate()

    now.setUTCDate(day - 1)

    return {
        start: now.getDayStart(),
        end: now.getDayEnd(),
    }
}

export const getRangeThisWeek = (thisDayTimezone?: string): DateRange => {
    const now = getCurrentDate(thisDayTimezone)

    return {
        start: now.getMonday().getDayStart(),
        end: now.getSunday().getDayEnd(),
    }
}

export const getRangeLastWeek = (timezone?: string): DateRange => {
    const now = getCurrentDate(timezone)
    now.setUTCFullYear(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() - 7)

    return {
        start: now.getMonday().getDayStart(),
        end: now.getSunday().getDayEnd(),
    }
}

export const getRangeThisMonth = (timezone?: string): DateRange => {
    const now = getCurrentDate(timezone)

    now.setUTCDate(1)

    const start = now.getDayStart()
    now.setUTCMonth(now.getUTCMonth() + 1, 0)

    return {
        start,
        end: now.getDayEnd(),
    }
}

export const getRangeLastMonth = (timezone?: string): DateRange => {
    let now = getCurrentDate(timezone)

    const currentMonth = now.getUTCMonth()

    now.setUTCMonth(currentMonth - 1, 1)
    const start = now.getDayStart()

    now = getCurrentDate(timezone)
    now.setUTCMonth(currentMonth, 0)
    const end = now.getDayEnd()

    return {
        start,
        end,
    }
}

export const getRangeThisYear = (timezone?: string): DateRange => {
    let now = getCurrentDate(timezone)

    const currentYear = now.getUTCFullYear()

    now.setUTCFullYear(currentYear, 0, 1)
    const start = now.getDayStart()

    now = getCurrentDate(timezone)
    now.setUTCFullYear(currentYear, 11, 31)
    const end = now.getDayEnd()

    return {
        start,
        end,
    }
}

export const getRangeLastYear = (timezone?: string): DateRange => {
    let now = getCurrentDate(timezone)

    const currentYear = now.getUTCFullYear()

    now.setUTCFullYear(currentYear - 1, 0, 1)
    const start = now.getDayStart()

    now = getCurrentDate(timezone)
    now.setUTCFullYear(currentYear - 1, 11, 31)
    const end = now.getDayEnd()

    return {
        start,
        end,
    }
}

export const getRangeDaysAfterToday = (days: number, timezone?: string): DateRange => {
    const start = getCurrentDate(timezone)
    const currentYear = start.getFullYear()
    const currentMonth = start.getMonth()

    const day = start.getUTCDate()

    const end = getCurrentDate(timezone)

    end.setUTCFullYear(currentYear, currentMonth, day + days)

    return {
        start,
        end,
    }
}

export const getRangeWeeksAfterToday = (weeks: number, timezone?: string): DateRange => {
    return getRangeDaysAfterToday(weeks * 7, timezone)
}

export const getRangeMonthsAfterToday = (months: number, timezone?: string): DateRange => {
    const start = getCurrentDate(timezone)

    const currentYear = start.getFullYear()
    const currentMonth = start.getMonth()

    let daysToAdd = 0

    for (let i = 0; i < months; i++) {
        const daysInMonth = new Date(currentYear, currentMonth + i + 1, 0).getDate()

        daysToAdd += daysInMonth
    }

    return getRangeDaysAfterToday(daysToAdd, timezone)
}

export const getChisinauGmtTime = () => {
    const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: 'Europe/Chisinau',
        timeZoneName: 'shortOffset',
    })
    const parts = formatter.formatToParts(new Date())
    const offsetPart = parts.find(part => part.type === 'timeZoneName')

    return offsetPart ? offsetPart.value + ', Chisinau Time' : 'GMT+2, Chisinau Time'
}

