import FileListModel from '@/api/models/File/FileListModel'
import { defaultProject } from '@/lib/core/helper/WorkspaceProjectHelper'

export default class FileData {
    tmpID
    errors

    /**
     * @property {string|null} id Record in DB id. Only available at fully uploaded files
     * @property {string} tmpID
     * @property {string} originalName
     * @property {string|null} extension
     * @property {File|null} _file
     * @property {number} size
     * @property {boolean} loading
     * @property {number|null} progress
     * @property {string[]} errors
     * @property {function} abort Function to abort upload request
     * @property {ProjectId} project_id
     *
     * @param {?Object} data
     */
    constructor(data = {}) {
        for (const key in data) {
            this[key] = data[key]
        }
    }

    /**
     * @param {File} file
     * @param {?Object} data
     * @return {FileData}
     */
    static fromFile(file, data = {}) {
        const nameParts = file.name.split('.')

        return new this(Object.assign({
            id: null,
            tmpID: file.name,
            name: file.name,
            originalName: file.name,
            extension: file.name.indexOf('.') !== -1 ? nameParts.pop() : null,
            basename: nameParts.join('.'),
            _file: file,
            size: file.size,
            loading: false,
            progress: null,
            errors: file.errors ?? [],
            abort: ('abort' in file) && file.abort ? file.abort : () => null,
            project_id: defaultProject,
        }, data))
    }

    /**
     * @param {FileListModel} file
     */
    static fromFileModel(file) {
        const nameParts = file.file_original_name.split('.')

        return new this({
            id: file.id,
            tmpID: file.file_original_name,
            name: file.file_original_name,
            originalName: file.file_original_name,
            extension: file.file_original_name.indexOf('.') !== -1 ? nameParts.pop() : null,
            basename: nameParts.join('.'),
            _file: null,
            size: file.file_size,
            loading: false,
            progress: 100,
            errors: [],
            abort: () => null,
        })
    }

    get pk() {
        if (!this.id) {
            return
        }

        return usePk('File', this.id)
    }

    get isLoading() {
        return this.loading
    }

    get isLoaded() {
        return this.progress === 100
    }

    get isFinished() {
        return this.isLoaded && ! this.errors.length
    }

    async upload() {
        let record

        try {
            this.loading = true

            /** @todo Create and Add abort callback to FileData */
            record = await FileListModel.instance({
                workspaceProject: this.project_id,
            }).upload(this._file, (progress) => {
                this.progress = progress
            })

            this.loading = false

            this.id = record.id
            this.name = record.file_original_name
            this.file_url = record.file_url
            this._file = null
        } catch (e) {
            this.errors.push(e.text || e.message)
            console.error(e)
        }

        return record
    }

    /**
     * @param {function(filename : string) : boolean} existsCallback Return true if filename exists
     */
    setUniqueFilename(existsCallback) {
        const match = this.basename.toLocaleLowerCase().match(/([\s\S]+?)-(\d+)$/)

        const basename = match ? match[1] : this.basename.toLocaleLowerCase()

        const check = (index = 0) => {
            const newBasename = basename + (index ? '-' + index : '')
            const name = newBasename + (this.extension ? '.' + this.extension : '')

            if (existsCallback(name)) {
                return check(index + 1)
            }

            return {
                filename: name,
                basename: newBasename,
            }
        }

        const { filename: newFilename, basename: newBasename } = check()

        if (newFilename !== this._file.name) {
            this.name = newFilename
            this.basename = newBasename
            this._file = new File([this._file], newFilename, { type: this._file.type })
        }
    }
}
