<template>
    <AppModalWrapper
        header="Add incentive"
        close-button
        class="!max-w-[500px]"
    >
        <div class="border-t grid grid-cols-12 gap-4 gap-y-3 p-4">
            <InputLayoutWrapper
                v-model="form.field.product.field.fare"
                class="col-span-12 sm:col-span-6"
            >
                <InputLabelWrapper class="form-label text-xs font-medium text-gray-600">
                    Net price:*
                </InputLabelWrapper>
                <FormInput class="form-control form-control-sm" />
            </InputLayoutWrapper>
            <InputLayoutWrapper
                v-model="form.field.product.field.sell_price"
                class="col-span-12 sm:col-span-6"
            >
                <InputLabelWrapper class="form-label text-xs font-medium text-gray-600">
                    Sell price:*
                </InputLabelWrapper>
                <FormInput class="form-control form-control-sm" />
            </InputLayoutWrapper>
            <InputLayoutWrapper
                v-model="form.field.product.field.type"
                class="col-span-12 sm:col-span-6"
            >
                <InputLabelWrapper class="form-label text-xs font-medium text-gray-600">
                    Type:*
                </InputLabelWrapper>
                <FormSelectGroups
                    :class="{
                        'border-theme-21': form.field.product.field.type.errors.length
                    }"
                    class="form-multiselect-sm"
                />
            </InputLayoutWrapper>
            <InputLayoutWrapper
                v-model="form.field.product.cardIdentity"
                class="col-span-6"
            >
                <label class="form-label text-xs font-medium text-gray-600">Payment type*</label>
                <FormSelectGroups
                    :class="{
                        'border-theme-21': form.field.product.cardIdentity.errors.length
                    }"
                    class="form-multiselect-sm"
                />
            </InputLayoutWrapper>

            <div class="col-span-12 sm:col-span-6">
                <InputLayoutWrapper
                    v-model="form.field.product.field.check_payment"
                >
                    <InputLabelWrapper class="form-label text-xs font-medium text-gray-600">
                        Ck:*
                    </InputLabelWrapper>

                    <div class="flex gap-1 items-center">
                        <InputCheckbox
                            :model-value="form.field.product.ckEnabled.value"
                            class="--primary focus:ring-0 flex-none"
                            @update:model-value="form.field.product.toggleCk($event)"
                        />
                        <FormInputNumberMask
                            v-tooltip="{content: form.field.product.getCkTooltipContent() }"
                            class="form-control form-control-sm"
                            :readonly="!form.field.product.ckEnabled.value"
                            :class="{
                                'warning': form.field.product.isCkManualWarning()
                            }"
                        />
                    </div>
                </InputLayoutWrapper>
            </div>

            <InputLayoutWrapper
                v-model="form.field.product.field.consolidator_order_id"
                class="col-span-12 sm:col-span-6"
            >
                <InputLabelWrapper class="form-label text-xs font-medium text-gray-600">
                    Confirmation id - PNR:*
                </InputLabelWrapper>
                <FormInput class="form-control form-control-sm" />
            </InputLayoutWrapper>
            <InputLayoutWrapper
                v-model="form.field.product.field.consolidator_area_id"
                class="col-span-12 sm:col-span-6"
            >
                <InputLabelWrapper class="form-label text-xs font-medium text-gray-600">
                    Consolidator:*
                </InputLabelWrapper>
                <FormSelect
                    :class="{
                        'border-theme-21': form.field.product.field.consolidator_area_id.errors.length
                    }"
                    class="form-multiselect-sm"
                />
            </InputLayoutWrapper>
            <InputLayoutWrapper
                v-model="form.field.product.field.remark"
                class="col-span-12"
            >
                <InputLabelWrapper class="form-label text-xs font-medium text-gray-600">
                    Remark
                </InputLabelWrapper>
                <FormTextarea class="form-control w-full min-h-40" />
            </InputLayoutWrapper>
        </div>

        <template #footer>
            <div class="flex justify-end gap-4">
                <AppModalButton class="btn-outline-secondary" @click="close">
                    Close
                </AppModalButton>
                <AppModalButton v-if="btnPress" class="btn-primary">
                    <LoaderIcon class="w-4 h-4" />
                </AppModalButton>
                <AppModalButton
                    v-else
                    class="btn-primary"
                    @click="add"
                >
                    Submit
                </AppModalButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts">
import type { PropType } from 'vue'
import FormHelper from '@/lib/core/helper/FormHelper'
import InputField from '@/lib/FormField/InputField'
import SelectField from '@/lib/FormField/SelectField'
import ProductEntity from '@/api/entity/ProductEntity'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper.vue'
import InputLabelWrapper from '@/components/Form/FormWrappers/InputLabelWrapper.vue'
import FormInput from '@/components/Form/FormField/FormInput.vue'
import FormSelect from '@/components/Form/FormField/FormSelect.vue'
import FormTextarea from '@/components/Form/FormField/FormTextarea.vue'
import LoaderIcon from '@/assets/icons/LoaderIcon.svg?component'
import ProductFormField from '@/lib/FormField/Models/ProductFormField'
import FormSelectGroups from '@/components/Form/FormField/FormSelectGroups.vue'
import { ProjectCardCategory } from '~/api/models/Card/ProjectCard'
import VCCSelectModal from '~/sections/Card/VirtualCard/modals/VCCSelectModal.vue'
import FormInputNumberMask from '@/components/Form/FormField/FormInputNumberMask.vue'

export default defineComponent({
    name: 'SaleIncentiveModal',
    components: {
        FormInputNumberMask,
        FormSelectGroups,
        FormTextarea,
        FormSelect,
        FormInput,
        InputLabelWrapper,
        InputLayoutWrapper,
        LoaderIcon,
    },

    props: {
        saleVersion: {
            type: Object as PropType<{
                id: number,
                check_payment_ps: number
            }>,

            required: true,
        },

        sale: {
            type: Object as PropType<{
                id: number,
                project_id: number
            }>,

            required: true,
        },

        creditCards: {
            type: Array,
            default: () => {
                return []
            },
        },

        virtualCards: {
            type: Array,
            default: () => {
                return []
            },
        },

        saleHelper: {
            type: Object,
            required: true,
        },

    },

    modal: {
        promise: true,
    },

    emits: ['close', 'resolve', 'reject'],

    setup() {
        const vccSelectModal = useModal(VCCSelectModal)

        return {
            vccSelectModal,
        }
    },

    data() {
        return {
            form: new FormHelper({
                sale_version_id: new InputField(true, 'Sale Version Id', {}),
                product: new ProductFormField({
                    type: new SelectField(true, 'Pay Type', {
                        items: ProductEntity.getIncentiveTypeList(this.sale),
                        placeholder: 'Select',
                    }),
                }),
            }),

            btnPress: false,
        }
    },

    computed: {

        saleVersionsComputed() {
            return this.saleVersion
        },
    },

    watch: {
        creditCards: {
            deep: true,
            immediate: true,
            handler(val) {
                this.form.field.product.setCreditCards(val)
            },
        },

        virtualCards: {
            deep: true,
            immediate: true,
            handler(val) {
                // @ts-ignore
                // this.canEditPayments in vModelMixin
                this.form.field.product.setInhouseVCC(this.virtualCards, this.canEditPayments && this.sale.is_connex_pay_enabled)
            },
        },
    },

    mounted() {
        this.form.field.sale_version_id.value = this.saleVersion.id
        this.form.field.product.field.sale_id.value = this.sale.id
        this.form.field.product.init()

        this.form.field.product.event.addListener('change', (...v) => {
            if (v[0].field === 'card_identity') {
                const product = this.form.field.product.value

                if (product.pay_type === 'comVCC' && !product.card_identity && !v[0].data) {
                    this.showVccSelectModal()
                }
            }

            if (v[0].field === 'type') {
                const product = this.form.field.product.value
                const enable = ['Tips', 'Insurance'].includes(product.type)
                this.form.field.product.toggleCk(enable)
            }
        })
    },

    methods: {
        close() {
            this.$emit('close')
        },

        add() {
            if (this.form.validate()) {
                this.btnPress = true
                this.$models.IncentiveSaleModel.insert(this.form.data)
                    .then(apiResponse => {
                        this.btnPress = false
                        this.$emit('resolve', { success: true, result: apiResponse.result })
                        this.close()
                    })
                    .catch(apiError => {
                        this.btnPress = false

                        if (apiError.name === 'ApiErrorForm') {
                            this.form.setErrors(apiError.data)
                            apiError.processWithMessage()
                        }
                    })
            }
        },

        async showVccSelectModal() {
            this.vccSelectModal.open({
                salePk: String(this.sale.id),
            }).then((card_pk) => {
                this.saleHelper.fetchVirtualCards()
                ///
                this.form.field.product.cardIdentity.value = `${ProjectCardCategory.ProjectVirtualCard}-${card_pk}`
            }).catch(payload => {
                this.form.field.product.cardIdentity.value = ''
                this.form.field.product.field.card_identity.value = ''
            })
        },
    },
})
</script>
