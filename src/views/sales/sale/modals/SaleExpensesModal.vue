<template>
    <AppModalWrapper
        close-button
        header="Add sale expense"
        class="!max-w-[500px]"
    >
        <div class="border-t grid grid-cols-12 gap-x-3 gap-y-1.5 p-4">
            <InputLayoutWrapper
                v-model="form.field.product.field.type"
                class="col-span-6"
            >
                <label class="form-label text-xs font-medium text-gray-600">Expenses type*</label>
                <FormSelect
                    :class="{
                        'border-theme-21': form.field.product.field.type.errors.length
                    }"
                    class="form-multiselect-sm"
                />
            </InputLayoutWrapper>

            <InputLayoutWrapper
                v-model="form.field.product.field.fare"
                class="col-span-6"
            >
                <label class="form-label text-xs font-medium text-gray-600">Net price</label>
                <FormInputNumberMask class="form-control form-control-sm" />
            </InputLayoutWrapper>

            <SaleExpensesPointsTradeSection
                v-if="isPointsTrade"
                ref="pointsTradeSection"
                class="col-span-12"
            />
            <InputLayoutWrapper
                v-model="form.field.product.field.sell_price"
                class="col-span-6"
            >
                <label
                    class="form-label text-xs font-medium text-gray-600"
                >Sell price</label>
                <FormInputNumberMask
                    class="form-control form-control-sm"
                />
            </InputLayoutWrapper>
            <InputLayoutWrapper
                v-model="form.field.product.cardIdentity"
                class="col-span-6"
            >
                <label class="form-label text-xs font-medium text-gray-600">Payment type*</label>
                <FormSelectGroups
                    :class="{
                        'border-theme-21': form.field.product.cardIdentity.errors.length
                    }"
                    class="form-multiselect-sm"
                />
            </InputLayoutWrapper>
            <InputLayoutWrapper
                v-model="form.field.product.field.consolidator_area_id"
                class="col-span-6"
            >
                <label class="form-label text-xs font-medium text-gray-600">Consolidator*</label>
                <FormSelect
                    :class="{
                        'border-theme-21': form.field.product.field.consolidator_area_id.errors.length
                    }"
                    class="form-multiselect-sm"
                />
            </InputLayoutWrapper>

            <InputLayoutWrapper
                v-if="show_assignee"
                v-model="form.field.assignee_id"
                class="col-span-6"
            >
                <label class="form-label text-xs font-medium text-gray-600">Assignee*</label>
                <FormSelect
                    :class="{
                        'border-theme-21': form.field.assignee_id.errors.length
                    }"
                    class="form-multiselect-sm"
                />
            </InputLayoutWrapper>

            <div class="col-span-6 ">
                <InputLayoutWrapper
                    v-model="form.field.product.field.check_payment"
                >
                    <label
                        class="form-label text-xs font-medium text-gray-600"
                    >Ck</label>

                    <div class="flex gap-1 items-center">
                        <InputCheckbox
                            :model-value="form.field.product.ckEnabled.value"
                            class="--primary focus:ring-0 flex-none"
                            @update:model-value="form.field.product.toggleCk($event)"
                        />
                        <FormInputNumberMask
                            v-tooltip="{content:form.field.product.getCkTooltipContent()}"
                            class="form-control form-control-sm"
                            :readonly="!form.field.product.ckEnabled.value"
                            :class="{
                                'warning': form.field.product.isCkManualWarning()
                            }"
                        />
                    </div>
                </InputLayoutWrapper>
            </div>
            <InputLayoutWrapper
                v-model="form.field.product.field.remark"
                class="col-span-12"
            >
                <label class="form-label text-xs font-medium text-gray-600">Remark</label>
                <FormTextarea
                    class="form-control w-full min-h-40"
                />
            </InputLayoutWrapper>
        </div>
        <template #footer>
            <div class="flex justify-end gap-4">
                <AppModalButton class="btn-outline-secondary" @click="close">
                    Close
                </AppModalButton>
                <AppModalButton v-if="btnPress" class="btn-primary">
                    <LoaderIcon class="w-4 h-4" />
                </AppModalButton>
                <AppModalButton
                    v-else
                    class="btn-primary"
                    @click="addSaleExpense"
                >
                    Submit
                </AppModalButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts">
import FormHelper from '@/lib/core/helper/FormHelper'
import InputField from '@/lib/FormField/InputField'
import ProductFormField from '@/lib/FormField/Models/ProductFormField'
import SelectField from '@/lib/FormField/SelectField'
import ProductEntity from '@/api/entity/ProductEntity'
import type { PropType } from 'vue'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper.vue'
import FormSelect from '@/components/Form/FormField/FormSelect.vue'
import FormInputNumberMask from '@/components/Form/FormField/FormInputNumberMask.vue'
import FormSelectGroups from '@/components/Form/FormField/FormSelectGroups.vue'
import FormTextarea from '@/components/Form/FormField/FormTextarea.vue'
import { DepartmentName } from '~/api/models/Department/Department'
import type { ModelAttributes } from '~types/lib/Model'
import { getFullName } from '~/lib/Helper/PersonHelper'
import { ProductType } from '~/api/models/Product/Product'
import VCCSelectModal from '~/sections/Card/VirtualCard/modals/VCCSelectModal.vue'
import { ProjectCardCategory } from '~/api/models/Card/ProjectCard'
import SaleExpensesPointsTradeSection from '~/sections/Sale/sections/SaleExpensesPointsTradeSection.vue'

export default defineComponent({
    name: 'SaleExpensesModal',

    components: {
        SaleExpensesPointsTradeSection,
        FormTextarea,
        FormSelectGroups,
        FormInputNumberMask,
        FormSelect,
        InputLayoutWrapper,
    },

    modal: {
        promise: true,
    },

    props: {
        saleVersionID: {
            type: Number,
            required: true,
        },

        sale: {
            type: Object as PropType<{
                id: number,
                project_id: number
            }>,

            required: true,
        },

        saleHelper: {
            type: Object,
            required: true,
        },

        creditCards: {
            type: Array,
            default: () => {
                return []
            },
        },

        virtualCards: {
            type: Array,
            default: () => {
                return []
            },
        },

    },

    emits: ['close', 'resolve', 'reject'],

    setup(props) {
        const { currentUserPk, useDictionary } = useContext()

        const departmentDictionary = useDictionary('Department')
        const agentsDictionary = useDictionary('Agent')
        const specServDep = departmentDictionary.findByName(DepartmentName.SpecialServices)
        const verificationDep = departmentDictionary.findByName(DepartmentName.Verification)
        const vccSelectModal = useModal(VCCSelectModal)

        const agentOptions = {
            specialServices: agentsDictionary.getAgentsByDepartment(usePk(specServDep)).map((item: ModelAttributes<'Agent'>) => ({
                value: item.id,
                label: getFullName(item),
            })),

            verification: agentsDictionary.getAgentsByDepartment(usePk(verificationDep)).map((item: ModelAttributes<'Agent'>) => ({
                value: item.id,
                label: getFullName(item),
            })),
        }

        const isSpecialServiceUser = agentOptions.specialServices.some(item => String(item.value) === currentUserPk)
        const isVerificationUser = agentOptions.verification.some(item => String(item.value) === currentUserPk)

        return {
            vccSelectModal,
            currentUserPk,
            agentOptions,
            isSpecialServiceUser,
            isVerificationUser,
        }
    },

    data() {
        const productTypes = ProductEntity.getAdditionalTypeList()
        const selectProductTypesOptions = productTypes.filter(item => item.value !== 'Cash Upgrade')

        return {
            show_assignee: false,
            form: new FormHelper({
                sale_version_id: new InputField(true, 'Sale Version Id', {}),
                assignee_id: new SelectField(false, 'Agent', {
                    items: [],
                    placeholder: 'Select',
                }),

                product: new ProductFormField({
                    type: new SelectField(true, 'Pay Type', {
                        items: selectProductTypesOptions,
                        placeholder: 'Select',
                    }),
                }),
            }),

            btnPress: false,
        }
    },

    computed: {
        isPointsTrade() {
            return this.form.field.product.field.type.value == ProductType.PointsTrade
        },
    },

    watch: {
        creditCards: {
            deep: true,
            immediate: true,
            handler(val) {
                this.form.field.product.setCreditCards(val)
            },
        },

        virtualCards: {
            deep: true,
            immediate: true,
            handler(val) {
                // @ts-ignore
                // this.canEditPayments in vModelMixin
                this.form.field.product.setInhouseVCC(this.virtualCards, this.canEditPayments && this.sale.is_connex_pay_enabled)
            },
        },
    },

    mounted() {
        this.form.field.sale_version_id.value = this.saleVersionID
        this.form.field.product.field.sale_id.value = this.sale.id
        this.form.field.product.init()

        this.form.field.product.event.addListener('change', (...v) => {
            if (v[0].field === 'card_identity') {
                const product = this.form.field.product.value

                if (product.pay_type === 'comVCC' && !product.card_identity && !v[0].data) {
                    this.showVccSelectModal()
                }
            }

            if (v[0].field === 'type') {
                const isSpecialServicesFee = v[0].data === ProductType.SpecialServicesFee
                const isAirlineReimbursementFee = v[0].data === ProductType.AirlineReimbursementFee

                const enable = isSpecialServicesFee || isAirlineReimbursementFee
                this.show_assignee = enable
                this.form.field.assignee_id.isRequired = enable
                this.form.field.assignee_id.value = enable && (this.isSpecialServiceUser || this.isVerificationUser)
                    ? Number(this.currentUserPk)
                    : null

                if (isSpecialServicesFee) {
                    this.form.field.assignee_id.items = this.agentOptions.specialServices
                } else if (isAirlineReimbursementFee) {
                    this.form.field.assignee_id.items = this.agentOptions.verification
                }
            }
        })
    },

    methods: {
        close() {
            this.$emit('close')
        },

        validatePointsTradeSection() {
            if (this.isPointsTrade && this.$refs.pointsTradeSection) {
                return this.$refs.pointsTradeSection.validateForm()
            }

            return true
        },

        addSaleExpense() {
            if (this.form.validate() && this.validatePointsTradeSection()) {
                this.btnPress = true
                const data = this.form.data
                data.product.net_price = 0
                data.product.external_number = ''
                data.product.consolidator_order_id = ''

                if (this.isPointsTrade) {
                    data.points_trade = this.$refs.pointsTradeSection.getPointsTrade()
                }

                if (!data.product.sell_price) {
                    data.product.sell_price = 0
                }
                delete data.product.created_at

                this.$models.AdditionalExpenseModel.insert(data)
                    .then(apiResponse => {
                        this.btnPress = false
                        this.$emit('resolve', { success: true, result: apiResponse.result })
                        this.close()
                    })
                    .catch(apiError => {
                        this.btnPress = false
                        this.form.setErrors(apiError.data)
                        apiError.processWithMessage()
                    })
            }
        },

        async showVccSelectModal() {
            this.vccSelectModal.open({
                salePk: String(this.sale.id),
            }).then((card_pk) => {
                this.saleHelper.fetchVirtualCards()
                ///
                this.form.field.product.cardIdentity.value = `${ProjectCardCategory.ProjectVirtualCard}-${card_pk}`
            }).catch(payload => {
                this.form.field.product.cardIdentity.value = ''
                this.form.field.product.field.card_identity.value = ''
            })
        },
    },
})
</script>
