<template>
    <div class="flex items-center justify-between mt-5">
        <h6 class="m-0">
            Additional expenses
        </h6>

        <div class="flex gap-2">
            <button
                v-if="$can('edit', 'Sale', sale) && !isSaleAdjusted && !isSaleClosed"
                class="btn btn-sm btn-outline-secondary box ml-auto cursor-pointer"
                @click="addCashUpgrade"
            >
                Cash Upgrade
            </button>

            <button
                v-if="$can('edit', 'Sale', sale) && !isSaleAdjusted && !isSaleClosed"
                class="btn btn-sm btn-outline-secondary box ml-auto cursor-pointer"
                @click="addAdditionalExpense"
            >
                <PlusIcon class="w-4 h-4 mr-1.5" />
                Add expenses
            </button>
        </div>
    </div>
    <div class="card-old mt-2">
        <div class="card-old-body p-2">
            <table
                class="sales-incentive-table max-w-full"
            >
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Sell Price</th>
                        <th>Value</th>
                        <th>Ck</th>
                        <th>Type</th>
                        <th>Payment type</th>
                        <!--                        <th>Consolidator</th>-->
                        <th>Created by</th>
                        <th>Assigned to</th>

                        <th>
                            Approve
                        </th>

                        <th class="text-right">
                            Action
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(item, index) in additionalExpenses"
                        :key="index"
                    >
                        <td>{{ item?.id }}</td>
                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >{{ Number.formatMoney(item.product?.sell_price) }}</span>
                            <InputLayoutWrapper
                                v-else
                                v-model="form.field.products.get(item.product.id).map.field.sell_price"
                            >
                                <FormInputNumberMask
                                    v-autowidth="{
                                        minWidth: '100%',
                                        maxWidth: '100%',
                                    }"
                                    :class="{
                                        'border border-theme-21': form.field.products.get(item.product.id).map.field.sell_price.errors.length
                                    }"
                                    class="inline-edit"
                                />
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >{{ Number.formatMoney(item.product?.fare) }}</span>
                            <InputLayoutWrapper
                                v-else
                                v-model="form.field.products.get(item.product.id).map.field.fare"
                            >
                                <FormInputNumberMask
                                    v-autowidth="{
                                        minWidth: '100%',
                                        maxWidth: '100%',
                                    }"
                                    class="inline-edit"
                                />
                            </InputLayoutWrapper>
                        </td>

                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >{{ Number.formatMoney(item.product.check_payment) }}</span>
                            <div v-else class="flex gap-1 items-center">
                                <InputCheckbox
                                    :model-value="form.field.products.get(item.product.id).map.ckEnabled.value"
                                    class="--primary focus:ring-0"
                                    @update:model-value="form.field.products.get(item.product.id).map.toggleCk($event)"
                                />
                                <div class="w-[50px]">
                                    <InputLayoutWrapper
                                        v-model="form.field.products.get(item.product.id).map.field.check_payment"
                                    >
                                        <FormInputNumberMask
                                            v-autowidth="{
                                                minWidth: '100%',
                                                maxWidth: '100%',
                                            }"
                                            v-tooltip="{content: form.field.products.get(item.product.id).map.getCkTooltipContent() }"
                                            class="inline-edit"
                                            :class="{
                                                'warning': form.field.products.get(item.product.id).map.isCkManualWarning()
                                            }"
                                            :readonly="!form.field.products.get(item.product.id).map.ckEnabled.value"
                                        />
                                    </InputLayoutWrapper>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span
                                v-if="product !== item.product.id || item.product?.type == 'Cash Upgrade'"
                            >{{ item.product?.type }}</span>
                            <InputLayoutWrapper
                                v-else-if="item.product?.type !== 'Cash Upgrade'"
                                v-model="form.field.products.get(item.product.id).map.field.type"
                            >
                                <FormSelect
                                    :class="{
                                        'border-theme-21': form.field.products.get(item.product.id).map.field.type.errors.length
                                    }"
                                    class="form-multiselect-sm w-[80px]"
                                    @enter-value="refetch(item.product.id)"
                                />
                            </InputLayoutWrapper>
                        </td>

                        <td>
                            <span
                                v-if="product !== item.product.id || item.product?.type == 'Cash Upgrade'"
                            >
                                {{ form.field.products.get(item.product.id).map.getCCPaymentTypeText(item) }}
                            </span>

                            <InputLayoutWrapper
                                v-else-if="item.product?.type !== 'Cash Upgrade'"
                                v-model="form.field.products.get(item.product.id).map.cardIdentity"
                            >
                                <FormSelectGroups
                                    :class="{
                                        'border-theme-21': form.field.products.get(item.product.id).map.cardIdentity.errors.length
                                    }"
                                    class="form-multiselect-sm w-[100px]"
                                    @enter-value="(value) => {
                                        if (value === 'comVCC-') {
                                            showVccSelectModal(item.product.id)
                                        }
                                    }"
                                />
                            </InputLayoutWrapper>

                            <button
                                v-if="product === null && item.product.pay_type === 'comVCC' && !!item.product.card_identity && canEditPayments"
                                class="inline box rounded p-0.5 text-green-500 ml-2"
                                @click="showCredentials(item.product.id)"
                            >
                                <EyeIcon
                                    class="w-2 h-2 !stroke-2"
                                />
                            </button>
                        </td>
                        <td class="max-w-[140px]">
                            <span
                                v-tooltip="{content: `${item?.product?.createdBy?.first_name} ${item?.product?.createdBy?.last_name}`}"
                                class="max-w-[110px] truncate pr-2"
                            >
                                {{ getShortName(item.product.createdBy) }}
                            </span>
                            <br>
                            <span class="max-w-[140px] truncate">
                                {{
                                    Date.fromUnixTimestamp(item.product.created_at).toFormatReactive('dd MMM yy, HH:mm')
                                }}
                            </span>
                        </td>

                        <td class="max-w-[120px] ">
                            <div v-if="product !== item.product.id" class="truncate">
                                <AgentInfo
                                    v-if="isAssignable(item) && item.assignee_id "
                                    :pk="String(item.assignee_id)"
                                />
                                <div v-else>
                                    ---
                                </div>
                            </div>

                            <InputLayoutWrapper
                                v-else-if="product === item.product.id && isAssignable(item)"
                                v-model="assigneeForm.field.assignee_id"
                            >
                                <FormSelect
                                    :class="{
                                        'border-theme-21': assigneeForm.field.assignee_id.errors.length
                                    }"
                                    class="form-multiselect-sm w-[80px]"
                                />
                            </InputLayoutWrapper>
                        </td>

                        <td>
                            <ProductClientApprove
                                :product-pk="String(item.product.id)"
                                :credit-cards="creditCards"
                                :sale-pk="String(sale.id)"
                                :sale-version-pk="String(saleVersionID)"
                                :is-sale-closed="!!isSaleClosed"
                                :is-sale-adjusted="!!isSaleAdjusted"
                            />
                        </td>
                        <td align="right">
                            <template
                                v-if="product !== item.product.id"
                            >
                                <ChatOpenButton
                                    v-if="sale.generalChatRoom?.id"
                                    v-slot="{messagesCount, hasNewMessages}"
                                    :branch="item.product.chatBranchName"
                                    :chat-room="sale.generalChatRoom"
                                    class="relative items-center p-1 -my-1 hover:bg-gray-200 rounded"
                                >
                                    <!--                                    :chat-id="sale.generalChatRoom.id"-->
                                    <!--                                    :ringcentral-chat-id="sale.generalChatRoom.ringcentral_id"-->
                                    <MessageCircleIcon class="w-4 h-4" />
                                    <div
                                        v-if="messagesCount"
                                        :class="[hasNewMessages ? 'bg-orange-400 text-white' : 'bg-slate-200 text-gray-700']"
                                        class="sales-ticket-badge top-0 right-0"
                                    >
                                        {{ messagesCount }}
                                    </div>
                                </ChatOpenButton>
                                <button
                                    v-if="$can('edit', 'Sale', sale) && !isSaleAdjusted && !isSaleClosed"
                                    class="px-1"
                                    @click="editProduct(item)"
                                >
                                    <EditIcon
                                        class="w-4 h-4"
                                    />
                                </button>
                                <button
                                    v-if="$can('edit', 'Sale', sale) && !isSaleAdjusted && !isSaleClosed"
                                    class="text-red-500 px-1 ml-1"
                                    @click="removeProduct(item)"
                                >
                                    <Trash2Icon
                                        class="w-4 h-4"
                                    />
                                </button>
                            </template>
                            <template
                                v-else
                            >
                                <button
                                    class="px-1"
                                    @click="saveProduct(item)"
                                >
                                    <CheckIcon
                                        class="w-4 h-4 !stroke-2 text-theme-20"
                                    />
                                </button>
                                <button
                                    class="text-red-500 px-1 ml-1"
                                    @click="cancelProduct(item)"
                                >
                                    <XIcon
                                        class="w-4 h-4"
                                    />
                                </button>
                            </template>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="text-primary-1">
                        <td />
                        <td>{{ $format.money(totalsComputed.sellPrice) }}</td>
                        <td>{{ $format.money(totalsComputed.netPrice) }}</td>
                        <td>{{ $format.money(totalsComputed.checkPayment) }}</td>
                        <td colspan="2">
                            <span class="text-gray-500">Profit:</span> {{ $format.money(totalsComputed.profit) }}
                        </td>
                        <td colspan="4" />
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</template>

<script lang="ts">
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import FormHelper from '@/lib/core/helper/FormHelper'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper.vue'
import FormSelect from '@/components/Form/FormField/FormSelect.vue'
import FormInputNumberMask from '@/components/Form/FormField/FormInputNumberMask.vue'
import FormSelectGroups from '@/components/Form/FormField/FormSelectGroups.vue'
import ProductFormField from '@/lib/FormField/Models/ProductFormField'
import ChatOpenButton from '@/modules/chat/components/ChatOpenButton.vue'
import ArrayMapField from '@/lib/FormField/ArrayMapField'
import SelectField from '@/lib/FormField/SelectField'
import ProductEntity from '@/api/entity/ProductEntity'
import VirtualCardCredentialsModal from '@/views/sales/sale/components/VirtualCardCredentialsModal.vue'
import SaleExpensesModal from '@/views/sales/sale/modals/SaleExpensesModal.vue'

import ProductClientApprove from '~/sections/Sale/components/ProductClientApprove.vue'
import AgentInfo from '~/sections/Agent/components/AgentInfo.vue'
import type { ModelAttributes } from '~types/lib/Model'
import { DepartmentName } from '~/api/models/Department/Department'
import { ProductType } from '~/api/models/Product/Product'
import { getFullName, getShortName } from '~/lib/Helper/PersonHelper'
import CashUpgradeModal from '~/sections/Sale/modals/CashUpgradeModal.vue'
import { ProjectCardCategory } from '~/api/models/Card/ProjectCard'
import VCCSelectModal from '~/sections/Card/VirtualCard/modals/VCCSelectModal.vue'
import InputCheckbox from '~/components/Input/InputCheckbox.vue'

export default defineComponent({
    name: 'AdditionalExpensesBlock',
    components: {
        AgentInfo,
        ProductClientApprove,
        ChatOpenButton,
        FormSelectGroups,
        FormInputNumberMask,
        InputLayoutWrapper,
        FormSelect,
        InputCheckbox,
    },

    parentName: ['SaleComponent'],
    mixins: [ChildMixin],

    setup() {
        const { useDictionary } = useContext()

        const departmentDictionary = useDictionary('Department')
        const agentsDictionary = useDictionary('Agent')
        const specServDep = departmentDictionary.findByName(DepartmentName.SpecialServices)
        const verificationDep = departmentDictionary.findByName(DepartmentName.Verification)
        const vccSelectModal = useModal(VCCSelectModal)

        const cashUpgradeModal = useModal(CashUpgradeModal)

        const agentOptions = {
            specialServices: agentsDictionary.getAgentsByDepartment(usePk(specServDep)).map((item: ModelAttributes<'Agent'>) => ({
                value: item.id,
                label: getFullName(item),
            })),

            verification: agentsDictionary.getAgentsByDepartment(usePk(verificationDep)).map((item: ModelAttributes<'Agent'>) => ({
                value: item.id,
                label: getFullName(item),
            })),
        }

        return {
            vccSelectModal,
            agentOptions,
            useDictionary,
            cashUpgradeModal,
        }
    },

    data() {
        const productTypes = ProductEntity.getAdditionalTypeList()
        const selectProductTypesOptions = productTypes.filter(item => item.value !== 'Cash Upgrade')

        return {
            assigneeForm: new FormHelper({
                assignee_id: new SelectField(false, 'Agent', {
                    items: [],
                    placeholder: 'Select',
                }),
            }),

            form: new FormHelper({
                products: new ArrayMapField(
                    new ProductFormField({
                        type: new SelectField(false, 'Type', { items: selectProductTypesOptions }),
                    }),
                    [],
                    (data, number) => {
                        return data.id || number
                    }),
            }),

            product: null,
            expense: null,
        }
    },

    computed: {
        saleHelper() {
            return this.$related.SaleComponent.saleHelper
        },

        creditCards() {
            return this.$related.SaleComponent.creditCardData.records
        },

        virtualCards() {
            return this.$related.SaleComponent.virtualCards
        },

        sale() {
            return this.$related.SaleComponent.saleModel
        },

        saleVersionID() {
            return this.$related.SaleComponent?.saleVersion?.id
        },

        saleVersionComputed() {
            return this.$related.SaleComponent?.saleVersion
        },

        additionalExpenses() {
            return this.$related.SaleComponent?.additionalExpenses.records
        },

        isSaleAdjusted() {
            return this.saleHelper?.isSaleAdjusted
        },

        isSaleClosed() {
            return this.saleVersionComputed?.is_sale_closed || !this.saleVersionComputed?.is_active
        },

        totalsComputed() {
            return this.saleHelper.saleSummaryData?.additional || {}
        },
    },

    watch: {
        additionalExpenses: {
            deep: true,
            immediate: true,
            handler(val) {
                val.forEach(expense => {
                    // console.log('additionalExpenses WATCH', expense)
                    this.form.field.products.addItem(expense.product, true)
                })

                this.form.field.products.items.forEach(product => {
                    product.map.init()
                    product.map.setCreditCards(this.creditCards)
                    product.map.setInhouseVCC(this.virtualCards, this.canEditPayments)
                })
            },
        },

        creditCards: {
            deep: true,
            immediate: true,
            handler(val) {
                if (val && this.form.field.products.items) {
                    this.form.field.products.items.forEach(product => {
                        product.map.setCreditCards(val)
                    })
                }
            },
        },

        virtualCards: {
            deep: true,
            immediate: true,
            handler(val) {
                setTimeout(() => {
                    if (this.virtualCards && this.form.field.products.items) {
                        this.form.field.products.items.forEach(product => {
                            product.map.setInhouseVCC(this.virtualCards, this.canEditPayments && this.sale.is_connex_pay_enabled)
                        })
                    }
                }, 1000)
            },
        },
    },

    methods: {
        async refetch(id) {
            await this.$nextTick()

            const formItem = this.form.field.products.get(id).map.field

            if (formItem.type.value === ProductType.AirlineReimbursementFee) {
                this.assigneeForm.field.assignee_id.value = null
                this.assigneeForm.field.assignee_id.items = [...this.agentOptions.verification]
            }

            if (formItem.type.value === ProductType.SpecialServicesFee) {
                this.assigneeForm.field.assignee_id.value = null
                this.assigneeForm.field.assignee_id.items = [...this.agentOptions.specialServices]
            }
        },

        getShortName,

        getSaleData() {
            return this.sale
        },

        checkSaleAdjusted() {
            if (this.isSaleAdjusted) {
                this.toastError('Sale adjusted!')

                return true
            }

            return false
        },

        editProduct(item) {
            if (this.checkSaleAdjusted())
                return

            const selectedType = item.product?.type

            if (selectedType === ProductType.SpecialServicesFee) {
                this.assigneeForm.field.assignee_id.items = [...this.agentOptions.specialServices]
            }

            if (selectedType === ProductType.AirlineReimbursementFee) {
                this.assigneeForm.field.assignee_id.items = [...this.agentOptions.verification]
            }

            this.assigneeForm.field.assignee_id.value = item.assignee_id
            this.product = item.product.id
            this.expense = item.id
        },

        save(expense) {
            expense = JSON.parse(JSON.stringify(expense))
            const pk = expense.id
            const product = this.form.field.products.get(this.product).map.value

            if (product.tax === '') {
                product.tax = 0 // TO prevent drop offer is_sent to client
            }

            expense.product = product

            if (product.type === ProductType.SpecialServicesFee || product.type === ProductType.AirlineReimbursementFee) {
                expense.assignee_id = this.assigneeForm.field.assignee_id.value
            } else {
                expense.assignee_id = null
            }

            delete expense.chatRoom
            delete expense.id
            delete expense.sale_version_id
            delete expense.created_at

            this.$models.AdditionalExpenseModel.update(pk, expense)
                .then(apiResponse => {
                    // console.log(apiResponse)
                    this.$related.SaleComponent.additionalExpenses.updateRecord(apiResponse.result)
                    // this.$related.SaleComponent.additionalExpenses.updateRecordByPK(apiResponse.result.id)

                    this.cancelProduct()
                }).catch(apiError => {
                    if (apiError.name === 'ApiErrorForm') {
                        apiError.processed = true
                        this.form.setErrors(apiError.data)
                        this.toastError('Something went wrong. Please try again.')
                    }
                })
        },

        saveProduct(expense) {
            if (this.form.field.products.get(this.product).map.validate()) {
                this.save(expense)
            }
        },

        cancelProduct(expense = undefined) {
            if (expense) {
                this.form.field.products.get(expense.product.id).map.$reset()
            }
            this.product = null
            this.expense = null
            this.assigneeForm.field.assignee_id.value = null
        },

        removeProduct(item) {
            if (this.checkSaleAdjusted())
                return

            this.$confirmDelete().then(() => {
                this.$models.AdditionalExpenseModel.remove(item.id)
                    .then(() => {
                        this.$related.SaleComponent?.additionalExpenses.refresh()
                    })
                    .catch(apiError => {
                        apiError.processWithMessage('Something went wrong. Please try again.')
                    })
            })
        },

        openChatRoom(id) {
            this.$related.SaleComponent.openChatRoom(id)
        },

        addAdditionalExpense() {
            if (this.checkSaleAdjusted())
                return
            this.$showModal(SaleExpensesModal, {}, {
                saleVersionID: this.saleVersionID,
                sale: this.sale,
                creditCards: this.creditCards,
                virtualCards: this.virtualCards,
                saleHelper: this.$related.SaleComponent.saleHelper,
            }).then(resolve => {
                this.$related.SaleComponent.updateSaleExpenses(resolve.result)
            }).catch(() => {
            })
        },

        addCashUpgrade() {
            if (this.checkSaleAdjusted()) {
                return
            }

            this.cashUpgradeModal.open({
                salePk: String(this.sale.id),
                saleVersionPk: String(this.saleVersionID),
            }).then(resolve => {
                this.$related.SaleComponent.refetchSaleExpenses()
                this.$related.SaleComponent.saleHelper.fetchVirtualCards()
            })
        },

        async showCredentials(productId: number) {
            const credentials = await this.$models.ProjectCardModel.getCredentialsUrl(productId)
            const { url } = credentials.result
            window.open(url, '_blank')
        },

        async showVccSelectModal(productId) {
            this.vccSelectModal.open({
                salePk: String(this.sale.id),
            }).then((card_pk) => {
                this.saleHelper.fetchVirtualCards()
                ///
                this.form.field.products.get(productId).map.cardIdentity.value = `${ProjectCardCategory.ProjectVirtualCard}-${card_pk}`
            }).catch(payload => {
                this.form.field.products.get(productId).map.cardIdentity.silentValue = ''
                this.form.field.products.get(productId).map.field.card_identity.value = ''
            })
        },

        isAssignable(additionalExpense) {
            const form_value = this.form.field.products.get(additionalExpense.product.id).map.field.type.value

            return [ProductType.SpecialServicesFee, ProductType.AirlineReimbursementFee].includes(form_value)
        },

    },
})
</script>
