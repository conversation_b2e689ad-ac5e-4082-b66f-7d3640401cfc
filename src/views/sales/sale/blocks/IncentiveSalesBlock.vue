<template>
    <div class="flex items-center justify-between mt-5">
        <h6 class="m-0">
            Incentive Sales
        </h6>
        <button
            v-if="$can('edit', 'Sale', saleModel) && !isSaleAdjusted && !isSaleClosed"
            class="btn btn-sm btn-outline-secondary box ml-auto cursor-pointer"
            @click="open"
        >
            <PlusIcon
                class="w-4 h-4 mr-1.5"
            />
            Add incentive
        </button>
    </div>

    <div class="card mt-2">
        <div class="card-body p-5">
            <table
                class="sales-incentive-table"
            >
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Sell price</th>
                        <th>Net price</th>
                        <th>Ck</th>
                        <th>Profit</th>
                        <th>Type</th>
                        <th>Payment type</th>
                        <th v-if="!product">
                            Created
                        </th>
                        <th class="text-right">
                            Action
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(item, index) in incentiveSalesComputed"
                        :key="index"
                    >
                        <td>{{ item.id }}</td>
                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >{{ Number.formatMoney(item.product.sell_price) }}</span>
                            <InputLayoutWrapper
                                v-else
                                v-model="form.field.products.get(item.product.id).map.field.sell_price"
                            >
                                <FormInputNumberMask
                                    v-autowidth="{
                                        minWidth: '100%',
                                        maxWidth: '100%',
                                    }"
                                    :class="{
                                        'border border-theme-21': form.field.products.get(item.product.id).map.field.sell_price.errors.length
                                    }"
                                    class="inline-edit"
                                />
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >{{ Number.formatMoney(item.product.fare) }}</span>
                            <InputLayoutWrapper
                                v-else
                                v-model="form.field.products.get(item.product.id).map.field.fare"
                            >
                                <FormInputNumberMask
                                    v-autowidth="{
                                        minWidth: '100%',
                                        maxWidth: '100%',
                                    }"
                                    class="inline-edit"
                                />
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >{{ Number.formatMoney(item.product.check_payment) }}</span>
                            <div v-else class="flex gap-1 items-center">
                                <InputCheckbox
                                    :model-value="form.field.products.get(item.product.id).map.ckEnabled.value"
                                    class="--primary focus:ring-0"
                                    @update:model-value="form.field.products.get(item.product.id).map.toggleCk($event)"
                                />
                                <div class="w-[50px]">
                                    <InputLayoutWrapper
                                        v-model="form.field.products.get(item.product.id).map.field.check_payment"
                                    >
                                        <FormInputNumberMask
                                            v-autowidth="{
                                                minWidth: '100%',
                                                maxWidth: '100%',
                                            }"
                                            v-tooltip="{content: form.field.products.get(item.product.id).map.getCkTooltipContent()}"
                                            class="inline-edit"
                                            :class="{
                                                'warning': form.field.products.get(item.product.id).map.isCkManualWarning()
                                            }"
                                            :readonly="!form.field.products.get(item.product.id).map.ckEnabled.value"
                                        />
                                    </InputLayoutWrapper>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >{{ Number.formatMoney(item.product.profit) }}</span>
                            <template
                                v-else
                            >
                                {{ Number.formatMoney(profit(item)) }}
                            </template>
                        </td>
                        <td>
                            <span
                                v-if="product !== item.product.id"
                            >
                                <span
                                    v-if="item.product.type === 'Insurance' && getInsuranceTypeInfo(item.product.sub_type)"
                                    class="flex gap-1"
                                >
                                    {{
                                        getInsuranceTypeInfo(item.product.sub_type)?.group === SaleInsuranceTypeGroup.BaggageProtection
                                            ? 'Baggage Protection'
                                            : 'TP'
                                    }}
                                    <span class="flex gap-1 items-center">
                                        <span v-if="getInsuranceTypeInfo(item.product.sub_type)?.titleShort">
                                            ({{ getInsuranceTypeInfo(item.product.sub_type)?.titleShort }})
                                        </span>

                                        <span
                                            v-tooltip="'Read terms and conditions'"
                                            class="text-gray-500 cursor-pointer"
                                            @click="openSubTypeInfo(item.product.sub_type)"
                                        >
                                            <InfoIcon class="icon --small" />
                                        </span>
                                    </span>
                                </span>
                                <span v-else>{{ item.product.type }}</span>
                            </span>
                            <InputLayoutWrapper
                                v-else
                                v-model="form.field.products.get(item.product.id).map.field.type"
                            >
                                <FormSelectGroups
                                    :class="{
                                        'border border-theme-21': form.field.products.get(item.product.id).map.field.type.errors.length
                                    }"
                                    class="form-multiselect-sm w-32"
                                />
                            </InputLayoutWrapper>
                        </td>
                        <td>
                            <span v-if="product !== item.product.id">
                                {{ getPayTypeLabel(item) }}
                            </span>
                            <InputLayoutWrapper
                                v-else-if="form.field.products.get(item.product.id).map.cardIdentity"
                                v-model="form.field.products.get(item.product.id).map.cardIdentity"
                            >
                                <FormSelectGroups
                                    :class="{
                                        'border-theme-21': form.field.products.get(item.product.id).map.cardIdentity.errors.length
                                    }"
                                    class="form-multiselect-sm w-32"
                                    @enter-value="(value) => {
                                        if (value === 'comVCC-') {
                                            showVccSelectModal(item.product.id)
                                        }
                                    }"
                                />
                            </InputLayoutWrapper>
                        </td>
                        <td v-if="!product" class="max-w-[140px]">
                            <span
                                v-tooltip="{content: `${item?.product?.createdBy?.first_name} ${item?.product?.createdBy?.last_name}`}"
                                class="max-w-[140px] truncate pr-2"
                            >
                                {{ item.product.createdBy?.first_name }} {{ item.product.createdBy?.last_name }}
                            </span>
                            <br>
                            <span class="max-w-[140px] truncate">
                                {{
                                    Date.fromUnixTimestamp(item.product.created_at).toFormatReactive('dd MMM yy, HH:mm')
                                }}
                            </span>
                        </td>
                        <td align="right">
                            <template
                                v-if="product !== item.product.id"
                            >
                                <ChatOpenButton
                                    v-if="saleModel.generalChatRoom?.id"
                                    v-slot="{messagesCount, hasNewMessages}"
                                    :branch="item.product.chatBranchName"
                                    :chat-room="saleModel.generalChatRoom"
                                    class="relative items-center p-1 -my-1 hover:bg-gray-200 rounded"
                                >
                                    <!--                                    :chat-id="saleModel.generalChatRoom?.id"-->
                                    <!--                                    :ringcentral-chat-id="saleModel.generalChatRoom?.ringcentral_id"-->
                                    <MessageCircleIcon class="w-4 h-4" />
                                    <div
                                        v-if="messagesCount"
                                        class="sales-ticket-badge top-0 right-0"
                                        :class="[hasNewMessages ? 'bg-orange-400 text-white' : 'bg-slate-200 text-gray-700']"
                                    >
                                        {{ messagesCount }}
                                    </div>
                                </ChatOpenButton>
                                <button
                                    v-if="$can('edit', 'Sale', saleModel) && !isSaleAdjusted && !isSaleClosed"
                                    class="px-1"
                                    @click="editProduct(item)"
                                >
                                    <EditIcon class="w-4 h-4" />
                                </button>
                                <button
                                    v-if="$can('edit', 'Sale', saleModel) && !isSaleAdjusted && !isSaleClosed"
                                    class="text-red-500 px-1 ml-1"
                                    @click="removeProduct(item)"
                                >
                                    <Trash2Icon class="w-4 h-4" />
                                </button>
                            </template>
                            <template
                                v-else
                            >
                                <button
                                    class="px-1"
                                    @click="saveProduct(item)"
                                >
                                    <CheckIcon class="w-4 h-4 !stroke-2 text-theme-20" />
                                </button>
                                <button
                                    class="text-red-500 px-1 ml-1"
                                    @click="cancelProduct(item)"
                                >
                                    <XIcon class="w-4 h-4" />
                                </button>
                            </template>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="text-primary-1">
                        <td colspan="1" />
                        <td>{{ Number.formatMoney(totalsComputed.sellPrice) }}</td>
                        <td>{{ Number.formatMoney(totalsComputed.netPrice) }}</td>
                        <td>{{ Number.formatMoney(totalsComputed.checkPayment) }}</td>
                        <td>{{ Number.formatMoney(totalsComputed.profit) }}</td>
                        <td colspan="4" />
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</template>

<script lang="ts">
import TransactionService from '@/lib/service/TransactionService'
import ChildMixin from '@/lib/mixin/ComponentRelation/ChildMixin'
import TagWrapperMixin from '@/lib/mixin/TagWrapperMixin'
import InputLayoutWrapper from '@/components/Form/FormWrappers/InputLayoutWrapper.vue'
import FormInputNumberMask from '@/components/Form/FormField/FormInputNumberMask.vue'
import ChatOpenButton from '@/modules/chat/components/ChatOpenButton.vue'
import SaleIncentiveModal from '@/views/sales/sale/modals/SaleIncentiveModal.vue'
import FormSelectGroups from '@/components/Form/FormField/FormSelectGroups.vue'
import FormHelper from '@/lib/core/helper/FormHelper'
import ProductFormField from '@/lib/FormField/Models/ProductFormField'
import ArrayMapField from '@/lib/FormField/ArrayMapField'
import { SaleInsuranceType, SaleInsuranceTypeGroup } from '~/api/dictionaries/Static/Sale/SaleInsuranceTypeDictionary'
import VCCSelectModal from '~/sections/Card/VirtualCard/modals/VCCSelectModal.vue'
import InputCheckbox from '~/components/Input/InputCheckbox.vue'
import { ProjectCardCategory } from '~/api/models/Card/ProjectCard'
import InputField from '@/lib/FormField/InputField'

export default defineComponent({
    name: 'IncentiveSalesBlock',
    components: {
        FormSelectGroups,
        ChatOpenButton,
        FormInputNumberMask,
        InputLayoutWrapper,
        InputCheckbox,
    },

    parentName: ['SaleComponent'],
    mixins: [TagWrapperMixin, ChildMixin],
    props: {
        salePk: String,
    },

    async setup(props) {
        const vccSelectModal = useModal(VCCSelectModal)

        const { useDictionary, record: sale } = await useNewContext('Sale', props.salePk)

        const projectInfo = useDictionary('Project').find(sale.project_pk)!

        return {
            vccSelectModal,
            SaleInsuranceType,
            projectInfo,
        }
    },

    data() {
        return {
            product: null,
            form: new FormHelper({
                products: new ArrayMapField(
                    new ProductFormField(),
                    [],
                    (data, number) => {
                        return data.id
                    }),
            }),
        }
    },

    computed: {
        SaleInsuranceTypeGroup() {
            return SaleInsuranceTypeGroup
        },

        sale() {
            return this.$related.SaleComponent.sale
        },

        incentiveSales() {
            return this.$related.SaleComponent?.incentiveSales
        },

        incentiveSalesComputed() {
            return this.incentiveSales.records
        },

        saleHelper() {
            return this.$related.SaleComponent.saleHelper
        },

        saleModel() {
            return this.$related.SaleComponent.saleModel
        },

        creditCards() {
            return this.$related.SaleComponent.creditCardData.records
        },

        virtualCards() {
            return this.$related.SaleComponent.virtualCards
        },

        saleVersionID() {
            return this.$related.SaleComponent?.saleVersion?.id
        },

        saleVersionsComputed() {
            return this.$related.SaleComponent?.saleVersion
        },

        totalsComputed() {
            return this.saleHelper.saleSummaryData?.incentive || {}
        },

        isSaleAdjusted() {
            return this.saleHelper?.isSaleAdjusted
        },

        isSaleClosed() {
            return this.saleVersionsComputed?.is_sale_closed || !this.saleVersionsComputed?.is_active
        },
    },

    watch: {
        incentiveSalesComputed: {
            deep: true,
            immediate: true,
            handler(val) {
                if (val.length) {
                    val.forEach(incentive => {
                        const productForm = this.form.field.products.addItem(incentive.product, true)

                        if (productForm?.map.field.type.value === 'Insurance' && incentive.product.sub_type) {
                            productForm.map.field.type.silentValue = incentive.product.sub_type
                        }
                    })

                    this.seedCardsInForm()
                }
            },
        },

        creditCards: {
            deep: true,
            immediate: true,
            handler(val) {
                if (this.creditCards && this.form.field.products.items) {
                    this.seedCardsInForm()
                }
            },
        },

        virtualCards: {
            deep: true,
            immediate: true,
            handler(val) {
                if (this.virtualCards && this.form.field.products.items) {
                    this.seedCardsInForm()
                }
            },
        },
    },

    methods: {
        seedCardsInForm() {
            this.form.field.products.items.forEach(product => {
                product.map.init()
                product.map.setCreditCards(this.creditCards)
                product.map.setInhouseVCC(this.virtualCards, this.canEditPayments)
            })
        },

        checkSaleAdjusted() {
            if (this.isSaleAdjusted) {
                this.toastError('Sale adjusted!')

                return true
            }

            return false
        },

        profit(incentiveSale) {
            const form = this.form.field.products.get(incentiveSale.product.id).map.value

            return TransactionService.calcProductTotals(form).profit
        },

        editProduct(item) {
            if (this.checkSaleAdjusted()) {
                return
            }

            this.product = item.product.id
        },

        saveProduct(incentiveSale) {
            if (this.form.validate()) {
                incentiveSale = JSON.parse(JSON.stringify(incentiveSale))
                const pk = incentiveSale.id
                const product = this.form.field.products.get(incentiveSale.product.id).map.value
                incentiveSale.product = product
                delete incentiveSale.chatRoom
                delete incentiveSale.id
                delete incentiveSale.sale_version_id
                delete incentiveSale.created_at
                this.$models.IncentiveSaleModel.update(pk, incentiveSale).then(apiResponse => {
                    this.incentiveSales.updateRecord(apiResponse.result)
                    this.product = null
                }).catch(apiError => {
                    apiError.processWithMessage()
                })
            }
        },

        cancelProduct(incentiveSale) {
            if (incentiveSale) {
                this.form.field.products.get(incentiveSale.product.id).map.$reset()
            }
            this.product = null
        },

        removeProduct(item) {
            if (this.checkSaleAdjusted())
                return

            this.$confirmDelete().then(() => {
                this.$models.IncentiveSaleModel.remove(item.id)
                    .then(() => {
                        this.incentiveSales.refresh()
                    })
                    .catch(apiError => {
                        apiError.processWithMessage('Something went wrong. Please try again.')
                    })
            })
        },

        async open() {
            if (this.checkSaleAdjusted()) {
                return
            }

            const saleIncentiveModal = useModal(SaleIncentiveModal)
            try {
                const resolve = await saleIncentiveModal.open({
                    saleVersion: this.saleVersionsComputed,
                    sale: this.saleModel,
                    creditCards: this.creditCards,
                    virtualCards: this.virtualCards,
                    saleHelper: this.saleHelper,
                })
                this.incentiveSales.addRecord(resolve.result)
            } catch (e) {

            }
        },

        openChatRoom(id) {
            this.$related.SaleComponent.openChatRoom(id)
        },

        async showVccSelectModal(productId) {
            this.vccSelectModal.open({
                salePk: String(this.saleModel.id),
            }).then((card_pk) => {
                this.saleHelper.fetchVirtualCards()
                ///
                this.form.field.products.get(productId).map.cardIdentity.value = `${ProjectCardCategory.ProjectVirtualCard}-${card_pk}`
            }).catch(payload => {
                this.form.field.products.get(productId).map.cardIdentity.silentValue = ''
                this.form.field.products.get(productId).map.field.card_identity.value = ''
            })
        },

        getPayTypeLabel(item) {
            if (this.form.field.products.get(item.product.id)) {
                return this.form.field.products.get(item.product.id).map.getCCPaymentTypeText(item)
            }

            return ''
        },

        async openSubTypeInfo(subType: SaleInsuranceType) {
            const paySiteOrigin = '//' + this.projectInfo.pay_host

            let link: string | undefined

            if (subType === SaleInsuranceType.BaggageProtection) {
                link = `${paySiteOrigin}/protection-terms/baggage`
            } else if (subType === SaleInsuranceType.TicketProtectionPlan1) {
                link = `${paySiteOrigin}/protection-terms/plan1`
            } else if (subType === SaleInsuranceType.TicketProtectionPlan2) {
                link = `${paySiteOrigin}/protection-terms/plan2`
            } else if (subType === SaleInsuranceType.TicketProtectionPlan3) {
                link = `${paySiteOrigin}/protection-terms/plan3`
            }

            if (link) {
                window.open(link, '_blank')
            }
        },

        getInsuranceTypeInfo(subType: SaleInsuranceType) {
            return useGeneralDictionary('SaleInsuranceType').records.find((record) => record.id === subType)
        },
    },
})
</script>

