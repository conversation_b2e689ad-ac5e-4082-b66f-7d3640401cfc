<template>
    <div
        v-if="$projectId"
        :data-project-id="$projectId"
        :style="{'background-color': color}"
        class="rounded text-3xs w-[fit-content] -mx-2 text-white font-semibold px-1.5 leading-normal self-center"
    >
        <span v-if="short">{{ getProjectInfo($projectId)?.project_abbr }}</span>
        <span v-else>{{ getProjectFullName($projectId) }}</span>
    </div>
</template>

<script lang="ts">
import { getCompanyColorByProject, getProjectFullName, getProjectInfo } from '@/lib/core/helper/WorkspaceProjectHelper'

export default defineComponent({
    name: 'WorkspaceLabelComponentAlternative',

    props: {
        projectPk: {
            type: String,
            default: null,
        },

        projectId: {
            type: Number,
            default: null,
        },

        short: {
            type: Boolean,
            default: true,
        },
    },

    computed: {
        $projectId(): number {
            return this.projectPk ? Number(this.projectPk) : this.projectId
        },

        color() {
            return getCompanyColorByProject(this.$projectId)
        },
    },

    methods: {
        getProjectFullName,
        getProjectInfo,
    },
})
</script>
