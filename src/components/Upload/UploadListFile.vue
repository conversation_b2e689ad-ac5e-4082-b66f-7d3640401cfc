<template>
    <div
        class="upload-list__file"
        :class="{'upload-list__file--box': box}"
    >
        <div class="upload-list__file__icon">
            <img
                :src="getExtensionImage(file.extension)"
                class="upload-list__file__icon__image"
                alt=""
            >
        </div>
        <div class="upload-list__file__info">
            <div class="upload-list__file__title pr-9">
                <div
                    class="upload-list__file__name"
                    :title="file.name"
                >
                    {{ file.basename }}
                </div>
                <div
                    v-if="file.extension"
                    class="upload-list__file__type"
                >
                    .{{ file.extension }}
                </div>
            </div>
            <div
                v-if="file.progress !== null && file.progress < 100"
                class="upload-list__file__progress"
            >
                <div class="upload-list__file__progress__percent">
                    {{ file.progress }}%
                </div>
                <div
                    class="upload-list__file__progress__loader"
                    :style="`width: ${file.progress}%`"
                />
            </div>
            <div
                v-else-if="file.errors.length"
                class="upload-list__file__data"
            >
                <span class="text-red-500">{{ file.errors[0] }}</span>
            </div>
            <div
                v-else-if="file.progress === null"
                class="upload-list__file__data"
            >
                <span class="text-gray-500">Queued</span>
            </div>
            <div
                v-else-if="!file.errors.length"
                class="upload-list__file__data"
            >
                <span class="text-green-500">Uploaded</span>
            </div>
        </div>
        <div class="upload-list__file__action">
            <button
                v-if="canPreviewFile && !file.errors.length && file.progress === 100"
                class="p-1 hover:scale-125 transition-transform"
                type="button"
                @click="previewFile()"
            >
                <EyeIcon
                    class="w-3.5 h-3.5 !stroke-2"
                />
            </button>
            <button
                v-if="canDownload && !file.errors.length && file.progress === 100"
                class="p-1 hover:scale-125 transition-transform"
                type="button"
                @click="download()"
            >
                <DownloadIcon
                    class="w-3.5 h-3.5 !stroke-2"
                />
            </button>

            <button
                class="p-1 hover:scale-125 transition-transform"
                type="button"
                @click="$emit('cancel')"
            >
                <TrashIcon
                    class="w-3.5 h-3.5 !stroke-2"
                />
            </button>
        </div>
    </div>
</template>

<script>
import { getExtensionImage } from '@/modules/chat/lib/Helpers/ChatHelpers'
import FileData from '@/lib/core/helper/File/FileData'
import { download } from '@/lib/core/helper/File/FileHelpers'
import FilePreviewerModal from '~/components/File/Previewer/FilePreviewerModal.vue'

export default {
    name: 'UploadListFile',

    props: {
        file: {
            type: [Object, FileData],
            required: true,
        },

        box: {
            type: Boolean,
            default: false,
        },

        canPreview: {
            type: Boolean,
            default: false,
        },

        canDownload: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['cancel'],

    computed: {
        canPreviewFile() {
            const valid_ext = ['png', 'jpg', 'jpeg'].includes(this.file.extension)

            return this.canPreview && valid_ext
        },
    },

    methods: {
        getExtensionImage,

        previewFile() {
            this.$showModal(FilePreviewerModal, {}, {
                file: this.file.file_url,
            })
        },

        download() {
            download(this.file)
        },
    },
}
</script>
