<template>
    <AppModalWrapper
        header="Add option"
        close-button
        :wide="isWideModal"
        class="app-modal__container--price-quote"
        :class="{
            'app-modal__container--price-quote-wide': isWideModal
        }"
    >
        <div class="p-7">
            <ParseSegmentsBlock
                v-if="step === 1"
            />
            <div
                v-else
                class="flex h-full"
            >
                <PreviewSegmentsBlock
                    v-if="step >= 2"
                    :key="updatedSecondStepComputed"
                />
                <PreTicketBlock
                    v-if="step === 3"
                    :key="updatedSecondStepComputed"
                />
            </div>
        </div>
        <template #footer>
            <div class="flex justify-end">
                <AppModalButton
                    v-if="step === 1"
                    class="btn-outline-secondary mr-4"
                    type="button"
                    @click="$emit('close')"
                >
                    Cancel
                </AppModalButton>

                <AppModalButton
                    v-if="step === 2"
                    class="btn-outline-secondary mr-4"
                    type="button"
                    @click="prevStep()"
                >
                    Edit PQ
                </AppModalButton>

                <AppModalButton
                    v-if="step === 3 && mode === 'clone'"
                    class="btn-outline-secondary mr-4"
                    type="button"
                    @click="prevStep()"
                >
                    Back
                </AppModalButton>

                <AppModalButton v-if="btnPress">
                    <LoaderIcon class="animate-spin h-4 w-4" />
                </AppModalButton>
                <AppModalButton
                    v-else
                    v-tooltip="(step === 3 && firstFormError ) ? {content: firstFormError} : false"
                    class="btn-primary"
                    @click="step < maxSteps ? nextStep() : save()"
                >
                    {{ mode === 'clone' ? step < maxSteps ? 'Next' : 'Save' : 'Update PQ' }}
                </AppModalButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script lang="ts">
import PriceQuoteHelper from '@/lib/core/helper/PriceQuote/PriceQuoteHelper'
import ParseSegmentsBlock from '@/components/Modals/priceQuote/blocks/ParseSegmentsBlock.vue'
import PreviewSegmentsBlock from '@/components/Modals/priceQuote/blocks/PreviewSegmentsBlock.vue'
import PreTicketBlock from '@/components/Modals/priceQuote/blocks/PreTicketBlock.vue'
import WorkspaceMixin from '@/lib/mixin/WorkspaceMixin'
import ChildTypedMixin from '@/lib/mixin/ComponentRelation/ChildTypedMixin'
import ParentMixin from '@/lib/mixin/ComponentRelation/ParentMixin'
import { wait } from '~/lib/Helper/PromiseHelper'
import { toastWarning } from '@/lib/core/helper/ToastHelper'

export default defineComponent({
    name: 'PriceQuoteModal',
    modal: {
        promise: true,
        closeOnOverlay: false,
    },

    components: {
        PreTicketBlock,
        PreviewSegmentsBlock,
        ParseSegmentsBlock,
    },

    type: 'PriceQuoteComponent',
    parentType: 'HasSegmentEditor',
    mixins: [WorkspaceMixin, ChildTypedMixin, ParentMixin],
    props: {
        id: {
            type: Number,
            required: false,
            // default: null,
        },

        mode: {
            type: String,
            required: false,
            default: 'clone', // edit
        },

        lead_id: {
            type: Number,
            required: false,
            // default: null,
        },

        sale_id: {
            type: Number,
            required: false,
            // default: null,
        },

        sale: {
            type: String,
            require: false,
            // default: '',
        },

        prepopulateFrom: {
            type: Object as PropType<{
                option: string,
                carrier_code?: string,
                consolidator_area_name?: string,
                fare_amount?: number,
                net_price?: number,
                commission?: number,
            }>,
        },
    },

    emits: ['close', 'resolve', 'reject'],

    setup() {
        const { useModel } = useContext()
        const airlineModel = useModel('Airline')
        const consolidatorAreaModel = useModel('ConsolidatorArea')

        return {
            airlineModel,
            consolidatorAreaModel,
        }
    },

    data() {
        return {
            priceQuote: new PriceQuoteHelper({
                mode: this.mode,
            }),

            updated: 0,
            updateSecondStep: 0,
            step: 1,
            maxSteps: 3,
            segmentsTextChange: false,

            validationTextErrors: false,
            safeOneTktOldValue: {},
            safeMultiTktPreTicket: {},

            btnPress: false,
        }
    },

    computed: {
        model() {
            return {
                id: this.id,
                lead_id: this.lead_id,
                sale_id: this.sale_id,
                sale: this.sale,
            }
        },

        updatedComputed: {
            get() {
                return this.updated
            },

            set(v) {
                this.updated = Date.now()
            },
        },

        updatedSecondStepComputed: {
            get() {
                return this.updateSecondStep
            },

            set(v) {
                this.updateSecondStep = Date.now()
            },
        },

        formData() {
            return this.priceQuote.form.data.raw_segments
        },

        formDataSegments() {
            return this.priceQuote.form.data.segments
        },

        allFormData() {
            return this.priceQuote.form.data
        },

        isWideModal() {
            return this.step === 3
        },

        firstFormError() {
            const formErrors = this.priceQuote.form.errorSummary[0]
            const preTicketFormErrors = this.priceQuote.preTicketForm.errorSummary[0]

            return formErrors || preTicketFormErrors
        },
    },

    watch: {
        formData(val) {
            if (this.priceQuote.initialRawSegments !== val) {
                this.segmentsTextChange = true
            }
        },

        allFormData(val) {
            if (val) {
                this.validationTextErrors = false
            }
        },
    },

    mounted() {
        this.priceQuote.segmentTypes = useGeneralDictionary('SegmentType').mapRecords.forSelect()
        this.priceQuote.ticketTypes = useGeneralDictionary('TicketType').mapRecords.forSelect()

        setTimeout(async () => {
            this.priceQuote.$init(this.model)

            if (this.model?.id) {
                if (this.mode === 'edit') {
                    this.$models.PriceQuoteFormModel.editPq(this.model.id).then(apiResponse => {
                        this.priceQuote.$initAfterParseOrClone(apiResponse, this.model, false)
                        this.step = 3
                    })
                } else {
                    this.$models.PriceQuoteFormModel.clone(this.model.id).then(apiResponse => {
                        this.priceQuote.$initAfterParseOrClone(apiResponse, this.model, false)
                        this.step = 2
                    })
                }
            } else if (this.prepopulateFrom) {
                this.priceQuote.$info.mainForm.field.raw_segments.value = this.prepopulateFrom.option

                if (this.prepopulateFrom.carrier_code) {
                    const airline = (await this.airlineModel.useRecord({ where: (and) => and.eq('code', this.prepopulateFrom.carrier_code) }).fetch()).value

                    if (airline) {
                        this.priceQuote.$info.mainForm.field.validating_carrier_id.value = Number(usePk(airline))
                    } else {
                        this.toastWarning(`Can't find submitted PCC`)
                    }
                }

                if (this.prepopulateFrom.fare_amount || this.prepopulateFrom.net_price || this.prepopulateFrom.commission || this.prepopulateFrom.consolidator_area_name) {
                    await this.nextStep()

                    await this.nextStep()

                    const preTicket = this.priceQuote.preTicket()

                    if (!preTicket) {
                        return
                    }

                    if (this.prepopulateFrom.consolidator_area_name) {
                        const consolidatorAreaRecord = this.consolidatorAreaModel.useRecord({ where: (and) => and.eq('name', this.prepopulateFrom.consolidator_area_name) })
                        await consolidatorAreaRecord.fetch()

                        if (consolidatorAreaRecord.record) {
                            preTicket.consolidator_area_id.value = usePk(consolidatorAreaRecord.record.value)
                        } else {
                            toastWarning('Consolidator area not found')
                        }
                    }

                    const prices = preTicket.prices?.items ?? []

                    if (prices.length) {
                        const adultPrices = prices.find((item) => item.$identity === 'adult')

                        if (adultPrices) {
                            adultPrices.fare_amount.value = this.prepopulateFrom.fare_amount
                            adultPrices.net_price.value = this.prepopulateFrom.net_price
                            adultPrices.commission.value = this.prepopulateFrom.commission
                        }
                    }
                }
            } else {
                this.step = this.model?.id ? 2 : 1
            }
        }, 500)
    },

    methods: {
        closeWithResolve(priceQuote) {
            this.$emit('resolve', priceQuote)
        },

        async nextStep() {
            if (this.step === 1) {
                this.updatedSecondStepComputed = 1
                this.priceQuote.$init(this.model)

                if (!this.priceQuote.form.field.raw_segments.value || !this.priceQuote.form.field.validating_carrier_id.value) {
                    this.priceQuote.form.validate()
                } else {
                    if (this.segmentsTextChange && this.priceQuote.lead_id) {
                        try {
                            const apiResponse = await this.$models.PriceQuoteFormModel.parseSegments(this.priceQuote.form.field.raw_segments.value, this.priceQuote.lead_id)
                            this.priceQuote.$initAfterParseOrClone(apiResponse, this.model)

                            await wait(100)
                            this.step = 2
                        } catch (apiError) {
                            if (apiError.name === 'ApiErrorForm') {
                                if (apiError.data.segmentText) {
                                    this.priceQuote.form.field.raw_segments.addError(apiError.data.segmentText)
                                    apiError.processWithMessage('Segments data is invalid')
                                }
                            }
                        }
                    } else {
                        this.step = 2
                    }
                }
            } else if (this.step < this.maxSteps) {
                this.step += 1
            }
        },

        prevStep() {
            if (this.step > 1) {
                this.step -= 1
            }
        },

        save() {
            if (!this.priceQuote.isCanSave) {
                this.validationTextErrors = true

                return
            }
            this.btnPress = true

            const data = this.priceQuote.$getData()

            if (this.priceQuote.isSale) {
                this.$models.SaleModel.createNewVersion(this.priceQuote.sale_id, data)
                    .then(apiResponse => {
                        this.btnPress = false
                        this.closeWithResolve(apiResponse.result)
                        this.validationTextErrors = false
                    })
                    .catch(apiError => {
                        this.btnPress = false
                        this.priceQuote.form.processApiErrors(apiError)
                        this.validationTextErrors = true
                        apiError.processed = true
                        apiError.processWithMessage(apiError.message)
                    })
            } else {
                if (this.mode === 'edit') {
                    this.$models.PriceQuoteFormModel.update(this.model.id, data)
                        .then(apiResponse => {
                            this.btnPress = false
                            this.closeWithResolve(apiResponse.result)
                            this.validationTextErrors = false
                        })
                        .catch(apiError => {
                            apiError.processWithMessage(apiError.message)
                            this.btnPress = false
                            this.priceQuote.form.processApiErrors(apiError)
                            this.validationTextErrors = true
                            apiError.processed = true
                        })
                } else {
                    this.$models.PriceQuoteFormModel.insert(data)
                        .then(apiResponse => {
                            this.btnPress = false
                            this.closeWithResolve(apiResponse.result)
                            this.validationTextErrors = false
                        })
                        .catch(apiError => {
                            apiError.processWithMessage(apiError.message)
                            this.btnPress = false
                            this.priceQuote.form.processApiErrors(apiError)
                            this.validationTextErrors = true
                            apiError.processed = true
                        })
                }
            }
        },
    },
})
</script>
