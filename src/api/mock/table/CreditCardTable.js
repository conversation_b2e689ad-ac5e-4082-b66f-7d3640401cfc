import FakeTable from '@/api/mock/lib/FakeTable'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import { faker } from '@faker-js/faker'
import fakeDB from "@/api/mock/fakeDB"
import FakeHelper from "@/api/mock/lib/FakeHelper"

class CreditCardRecord extends FakeRecord {
    id = null
    holder_first_name = null
    holder_last_name = null
    strip = null
    expires_at = null
    client_id = null
}

export default class CreditCardTable extends FakeTable {
    get recordModel() {
        return CreditCardRecord
    }

    constructor() {
        super('creditCards', [])
    }

    /**
     * @param client {ClientRecord}
     * @return {CreditCardRecord}
     */
    buildNew(client) {
        // const strip = faker.finance.creditCardNumber('visa')
        const strip = FakeHelper.randomBool() ? '****************' : '****************'

        const data = {
            holder_first_name: client.first_name,
            holder_last_name: client.first_name,
            strip: strip,
            expires_at: FakeTable.currentTimeStamp() + 100000,
            client_id: client.id,
        }

        return this.insert(data)
    }
}
