import '@/lib/core/JsMutations/Date'
import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import { faker } from '@faker-js/faker'
import FakeHelper from '@/api/mock/lib/FakeHelper'

class AgentDeviceRecord extends FakeRecord {
    id = null
    agent_id = null
    created_at = null
    last_login_at = null
    place = null
    is_approved = null
    device_type = null
    device_os = null
    device_browser = null
    ip = null
    approved_by = null
    is_new = null

    /**
     * @param table {AgentDeviceTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('is_approved', 0)
        this.$setDefaultValue('is_new', 1)
        this.$setDefaultValue('created_at', FakeTable.currentTimeStamp())
    }

    /**
     *
     * @return {AgentRecord}
     */
    get approvedBy() {
        return fakeDB.agents.findByPK(this.approved_by)
    }

    /**
     *
     * @return {AgentRecord}
     */
    get agent() {
        return fakeDB.agents.findByPK(this.agent_id)
    }
}

/**
 * @property
 */
export default class AgentDeviceTable extends FakeTable {
    /**
     * @return {AgentDeviceRecord[]}
     */
    get records() {
        return this._records
    }

    /**
     * @return {typeof AgentDeviceRecord}
     */
    get recordModel() {
        return AgentDeviceRecord
    }

    constructor() {
        super('agentDevices', [])
    }

    static randomOS() {
        return  [
            'Windows',
            'Linux',
            'Android',
            'macOS',
        ][Math.floor(Math.random() * 4)]
    }

    static randomBrowser() {
        return  [
            'Opera',
            'Firefox',
            'Safari',
            'Internet Explorer',
            'Edge',
            'Chrome',
            'Blink',
        ][Math.floor(Math.random() * 7)]
    }

    static randomDevice() {
        return  [
            'mobile',
            'tablet',
            'pc',
        ][Math.floor(Math.random() * 3)]
    }

    addToday(agent_id) {
        const date = this.randomDateHours(-3, 3, new Date())
        const isApprove = FakeHelper.randomBool()
        const data = {
            agent_id: agent_id,
            last_login_at: date.unixTimestamp(),
            place: `${faker.address.countryCode()}, ${faker.address.city()}`,
            device_type: this.constructor.randomDevice(),
            device_os: this.constructor.randomOS(),
            device_browser: this.constructor.randomBrowser(),
            ip: faker.internet.ip(),
            is_new: isApprove ? 0 : 1,
            approved_by: isApprove ? 1 : null,
            is_approved: isApprove ? 1 : 0,
        }

        return this.insert(data)
    }

    generateData() {
        fakeDB.agents.records.forEach(agent => {
            const daysCount = 2
            for (let i = 0; i < daysCount; i++) {
                const day = new Date()
                day.setDate(day.getDate() - (daysCount - i))
                day.setHours(0)
                day.setMinutes(0)

                const date = this.randomDateHours(0, +5, day)
                const isApprove = FakeHelper.randomBool()
                const data = {
                    agent_id: agent.id,
                    last_login_at: date.unixTimestamp(),
                    place: `${faker.address.countryCode()}, ${faker.address.city()}`,
                    device_type: this.constructor.randomDevice(),
                    device_os: this.constructor.randomOS(),
                    device_browser: this.constructor.randomBrowser(),
                    ip: faker.internet.ip(),
                    is_new: isApprove ? 0 : 1,
                    approved_by: isApprove ? 1 : null,
                    is_approved: isApprove ? 1 : 0,
                }

                this.insert(data)

                const nexLogin = this.randomDateHours(3, 5, date)
                data.last_login_at = nexLogin.unixTimestamp()
                this.insert(data)
            }
        })
    }
}
