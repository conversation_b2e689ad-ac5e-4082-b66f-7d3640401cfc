import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import { faker } from '@faker-js/faker'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import FakeHelper from "@/api/mock/lib/FakeHelper"

class FileRecord extends FakeRecord {
    id = null

    file_name = null
    file_original_name = null
    file_url = null
    file_type_system_name = null
    file_extension = null
    file_size = null

    created_at = null
    updated_at = null

    created_by = null
    updated_by = null

    file_web_url = null

    /**
     * @param table {FileTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('created_at', FakeTable.currentTimeStamp())
        this.$setDefaultValue('updated_at', FakeTable.currentTimeStamp())
        this.$setDefaultValue('file_size', 0)
        this.$setDefaultValue('file_original_name', `${this.file_name}.${this.file_extension}`)
    }

    /**
     * @return {AgentRecord|null}
     */
    get createdBy() {
        return fakeDB.agents.findByPK(this.created_by)
    }

    /**
     * @return {AgentRecord|null}
     */
    get updatedBy() {
        return fakeDB.agents.findByPK(this.updated_by)
    }

    get publicUrl() {
        return `https://${window.location.hostname}/file?id=${this.id}`
    }
}

export default class FileTable extends FakeTable {
    get recordModel() {
        return FileRecord
    }

    constructor() {
        super('files', [])
    }

    /**
     * @param user_id
     * @param fileName
     * @param fileUrl
     * @return {FileRecord}
     */
    addTmpFile(user_id, fileName, fileUrl, size) {
        const file_extension = fileName.split('.').pop().toLowerCase()
        const file = this.insert({
            file_name: fileName.replace(`.${file_extension}`, ''),
            file_extension: file_extension,
            file_url: fileUrl,
            file_size: size,
            file_type_system_name: 'tmp',
            created_by: user_id,
            updated_by: user_id,
        })

        // file.file_web_url = `https://some-awesome-url/file?id=${file.id}`
        // this.save(file)
        return file
    }

    randomAva() {
        const len = FakeHelper.randomMaxMinInt(1, 70)

        return `https://i.pravatar.cc/100?img=${len}`
    }

    generateAva(agent_id, user_id) {
        return this.insert({
            file_name: `ava_for_${agent_id}`,
            file_extension: `png`,
            file_url: this.randomAva(),
            file_type_system_name: 'agent_avatar',

            created_by: user_id,
            updated_by: user_id,
        })
    }

    generateNewAva(user_id) {
        return this.insert({
            file_name: `ava_for_${faker.name.firstName()}_${Date.now()}`,
            file_extension: `png`,
            file_url: this.randomAva(),
            file_type_system_name: 'tmp',

            created_by: user_id,
            updated_by: user_id,
        })
    }

    generateInvoice(user_id) {
        return this.insert({
            file_name: `invoice`,
            file_extension: `pdf`,
            file_url: 'invoice.pdf',
            file_type_system_name: 'tmp',

            created_by: user_id,
            updated_by: user_id,
        })
    }
}
