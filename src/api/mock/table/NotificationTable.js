import FakeTable from '@/api/mock/lib/FakeTable'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import fakeDB from '@/api/mock/fakeDB'
import { faker } from '@faker-js/faker'

const random = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1) + min)
}

const chance = (percent) => {
    return random(0, 100) <= percent
}

class NotificationRecord extends FakeRecord {
    id = null
    message = null
    type = null
    category_name = null
    link = null
    need_push = null
    need_toast = null
    is_read = null
    created_at = null
    agent_id = null
    sender_id = null
    actions = []

    $setDefaultValues(table) {
        this.$setDefaultValue('type', 'info')
        this.$setDefaultValue('need_push', 0)
        this.$setDefaultValue('need_toast', 0)
        this.$setDefaultValue('is_read', 0)
    }

    /**
     * @return {AgentRecord|null}
     */
    get sender() {
        return fakeDB.agents.findByPK(this.sender_id)
    }

    get category() {
        return fakeDB.notificationTemplateCategories.find({ category_name: this.category_name })
    }
}

export default class NotificationTable extends FakeTable {
    get recordModel() {
        return NotificationRecord
    }

    constructor() {
        super('notifications')
    }

    generateData() {
        ([...Array(10).keys()]).forEach(i => {
            this.insert({
                id: random(0, 10000000),
                message: faker.lorem.text(1),
                type: [
                    'info',
                    'success',
                    'warning',
                    'danger',
                ][random(0, 3)],
                category_name: ['leads', 'sales', 'experts', 'systems'][random(0, 3)],
                link: [null, '#link'][random(0, 1)],
                need_push: [0, 1][random(0, 1)],
                need_toast: [0, 1][random(0, 1)],
                is_read: [0, 1][random(0, 1)],
                created_at: Date.currentUnixTimestamp() - random(0, 60 * 60 * 36),
                agent_id: chance(20) ? null : fakeDB.agents.randomRecord().id,
                sender_id: chance(20) ? null : fakeDB.agents.randomRecord().id,
                actions: chance(60) ? [] : [
                    {
                        text: 'Accept',
                        icon: 'check',
                        metadata: { lead_id: 80 },
                        type: 'success',
                    },
                    {
                        text: 'Deny',
                        icon: 'x',
                        metadata: '#deny',
                        type: 'secondary',
                    },
                ],
            })
        })
    }
}
