import FakeTable from '@/api/mock/lib/FakeTable'
import { faker } from '@faker-js/faker'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import fakeDB from '@/api/mock/fakeDB'
import FakeHelper from '@/api/mock/lib/FakeHelper'

class ClientRecord extends FakeRecord {
    id = null
    first_name = null
    last_name = null
    nickname = null
    source_id = null // external sources
    created_at = null
    curator_id = null
    _avatar_file_id = null
    status_id = null
    referral_client_id = null
    project_id = null
    client_cabinet_id = null

    get company_id() {
        return this.company.id
    }

    get company() {
        return fakeDB.companies.findByPK(this.project.company_id)
    }

    get project() {
        return fakeDB.projects.findByPK(this.project_id)
    }

    get avatar_file_id() {
        return this._avatar_file_id
    }

    set avatar_file_id(v) {
        if (v > 0) {
            const file = fakeDB.files.findByPK(Number(v))
            file.file_type_system_name = 'client_avatar'
            this._avatar_file_id = file.id
        } else {
            this._avatar_file_id = null
        }
    }

    // get keywords() {
    //     return `${this.first_name} ${this.last_name} ${this.defaultPhone} ${this.defaultEmail}`
    // }

    get isRepeat() {
        return fakeDB.leads.find({ client_id: this.id }).length > 1
    }

    get statusId() {
        return fakeDB.clientStatuses.find({ id: this.status_id })[0]
    }

    get referralClient() {
        return fakeDB.clients.find({ id: this.referral_client_id })[0]
    }

    get passportOwnCount() {
        return fakeDB.clientPassports.find({ client_id: this.id, is_own: 1 }).length
    }

    get passportsOtherCount() {
        return fakeDB.clientPassports.find({ client_id: this.id, is_own: 0 }).length
    }

    /**
     * @return {FileRecord|null}
     */
    get avatarFile() {
        return fakeDB.files.findByPK(this.avatar_file_id)
    }

    get avatar() {
        return this.avatarFile?.file_url
    }

    get fullName() {
        return `${this.first_name} ${this.last_name}`
    }

    // TODO: is it correct to display first element as default if there are not is_default:1 elements
    get defaultEmailRecord() {
        const emails = fakeDB.clientEmails.findOne({ client_id: this.id, is_default: 1 })

        return emails || fakeDB.clientEmails.find({ client_id: this.id })[0]
    }

    get defaultEmail() {
        return this.defaultEmailRecord.email
    }

    get pendingStatusId() {
        return 1
    }

    get searchEmail() {
        return fakeDB.clientEmails.find({ client_id: this.id }).map(record => record.email).join('|')
    }

    get defaultPhoneRecord() {
        const phones = fakeDB.clientPhones.findOne({ client_id: this.id, is_default: 1 })

        return phones || fakeDB.clientPhones.find({ client_id: this.id })[0]
    }

    get defaultPhone() {
        return this.defaultPhoneRecord.phone
    }

    get searchPhone() {
        return fakeDB.clientPhones.find({ client_id: this.id }).map(record => record.phone).join('|')
    }

    get lastActivity() {
        return new Date(Date.now()).unixTimestamp() - 60 * 60 * 24 * 10
    }

    get keywords() {
        return [this.searchPhone, this.searchEmail, this.first_name, this.last_name].join('|')
    }

    get sales() {
        return fakeDB.sales.find({ client_id: this.id })
    }

    get salesCount() {
        return this.sales.length
    }

    get leadsCount() {
        return fakeDB.leads.find({ client_id: this.id }).length
    }

    /**
     * @returns {LeadRecord}
     */
    get lastLead() {
        // TODO: temp
        return fakeDB.leads.findOne({ client_id: this.id })
    }

    get searchLastLeadId() {
        return this.lastLead.id ? this.lastLead.id : null
    }

    get curator() {
        return fakeDB.agents.findOne({ id: this.curator_id })
    }

    get curatorName() {
        return `${this.curator?.first_name} ${this.curator?.last_name}`
    }

    get source() {
        const bool = FakeHelper.randomBool(50)

        return {
            name: bool ? 'Revenue Web Site' : 'Award Web Site',
            system_name: bool ? 'revenue_web_site' : 'award_web_site',
        }
    }

    get sourceName() {
        return this.source.name
    }

    get lastGA() {
        return this.lastLead?.utm?.ga || null
    }

    $setDefaultValues(table) {
        this.$setDefaultValue('created_at', FakeTable.currentTimeStamp())
        this.$setDefaultValue('source_id', 1)
        this.$setDefaultValue('curator_id', 1)
    }

    $insertData(data, tabel) {
        const record = super.$insertData(data, tabel)

        if (data.client_email) {
            const email = fakeDB.clientEmails.buildNew(record.id)
            email.is_default = 1
        }

        if (data.client_phone) {
            const phone = fakeDB.clientPhones.buildNew(record.id)
            phone.is_default = 1
        }

        return record
    }
}

export default class ClientTable extends FakeTable {
    constructor() {
        super('clients', [])
    }

    get recordModel() {
        return ClientRecord
    }

    getRandomCurator() {
        try {
            const salesDepartment = fakeDB.departments.findOne({ system_name: 'sales' })
            const agent = fakeDB.agents.randomExecutor(salesDepartment.id, false)

            return agent.id
        } catch (e) {
            console.log('getRandomCurator', e)

            return 1
        }
    }

    getRandomProject() {
        try {
            const project = fakeDB.projects.randomRecord()

            return project.id
        } catch (e) {
            // console.log('getRandomProject', e)

            return 1
        }
    }

    /**
     * @return {ClientRecord}
     */
    generateNew() {
        const clientAva = fakeDB.files.generateNewAva(1)
        const newClient = this.insert({
            first_name: faker.name.firstName(),
            last_name: FakeHelper.randomBool() ? '' : faker.name.lastName(),
            nickname: faker.internet.userName(),
            avatar_file_id: clientAva.id,
            curator_id: this.getRandomCurator(),
            project_id: this.getRandomProject(),
            client_cabinet_id: FakeHelper.randomBool() ? '' : FakeHelper.randomMaxMinInt(1, 100),
        })

        fakeDB.clientPassports.generateRandomNew(newClient.id)
        fakeDB.clientEmails.buildNew(newClient.id)
        fakeDB.clientEmails.buildNew(newClient.id)
        fakeDB.clientPhones.buildNew(newClient.id)
        fakeDB.clientPhones.buildNew(newClient.id)
        fakeDB.clientPhones.buildNew(newClient.id)

        return newClient
    }

    generateEmail(client) {
        return `${client.nickname}@external.loc`
    }

    generatePhone(client) {
        return faker.phone.phoneNumber('+48 ## ### ## ##')
    }
}
