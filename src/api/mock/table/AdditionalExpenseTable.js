import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import { faker } from '@faker-js/faker'
import FakeHelper from '@/api/mock/lib/FakeHelper'

class AdditionalExpenseRecord extends FakeRecord {
    id = null
    sale_version_id = null
    assignee_id = null

    /**
     * @return {SaleVersionRecord}
     */
    get saleVersion() {
        return fakeDB.saleVersions.findByPK(this.sale_version_id)
    }

    get saleID() {
        return this.saleVersion.sale.id
    }

    /**
     * @return {ProductRecord}
     */
    get product() {
        return fakeDB.products.findOne({ item_type: 'additionalExpense', item_id: this.id })
    }

    set product(v) {
        this.product.$updateData(v, fakeDB.products)
    }

    get consolidatorName() {
        return this.product?.consolidatorName || ''
    }

    get chatRoom() {
        return fakeDB.chatRooms.findOne({ model_name: 'additionalExpense', model_id: this.id, category: 'additionalExpense' })
    }

    $insertData(data, tabel) {
        super.$insertData(data, tabel)

        const room = this.saleVersion.sale.generalChatRoom

        // fakeDB.chatRooms.buildNew('additionalExpense', this.id, 'additionalExpense', `Expense #${this.id}`, room.id)

        return this
    }
}

export default class AdditionalExpenseTable extends FakeTable {
    get recordModel() {
        return AdditionalExpenseRecord
    }

    constructor() {
        super('additionalExpenses', [])
    }

    /**
     * @param saleVersion {SaleVersionRecord}
     */
    buildNewCheckIn(saleVersion) {
        const data = {
            sale_version_id: saleVersion.id,

        }

        const additional = this.insert(data)

        const price = FakeHelper.randomMaxMinInt(200, 1000)
        const product = fakeDB.products.buildNew('additionalExpense', additional.id, {
            type: 'Check-in',
            pay_type: 'CC',

            sell_price: 0,
            fare: price,
            tax: 0,
        })

        return additional
    }

    /**
     * @param saleVersion {SaleVersionRecord}
     */
    buildNewBaggage(saleVersion) {
        const data = {
            sale_version_id: saleVersion.id,

        }

        const additional = this.insert(data)

        const price = FakeHelper.randomMaxMinInt(200, 1000)
        const product = fakeDB.products.buildNew('additionalExpense', additional.id, {
            type: 'Baggage',
            pay_type: 'CC',

            sell_price: 0,
            fare: price,
            tax: 0,
        })

        return additional
    }

    /**
     * @param saleVersion {SaleVersionRecord}
     * @param product {ProductRecord}
     * @param type
     */
    buildNewRefund(saleVersion, type, amount) {
        const data = {
            sale_version_id: saleVersion.id,
        }

        const additional = this.insert(data)

        fakeDB.products.buildNew('additionalExpense', additional.id, {
            type: type,
            pay_type: 'CC',

            sell_price: 0,
            fare: amount,
            tax: 0,
        })

        return additional
    }
}
