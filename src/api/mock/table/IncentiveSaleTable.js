import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import { faker } from '@faker-js/faker'
import FakeHelper from '@/api/mock/lib/FakeHelper'

class IncentiveSaleRecord extends FakeRecord {
    id = null
    sale_version_id = null

    /**
     * @return {SaleVersionRecord}
     */
    get saleVersion() {
        return fakeDB.saleVersions.findByPK(this.sale_version_id)
    }

    get saleID() {
        return this.saleVersion.sale.id
    }

    get consolidatorName() {
        return this.product?.consolidatorName || ''
    }

    /**
     * @return {ProductRecord}
     */
    get product() {
        return fakeDB.products.findOne({ item_type: 'incentiveSale', item_id: this.id })
    }

    set product(v) {
        console.log(v)
        this.product.$updateData(v, fakeDB.products)
    }

    get chatRoom() {
        return fakeDB.chatRooms.findOne({ model_name: 'incentiveSale', model_id: this.id, category: 'incentiveSale' })
    }

    $insertData(data, tabel) {
        super.$insertData(data, tabel)

        const room = this.saleVersion.sale.generalChatRoom

        // fakeDB.chatRooms.buildNew('incentiveSale', this.id, 'incentiveSale', `Incentive #${this.id}`, room.id)

        return this
    }
}

export default class IncentiveSaleTable extends FakeTable {
    get recordModel() {
        return IncentiveSaleRecord
    }

    constructor() {
        super('incentiveSales', [])
    }

    /**
     * @param saleVersion {SaleVersionRecord}
     */
    buildNewTips(saleVersion) {
        const data = {
            sale_version_id: saleVersion.id,
        }

        const IncentiveSale = this.insert(data)

        const product = fakeDB.products.buildNew('incentiveSale', IncentiveSale.id, {
            type: 'Tips',

            sell_price: FakeHelper.randomMaxMinInt(0, 150),
            fare: 0,
            tax: 0,
        })

        return IncentiveSale
    }

    /**
     * @param saleVersion {SaleVersionRecord}
     */
    buildNewInsurance(saleVersion) {
        const data = {
            sale_version_id: saleVersion.id,
        }

        const IncentiveSale = this.insert(data)

        const product = fakeDB.products.buildNew('incentiveSale', IncentiveSale.id, {
            type: 'Insurance',
            payment_type: 'TP',

            sell_price: FakeHelper.randomMaxMinInt(0, 500),
            fare: 0,
            tax: 0,
        })

        return IncentiveSale
    }
}
