import FakeTable from '@/api/mock/lib/FakeTable'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import fakeDB from "@/api/mock/fakeDB"
import { markRaw } from "vue"
import {
BellIcon,
CreditCardIcon,
MailIcon,
MessageCircleIcon,
MessageSquareIcon,
} from "@zhuowenli/vue-feather-icons"

const random = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1) + min)
}

const chance = (percent) => {
    return random(0, 100) <= percent
}

class NotificationSettingsRecord extends FakeRecord {
    id = null
    title = null
    icon = null
    description = null
    options = null
}

export default class NotificationSettingsTable extends FakeTable {
    get recordModel() {
        return NotificationSettingsRecord
    }

    constructor() {
        super('notificationSettings')
    }

    generateData() {
        this.insert({
            title: 'Comments',
            icon: 'message-circle-icon',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec convallis arcu est, a posuere velit ultricies ac. ',
            options: [
                {
                    title: 'Push',
                    icon: 'credit-card-icon',
                    enabled: true,
                },
                {
                    title: 'Email',
                    icon: 'mail-icon',
                    enabled: false,
                },
                {
                    title: 'Backoffice',
                    icon: 'bell-icon',
                    enabled: true,
                },
            ],
        })

        this.insert({
            title: 'Chat',
            icon: 'message-square-icon',
            description: null,
            options: [
                {
                    title: 'Push',
                    icon: 'credit-card-icon',
                    enabled: true,
                },
                {
                    title: 'Email',
                    icon: 'mail-icon',
                    enabled: false,
                },
                {
                    title: 'Backoffice',
                    icon: 'bell-icon',
                    enabled: false,
                },
            ],
        })
    }
}
