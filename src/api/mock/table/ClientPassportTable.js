import FakeTable from '@/api/mock/lib/FakeTable'
import { faker } from '@faker-js/faker'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import fakeDB from '@/api/mock/fakeDB'

class ClientPassportRecord extends FakeRecord {
    id = null
    client_id = null
    first_name = null
    last_name = null
    middle_name = null
    birthday_at = null
    country_id = null
    birth_country_id = null
    number = null
    sex = null
    issue_at = null
    expire_at = null
    comment = null
    _is_own = null
    _is_default = null
    deleted_at = null
    deleted_by = null
    created_at = null
    created_by = null

    get is_own() {
        return this._is_own
    }

    set is_own(v) {
        this._is_own = v ? 1 : 0
    }

    get is_default() {
        return this._is_default
    }

    set is_default(v) {
        if (v) {
            fakeDB.clientPassports.updateAll({ client_id: this.client_id, is_own: this.is_own, is_default: 1 }, { is_default: 0 })
        }
        this._is_default = v ? 1 : 0
    }

    get isDeleted() {
        return this.deleted_at === null ? 0 : 1
    }

    set isDeleted(v) {
        if (v) {
            this.deleted_at = FakeTable.currentTimeStamp()
            this.deleted_by = this.$currentAgentID()
        } else {
            this.deleted_at = null
            this.deleted_by = null
        }
    }

    get country() {
        return fakeDB.countries.findByPK(this.country_id)
    }

    get birthCountry() {
        return fakeDB.countries.findByPK(this.birth_country_id)
    }

    /**
     * @param table {ClientPassportTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('created_at', FakeTable.currentTimeStamp())
        this.$setDefaultValue('is_own', 1)
        this.$setDefaultValue('_is_default', 0)
        this.$setDefaultValue('created_by', this.$currentAgentID)
        this.$setDefaultValue('birth_country_id', this.country_id)
    }
}

export default class ClientPassportTable extends FakeTable {
    get recordModel() {
        return ClientPassportRecord
    }

    /**
     * @return {ClientRecord}
     */
    generateNew(clientID, isOwn, isDefault) {
        return this.insert({
            client_id: clientID,
            first_name: faker.name.firstName(),
            last_name: faker.name.lastName(),
            middle_name: faker.name.firstName(),
            birthday_at: faker.date.past(50, new Date("Sat Sep 20 1992 21:35:02 GMT+0200 (CEST)")).unixTimestamp(),
            country_id: fakeDB.countries.randomRecord().id,
            number: faker.finance.creditCardNumber(),
            sex: ['M', "F"][Math.floor(Math.random() * 2)],
            issue_at: (new Date()).unixTimestamp(),
            expire_at: (new Date()).unixTimestamp(),
            comment: '',
            is_own: isOwn ? 1 : 0,
            is_default: isDefault ? 1 : 0,
            created_by: 1,
        })
    }

    generateRandomNew(clientID) {
        this.generateNew(clientID, true)

        const count = Math.floor(Math.random() * 10)

        for (let i = 0; i < count; i++) {
            this.generateNew(clientID, Math.floor(Math.random() * 1) === 1)
        }
    }

    constructor() {
        super('clientPassports', [])
    }
}
