import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import FakeHelper from '@/api/mock/lib/FakeHelper'
import ArrayCond<PERSON>Helper from '@/lib/core/helper/ArrayCondition/ArrayConditionHelper'
import { faker } from '@faker-js/faker'

class SaleRecord extends FakeRecord {
    id = null
    lead_id = null
    project_id = null
    client_id = null
    executor_id = null
    paid_amount = null
    external_id = null
    is_hidden = null
    old_bo_id = null
    _sale_at = null
    group_id = null
    client_status_id = null
    type = null
    split_request_id = null
    is_enable_automatic_transactions = null
    remark = null
    is_test = null
    is_connex_pay_enabled = null

    _model_type = 'App\\Models\\Sale'

    get canViewClient() {
        return true
    }

    $setDefaultValues(table) {
        this.$setDefaultValue('paid_amount', 0)
        this.$setDefaultValue('external_id', faker.random.alphaNumeric(5).toUpperCase())
        this.$setDefaultValue('type', 'sale')
        this.$setDefaultValue('is_enable_automatic_transactions', 1)
        this.$setDefaultValue('client_status_id', 1)
        this.$setDefaultValue('is_connex_pay_enabled', 1)
    }

    get splitRequest() {
        return null
        // return fakeDB.approveRequests.findByPK(this.split_request_id)
    }

    get company_id() {
        return this.company.id
    }

    set company_id(v) {
        //
    }

    get company() {
        return fakeDB.companies.findByPK(this.project.company_id)
    }

    get keywords() {
        return `${this.id} ${this.clientFullName} ${this.executorFullName} ${this.summaryPNR}`
    }

    get created_at() {
        return this.activeVersion.created_at
    }

    get clientStatus() {
        return fakeDB.clientStatuses.find({ id: this.client_status_id })[0]
    }

    get createdByName() {
        const agent = this.activeVersion.createdBy

        return `${agent?.first_name} ${agent?.last_name}`
    }

    get sale_at() {
        if (this._sale_at === null) {
            this._sale_at = this.activeVersion.created_at
        }

        return this._sale_at
    }

    set sale_at(v) {
        this._sale_at = v
    }

    get pnrs() {
        return []
    }

    /**
     * @return {AgentRecord|null}
     */
    get executor() {
        return fakeDB.agents.findByPK(this.executor_id)
    }

    /**
     * @return {ClientRecord|null}
     */
    get client() {
        return fakeDB.clients.findByPK(this.client_id)
    }

    /**
     * @return {LeadRecord}
     */
    get lead() {
        return fakeDB.leads.findByPK(this.lead_id)
    }

    get leadPhone() {
        return this.lead.client_phone
    }

    get leadEmail() {
        return this.lead.client_email
    }

    get utm() {
        return this.lead.utm
    }

    get utmSource() {
        return this.utm.utm_source
    }

    get utmCampaign() {
        return this.utm.utm_campaign
    }

    get utmGa() {
        return this.utm.ga
    }

    get utmMedium() {
        return this.utm.utm_medium
    }

    get utmTerm() {
        return this.utm.utm_term
    }

    /**
     * @return {ProjectRecord|null}
     */
    get project() {
        return fakeDB.projects.findByPK(this.project_id)
    }

    /**
     * @return {SaleVersionRecord}
     */
    get activeVersion() {
        return fakeDB.saleVersions.findOne({ sale_id: this.id, is_active: 1 })
    }

    get departureAt() {
        return this.activeVersion.departure_at
    }

    get returnAt() {
        return this.activeVersion.return_at
    }

    get clientEmail() {
        return this.activeVersion.client_email
    }

    get clientPhone() {
        return this.activeVersion.client_phone
    }

    get clientEmailCount() {
        return this.lead.clientEmailCount
    }

    get clientPhoneCount() {
        return this.lead.clientPhoneCount
    }

    get versionToIata() {
        return this.activeVersion?.priceQuote?.toIata?.code || null
    }

    get versionFromIata() {
        return this.activeVersion?.priceQuote?.fromIata?.code || null
    }

    get versionPayType() {
        return this.activeVersion?.pay_type || null
    }

    get hasAward() {
        return this.activeVersion.hasAward
    }

    get saleType() {
        return this.activeVersion.saleType
    }

    get summaryNetPrice() {
        return this.activeVersion?.summary?.netPrice ? this.activeVersion.summary.netPrice + '' : ''
    }

    get summarySellPrice() {
        return this.activeVersion?.summary?.sellPrice ? this.activeVersion.summary.sellPrice + '' : ''
    }

    get summaryFee() {
        return this.activeVersion?.summary?.fee ? this.activeVersion.summary.fee + '' : ''
    }

    get summaryProfit() {
        return this.activeVersion?.summary?.profit ? this.activeVersion.summary.profit + '' : ''
    }

    get summaryTicketProfit() {
        return this.activeVersion?.summary?.ticketProfit || 0
    }

    get summaryInsuranceProfit() { // TP profit
        return this.activeVersion?.summary?.insuranceProfit || 0
    }

    get summaryTipsProfit() {
        return this.activeVersion?.summary?.tipsProfit || 0
    }

    get summaryExtraNegativeProfit() {
        return this.activeVersion?.summary?.extraNegativeProfit || 0
    }

    get extraNegativeProfit() {
        return this.summaryExtraNegativeProfit
    }

    get summaryExtraPositiveProfit() {
        return this.activeVersion?.summary?.extraPositiveProfit || 0
    }

    get extraPositiveProfit() {
        return this.summaryExtraPositiveProfit
    }

    get summaryExtraTotalProfit() {
        return this.activeVersion?.summary?.extraTotalProfit || 0
    }

    get extraTotalProfit() {
        return this.summaryExtraTotalProfit
    }

    get ticketProfit() {
        return this.summaryTicketProfit
    }

    get insuranceProfit() {
        return this.summaryInsuranceProfit
    }

    get tipsProfit() {
        return this.summaryTipsProfit
    }

    get summaryPNR() {
        return this.activeVersion?.summary?.passenger_name_record || null
    }

    get summaryVC() {
        return this.activeVersion?.summary?.validation_carrier_code || null
    }

    get searchTicket() {
        let result = ''
        const tickets = this.activeVersion.tickets

        if (tickets.length > 0) {
            tickets.forEach(ticket => {
                result = result + ` ${ticket?.validating_carrier_id} `
            })
        }

        return result
    }

    get searchPassengersNames() {
        let result = ''
        const tickets = this.activeVersion.tickets

        if (tickets.length > 0) {
            tickets.forEach(ticket => {
                const passenger = fakeDB.passengers.find({ id: ticket?.passenger_id })
                result = result + ` ${passenger?.first_name} ${passenger?.last_name}`
            })
        }

        return result
    }

    get isPaidFully() {
        return this.activeVersion.is_paid_fully
    }

    get isHasDebt() {
        return this.isPaidFully === 1 ? 0 : 1
    }

    get deptAmount() {
        return this.activeVersion.summary.sellPrice - this.paid_amount
    }

    get isTicketSent() {
        return this.activeVersion.is_ticket_sent
    }

    get isPaidBsb() {
        return this.activeVersion.is_paid_bsb
    }

    get isSaleClosed() {
        return this.activeVersion.is_sale_closed
    }

    get isSaleAdjusted() {
        return this.activeVersion.is_sale_adjusted
    }

    get executorFullName() {
        return `${this.executor.first_name} ${this.executor.last_name}`
    }

    get clientFullName() {
        return `${this.client?.first_name} ${this.client?.last_name}`
    }

    get generalChatRoom() {
        return this.getBelongsTo(fakeDB.chatRooms)[0]
        // return fakeDB.chatRooms.findOne({ model_name: 'sale', model_id: this.id, category: 'general' })
    }

    get chatMessagesCount() {
        return this.generalChatRoom.message_count
    }

    get lastChatMessage() {
        return this.generalChatRoom.lastMessage
    }

    get lastChatMessageTime() {
        return this.lastChatMessage?.created_at
    }

    /**
     * @return Array.<SaleTransactionRecord>
     */
    get transactions() {
        return fakeDB.saleTransactions.find({ sale_id: this.id })
    }

    get consolidatorId() {
        const sale_tickets = this.activeVersion.tickets

        return sale_tickets[0]?.product?.consolidator_area_id ? sale_tickets[0].product.consolidator_area_id : null
    }

    get members() {
        return fakeDB.saleMembers.find({ sale_id: this.id })
    }

    get summary() {
        return this.activeVersion.summary
    }

    $insertData(data, table) {
        super.$insertData(data, table)

        fakeDB.chatRooms.buildNew('App\\Models\\Sale', this.id, 'general', 'General', null, true)

        return this
    }

    get from() {
        return {
            code: this.versionFromIata,
            date: this.departureAt,
        }
    }

    get to() {
        return {
            code: this.versionToIata,
            date: this.returnAt,
        }
    }

    get validatingCarrier() {
        return 'Some VC'
    }

    get isPendingApproval() {
        return !!(this.id % 2)
    }

    get pnrs() {
        return []
    }

    get pccs() {
        return []
    }

    get product_types() {
        return []
    }

    get fops() {
        return []
    }

    get product_types() {
        return []
    }

    get commissions() {
        return []
    }
}

export default class SaleTable extends FakeTable {
    get recordModel() {
        return SaleRecord
    }

    constructor() {
        super('sales', [])
    }

    /**
     * @returns {LeadRecord}
     */
    getRandomLead() {
        const where = new ArrayConditionHelper()
        where.gt('executor_id', 0)
        const list = fakeDB.leads.find(where)

        return FakeHelper.randomItem(list)
    }

    /**
     * @returns {LeadRecord}
     */
    getRandomMultiPQ() {
        const where = new ArrayConditionHelper()
        where.eq('itinerary_type', 'multiCity')

        const list = fakeDB.priceQuotes.find(where)

        return FakeHelper.randomItem(list)
    }

    /**
     * @param lead {PriceQuoteRecord}
     */
    createSale(pq) {
        const lead = pq.lead
        lead.is_new = 0
        /** @type {ProjectRecord} */
        const project = fakeDB.projects.randomRecord()
        const sale = {
            lead_id: lead.id,
            project_id: project.id,
            company_id: project.company_id,
            client_id: lead.client_id,
            executor_id: lead.executor_id,
            group_id: FakeHelper.randomMaxMinInt(1, 12) > 5 ? FakeHelper.randomMaxMinInt(1, 12) : null,
            // group_id: FakeHelper.randomMaxMinInt(1, 50)
            is_test: FakeHelper.randomBool(),
        }

        const newSale = this.insert(sale)

        const version = fakeDB.saleVersions.buildNew(newSale, pq)

        const tips_ps = FakeHelper.randomMaxMinInt(0, 100)
        const ticket_protection_ps = FakeHelper.randomMaxMinInt(0, 100)
        const ticket_profit_ps = FakeHelper.randomMaxMinInt(0, 100)

        fakeDB.saleMembers.buildNew({
            category: 'executor',
            agent_id: newSale.executor_id,
            sale: newSale,
            tips_ps,
            ticket_protection_ps,
            ticket_profit_ps,
        })

        if (FakeHelper.randomBool(50)) {
            const secondAgent = FakeHelper.randomBool(50) ? fakeDB.agents.randomExecutor(newSale.executor.department_id, false) : fakeDB.agents.findByPK(1)

            fakeDB.saleMembers.buildNew({
                category: 'executor',
                agent_id: secondAgent.id,
                sale: newSale,
                tips_ps: 100 - tips_ps,
                ticket_protection_ps: 100 - ticket_protection_ps,
                ticket_profit_ps: 100 - ticket_profit_ps,
            })
        } else if (FakeHelper.randomBool()) {
            // fakeDB.approveRequests.buildSplitSale(newSale)
        }

        const chat_room = fakeDB.chatRooms.buildNew('App\\Models\\Sale', this.id, 'sale', `Sale #${this.id}\``, null, true)

        fakeDB.saleInvoices.buildNew(version)
        fakeDB.activityLogs.buildNewForSale(version)

        fakeDB.saleClientInvoices.buildNew(newSale)

        return newSale
    }

    buildMembers(sale, agent) {
        const tips_ps = 0
        const ticket_protection_ps = 0
        const ticket_profit_ps = 0

        fakeDB.saleMembers.buildNew({
            category: 'executor',
            agent_id: agent.id,
            sale: sale,
            tips_ps,
            ticket_protection_ps,
            ticket_profit_ps,
        })

        fakeDB.saleMembers.buildNew({
            category: 'executor',
            agent_id: sale.executor_id,
            sale: sale,
            tips_ps: 100 - tips_ps,
            ticket_protection_ps: 100 - ticket_protection_ps,
            ticket_profit_ps: 100 - ticket_profit_ps,
        })
    }

    generateData() {
        const multi = this.getRandomMultiPQ()

        for (let i = 0; i < 24; i++) {
            if (i === 0) {
                /** @type PreTicketRecord */
                const preTicket = fakeDB.preTickets.findOne({ sell_type: 'award' })
                // console.log(preTicket)
                const pq = preTicket.priceQuote
                // console.log(pq)
                const sale = this.createSale(pq)

                // console.log(sale)
            } else {
                const lead = this.getRandomLead()
                const pqs = lead.priceQuotes
                const sale = this.createSale(pqs[pqs.length - 1])

                if (FakeHelper.randomBool()) {
                    // fakeDB.approveRequests.buildSplitSale(sale)
                }
            }
        }
    }
}
