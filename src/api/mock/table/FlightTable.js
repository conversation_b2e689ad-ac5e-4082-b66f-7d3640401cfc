import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import { faker } from '@faker-js/faker'
import FakeRecord from '@/api/mock/lib/FakeRecord'

class FlightRecord extends FakeRecord {
    id = null
    airline_id = null
    from_iata_id = null
    to_iata_id = null
    departure_at = null
    arrived_at = null
    plane = null

    /**
     * @return {IataCodeRecord|null}
     */
    get fromIata() {
        return fakeDB.iataCodes.findByPK(this.from_iata_id)
    }

    /**
     * @return {IataCodeRecord|null}
     */
    get toIata() {
        return fakeDB.iataCodes.findByPK(this.to_iata_id)
    }

    /**
     * @return {AirlineRecord|null}
     */
    get airline() {
        return fakeDB.airlines.findByPK(this.airline_id)
    }
}

export default class FlightTable extends FakeTable {
    get recordModel() {
        return FlightRecord
    }

    randomPlane() {
        const list = [
            'Hawker 800XP',
            'Boeing 737-8K5',
            'Boeing 757-232',
            'Boeing 767-34AF(ER)',
            'Airbus A380-842',
            'Airbus A350-941',
            'Airbus A330-303',
            'Airbus A321-231',
            'Airbus A320-214',
            'Airbus A319-115',
            'Airbus A300B4-622R(F)',
        ]

        return list[Math.floor(Math.random() * list.length)]
    }

    /**
     *
     * @param data
     * @param departureAt {Date}
     * @returns {*}
     */
    buildNewFlightByData(data, departureAt) {
        const airline = fakeDB.airlines.randomAirline()
        const from = fakeDB.iataCodes.findOne({ code: data.flight.departureCode })
        const to = fakeDB.iataCodes.findOne({ code: data.flight.arrivalCode })

        const arrive = new Date(departureAt)
        arrive.setMinutes(arrive.getMinutes() + data.flight.time)

        const flight = {
            airline_id: airline.id,
            from_iata_id: from.id,
            to_iata_id: to.id,
            departure_at: departureAt.unixTimestamp(),
            arrived_at: arrive.unixTimestamp(),
            plane: data.flight.plane,
        }

        const f = this.findOne({ from_iata_id: flight.from_iata_id, to_iata_id: flight.to_iata_id, departure_at: flight.departure_at })

        if (!f) {
            return this.insert(flight)
        }

        return f
    }

    /**
     * @param fromID
     * @param toID
     * @param departDate {Date|null}
     * @return {FlightRecord}
     */
    buildNewFlight(fromID, toID, departDate = null) {
        const airline = fakeDB.airlines.randomAirline()
        const from = fakeDB.iataCodes.findByPK(fromID)
        const to = fakeDB.iataCodes.findByPK(toID)

        const depart = departDate || this.randomDate(1, 60)
        const arrived = new Date(depart)

        arrived.setHours(arrived.getHours() + Math.randomMaxMinInt(3, 12))

        const data = {
            airline_id: airline.id,
            from_iata_id: from.id,
            to_iata_id: to.id,
            departure_at: depart.unixTimestamp(),
            arrived_at: arrived.unixTimestamp(),
            plane: this.randomPlane(),
        }

        return this.insert(data)
    }

    buildNextFlight(fromID, toID, lastArrived) {
        const depart = this.randomDateHours(1, 8, lastArrived)

        return this.buildNewFlight(fromID, toID, depart)
    }

    constructor() {
        super('flights', [])
    }
}
