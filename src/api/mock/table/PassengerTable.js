import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import { faker } from '@faker-js/faker'

class PassengerRecord extends FakeRecord {
    id = null
    sale_version_id = null

    passenger_type = null

    first_name = null
    last_name = null
    middle_name = null
    birthday_at = null
    sex = null
    extra_seat_preference_id = null
    extra_meal_preference_id = null
    extra_special_preference_id = null

    /**
     * @return {TicketExtraPreferenceRecord}
     */
    get extraSeatPreference() {
        return fakeDB.ticketExtraPreferences.findByPK(this.extra_seat_preference_id)
    }

    /**
     * @return {TicketExtraPreferenceRecord}
     */
    get extraMealPreference() {
        return fakeDB.ticketExtraPreferences.findByPK(this.extra_meal_preference_id)
    }

    /**
     * @return {TicketExtraPreferenceRecord}
     */
    get extraSpecialPreference() {
        return fakeDB.ticketExtraPreferences.findByPK(this.extra_special_preference_id)
    }
}

export default class PassengerTable extends FakeTable {
    get recordModel() {
        return PassengerRecord
    }

    constructor() {
        super('passengers', [])
    }

    /**
     * @param saleVersion {SaleVersionRecord}
     */
    buildNew(saleVersion, type) {
        const data = {
            passenger_type: type,
            sale_version_id: saleVersion.id,
            first_name: faker.name.firstName(),
            last_name: faker.name.lastName(),
            middle_name: faker.name.lastName(),
            birthday_at: faker.date.past(50, new Date("Sat Sep 20 1992 21:35:02 GMT+0200 (CEST)")).unixTimestamp(),
            sex: ['M', "F"][Math.floor(Math.random() * 2)],
            extra_seat_preference_id: fakeDB.ticketExtraPreferences.randomPreference('seat').id,
            extra_meal_preference_id: fakeDB.ticketExtraPreferences.randomPreference('meal').id,
            extra_special_preference_id: fakeDB.ticketExtraPreferences.randomPreference('special').id,
        }

        return this.insert(data)
    }
}
