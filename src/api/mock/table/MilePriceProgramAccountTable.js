import FakeTable from '@/api/mock/lib/FakeTable'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import fakeDB from '@/api/mock/fakeDB'
import { faker } from '@faker-js/faker'
import FakeHelper from '@/api/mock/lib/FakeHelper'

class MilePriceProgramAccountRecord extends FakeRecord {
    id = null
    created_at = null
    account_holder = null
    account_number = null

    vpn = null
    pin = null
    phone = null

    consolidator_id = null

    balance = null
    mile_price = null

    status = null
    deleted_at = null

    remark_count = null

    mile_price_program_id = null

    // chat_room_id = null

    /**
     * @param table {MilePriceProgramAccountTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('created_at', FakeTable.currentTimeStamp() - (50000 * table.records.length))
        this.$setDefaultValue('balance', 0)
    }

    get isDeleted() {
        return this.deleted_at === null ? 0 : 1
    }

    get keywords() {
        return `${this.vpn}|${this.phone}`
    }

    /**
     * @return {ConsolidatorRecord}
     */
    get consolidator() {
        return fakeDB.consolidators.findByPK(this.consolidator_id)
    }

    get consolidatorName() {
        return this.consolidator.name
    }

    /**
     * @return {MilePriceProgramRecord}
     */
    get milePriceProgram() {
        return fakeDB.milePricePrograms.findByPK(this.mile_price_program_id)
    }

    get milePriceProgramName() {
        return this.milePriceProgram.name
    }

    get generalChatRoom() {
        return fakeDB.chatRooms.findOne({ model_name: 'mile-program-account', model_id: this.id, category: 'general' })
    }

    get incomingRequests() {
        return fakeDB.milePriceProgramRequests.find({ 'mile_price_program_account_id': this.id })
    }

    calc() {
        this.balance = 0
        let allMiles = 0
        let allPrice = 0

        this.incomingRequests.forEach(request => {
            if (request.balance > 0) {
                this.balance += Number(request.balance)
                allMiles += Number(request.product.net_price)
                allPrice += Number(request.product.net_price_base)
            }
        })

        this.mile_price = allPrice / allMiles
    }

    $insertData(data, table) {
        super.$insertData(data, table)

        // fakeDB.chatRooms.buildNew('mile-program-account', this.id, 'general', 'General')

        return this
    }
}

export default class MilePriceProgramAccountTable extends FakeTable {
    get recordModel() {
        return MilePriceProgramAccountRecord
    }

    constructor() {
        super('milePriceProgramAccounts', [])
    }

    /**
     * @return {MilePriceProgramAccountRecord}
     */
    randomMilePriceProgramAccount() {
        return this.randomRecord()
    }

    randomStatus() {
        return FakeHelper.randomItem(['is-active', 'is-use', 'is-suspended', 'is-blocked'])
    }

    generateData() {
        for (let i = 0; i < 5; i++) {
            const account = this.insert({
                mile_price_program_id: fakeDB.milePricePrograms.randomMilePriceProgram().id,
                vpn: faker.random.alphaNumeric(4),
                account_holder: faker.name.firstName() + ' ' + faker.name.lastName(),
                account_number: faker.random.alphaNumeric(8),
                pin: faker.random.alphaNumeric(15),
                phone: faker.phone.phoneNumber('+48 ## ### ## ##'),
                balance: 0, // FakeHelper.randomMaxMinInt(5, 25),
                remark_count: 0,
                mile_price: 0,
                status: 'is-active', // this.randomStatus(),
                consolidator_id: fakeDB.consolidators.randomConsolidator().id,
            })

            fakeDB.milePriceProgramRequests.buildNew(account)
            // fakeDB.chatMessages.buildNewForMilePriceProgramAccount(account)
        }
    }
}
