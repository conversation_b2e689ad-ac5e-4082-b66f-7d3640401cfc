import FakeTable from '@/api/mock/lib/FakeTable'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import { faker } from '@faker-js/faker'
import fakeDB from "@/api/mock/fakeDB"

class ClientEmailRecord extends FakeRecord {
    static primaryKey = ['client_id', 'email_id']

    client_id = null
    email_id = null
    email = null
    _is_default = null

    get is_default() {
        return this._is_default
    }

    set is_default(v) {
        if (v) {
            fakeDB.clientEmails.updateAll({ client_id: this.client_id, is_default: 1 }, { is_default: 0 })
        }
        this._is_default = v ? 1 : 0
    }

    $generatePrimaryKey(table) {
        this.email_id = table.uiid()
    }

    /**
     * @param table {ClientEmailTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('is_default', 0)
    }
}

export default class ClientEmailTable extends FakeTable {
    get recordModel() {
        return ClientEmailRecord
    }

    constructor() {
        super('clientEmails', [])
    }

    buildNew(client_id) {
        return this.insert({
            client_id: client_id,
            email: faker.internet.email(),
        })
    }
}
