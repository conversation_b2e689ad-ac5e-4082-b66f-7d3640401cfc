import FakeTable from '@/api/mock/lib/FakeTable'
import fakeDB from '@/api/mock/fakeDB'
import { faker } from '@faker-js/faker'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import FakeHelper from '@/api/mock/lib/FakeHelper'

class LeadRecord extends FakeRecord {
    id = null
    from_iata_id = null
    to_iata_id = null
    client_id = null
    client_phone = null
    is_unknown_phone = null
    client_email = null
    client_ip = ''
    departure_at = null
    return_at = null
    taken_at = null
    external_id = null
    external_resource_category_id = null
    created_at = null
    updated_at = null
    created_by = null
    updated_by = null
    executor_id = null
    itinerary_type = null
    itinerary_class = null
    itinerary_plan = null
    adult_count = null
    child_count = null
    infant_count = null
    utm = null
    is_bonus = null
    status_id = null

    // is_sold=null
    expert_id = null
    expert_request_at = null
    expert_request_is_done = null
    expert_request_number = null
    expert_request_start_at = null
    duplicate_status = ''
    remark = null
    is_hidden = null
    keepClient = null
    keepClientRemark = null
    is_to_be_lost = null
    is_to_be_lost_at = null

    _model_type = 'App\\Models\\Lead'

    project_id = null
    company_id = null

    /**
     * @param table {LeadTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('created_at', FakeTable.currentTimeStamp())
        // this.$setDefaultValue('expert_request_at', FakeTable.currentTimeStamp())
        this.$setDefaultValue('updated_at', this.created_at)
        this.$setDefaultValue('itinerary_type', '')
        this.$setDefaultValue('itinerary_class', '')
        this.$setDefaultValue('adult_count', 0)
        this.$setDefaultValue('child_count', 0)
        this.$setDefaultValue('infant_count', 0)
        this.$setDefaultValue('expert_request_number', 0)
        this.$setDefaultValue('status_id', 1)
        this.$setDefaultValue('is_bonus', 0)
        this.$setDefaultValue('external_resource_category_id', fakeDB.externalResourceCategories.findOne({ system_name: 'bo' }).id)
        this.$setDefaultValue('remark', '')
        this.$setDefaultValue('is_hidden', FakeHelper.randomBool() ? 1 : 0)
        this.$setDefaultValue('is_to_be_lost', FakeHelper.randomBool() ? 1 : 0)
        this.$setDefaultValue('is_to_be_lost_at', FakeTable.currentTimeStamp())
        this.$setDefaultValue('is_unknown_phone', 0)

        this.$setDefaultValue('utm',
            {
                'ga': 'GA1.2.259890655.1635247759',
                'utm_medium': 'cpc',
                'utm_source': FakeHelper.randomBool() ? 'google' : 'facebook',
                'utm_campaign': 'DSC_target-europe-mob',
                'utm_term': '+business +class +flights',
                'utm_content': 'SH-gen-kwd',
                'ip': '***************',
            },
        )
    }

    get project() {
        return fakeDB.projects.findByPK(this.project_id)
    }

    get isAward() {
        return this.project?.is_main ? 0 : 1
    }

    get utmIP() {
        return this.utm.ip
    }

    get utmIPCountry() {
        return ''
    }

    get utmSource() {
        return this.utm.utm_source
    }

    get utmCampaign() {
        return this.utm.utm_campaign
    }

    get utmGa() {
        return this.utm.ga
    }

    get utmMedium() {
        return this.utm.utm_medium
    }

    get utmTerm() {
        return this.utm.utm_term
    }

    get keywords() {
        return `${this.id}  ${this.client_email} ${this.client_phone} ${this.clientName} `
    }

    /**
     * @return {AgentLeadRecord|null}
     */
    get agentLead() {
        return fakeDB.agentLeads.findOne({ agent_id: this.$currentAgentID(), lead_id: this.id })
    }

    get is_pinned() {
        return this.agentLead?.is_pinned || 0
    }

    get star_type() {
        return this.agentLead?.star_type || ''
    }

    get is_sold() {
        const lead_sales = fakeDB.sales.find({ lead_id: this.id })

        return lead_sales.length > 0 ? 1 : 0
    }

    get fullDuplicates() {
        const response = [this]
        this.duplicateLeads.forEach(duplicate => {
            response.push(duplicate.duplicate)
        })

        return response
    }

    /**
     * @return {ExternalResourceCategoryRecord|null}
     */
    get externalResourceCategory() {
        return fakeDB.externalResourceCategories.findByPK(this.external_resource_category_id)
    }

    /**
     * @return {IataCodeRecord|null}
     */
    get fromIata() {
        return fakeDB.iataCodes.findByPK(this.from_iata_id)
    }

    get fromIataCode() {
        return this.fromIata.code
    }

    /**
     * @return {IataCodeRecord|null}
     */
    get toIata() {
        return fakeDB.iataCodes.findByPK(this.to_iata_id)
    }

    get toIataCode() {
        return this.toIata.code
    }

    /**
     * @return {AgentRecord|null}
     */
    get createdBy() {
        return fakeDB.agents.findByPK(this.created_by)
    }

    get createdByName() {
        return `${this.createdBy?.first_name} ${this.createdBy?.last_name}`
    }

    get externalResourceCategoryName() {
        return this.externalResourceCategory.name
    }

    /**
     * @return {AgentRecord|null}
     */
    get updatedBy() {
        return fakeDB.agents.findByPK(this.updated_by)
    }

    /**
     * @return {AgentRecord|null}
     */
    get executor() {
        return fakeDB.agents.findByPK(this.executor_id)
    }

    /**
     * @return {AgentRecord|null}
     */
    get expert() {
        return fakeDB.agents.findByPK(this.expert_id)
    }

    get executorName() {
        return `${this.executor?.first_name} ${this.executor?.last_name}`
    }

    /**
     * @return {ClientRecord|null}
     */
    get client() {
        return fakeDB.clients.findByPK(this.client_id)
    }

    set client(v) {
        if (this.client) {
            this.client.$updateData(v, fakeDB.clients)
        } else {
            const newClient = fakeDB.clients.generateNew()
            this.client_id = newClient.id
            newClient.$updateData(v, fakeDB.clients)
        }
    }

    get clientName() {
        return `${this.client.first_name} ${this.client.last_name}`
    }

    get clientEmailCount() {
        return fakeDB.clientEmails.find({ client_id: this.client_id }).length
    }

    get clientPhoneCount() {
        return fakeDB.clientPhones.find({ client_id: this.client_id }).length
    }

    /**
     * @return {Array.<DuplicateLeadRecord>}
     */
    get duplicateLeads() {
        return fakeDB.duplicateLeads.find({ 'lead_id': this.id })
    }

    get duplicateLeadCount() {
        return this.duplicateLeads.length
    }

    /**
     * @return {Array.<PriceQuoteRecord>}
     */
    get priceQuotes() {
        return fakeDB.priceQuotes.find({ 'lead_id': this.id })
    }

    get priceQuoteCount() {
        return this.priceQuotes.length
    }

    get priceQuoteId() {
        return this.priceQuotes[0]?.id
    }

    get hasPriceQuotes() {
        return this.priceQuotes.length > 0 ? 1 : 0
    }

    get hasAgentsPriceQuotes() {
        return 0
    }

    get toBeLostExpiredAt() {
        if (this.is_to_be_lost) return this.is_to_be_lost_at + 60 * 60 * 24 * 3

        return null
    }

    get priceQuoteLastTime() {
        if (this.hasPriceQuotes) {
            return this.priceQuotes[this.priceQuotes.length - 1].created_at
        } else {
            return null
        }
    }

    get generalChatRoom() {
        // const stringID = this.generateStringID('lead', this.id)
        // return fakeDB.chatRooms.findOne({ model_name: 'lead', model_id: this.id, category: 'general' })
        return this.getBelongsTo(fakeDB.chatRooms)[0]
    }

    get chatRoomID() {
        return this.generalChatRoom.id
    }

    get generalTaskGroup() {
        return fakeDB.taskGroups.findOne({ model_name: 'lead', model_id: this.id, category: 'general' })
    }

    get customerSupportTaskGroup() {
        return fakeDB.taskGroups.findOne({ model_name: 'lead', model_id: this.id, category: 'general' })
    }

    get status() {
        return fakeDB.leadStatuses.findOne({ id: this.status_id })
    }

    $insertData(data, table) {
        super.$insertData(data, table)
        const tasks_group = fakeDB.taskGroups.buildNew('lead', this.id, 'general', 'General', this.company_id)
        fakeDB.tasks.buildNewForLead(tasks_group, this)

        const room = fakeDB.chatRooms.buildNew('App\\Models\\Lead', this.id, 'general', 'General', null, true)
        // fakeDB.chatRooms.buildNew('lead', this.id, 'remark', 'Remark', room.id)

        return this
    }
}

export default class LeadTable extends FakeTable {
    get recordModel() {
        return LeadRecord
    }

    constructor() {
        super('leads', [])
    }

    randomItineraryClass() {
        return ['premium', 'business', 'first'][Math.floor(Math.random() * 3)]
    }

    randomItineraryType() {
        return ['oneWay', 'roundTrip', 'multiCity'][Math.floor(Math.random() * 3)]
    }

    randomTime() {
        const depTime = new Date()
        depTime.setDate(depTime.getDate() + Math.randomMaxMinInt(1, 50))
        const returnTime = new Date(depTime)
        returnTime.setDate(returnTime.getDate() + Math.randomMaxMinInt(1, 15))

        return {
            departure_at: depTime,
            return_at: returnTime,
        }
    }

    generateData() {
        const salesDep = fakeDB.departments.findOne({ system_name: 'sales' })

        const leadsCount = 10

        for (let i = 0; i < leadsCount; i++) {
            const randomLeadData = fakeDB.segments.generateRandomLeadData()
            const randomIata = fakeDB.iataCodes.fromTo(randomLeadData.from, randomLeadData.to)

            const newClient = fakeDB.clients.generateNew()
            const resource = FakeHelper.randomBool() ? fakeDB.externalResourceCategories.findOne({ system_name: 'web_site' }) : fakeDB.externalResourceCategories.findOne({ system_name: 'bo' })

            /** @type {AgentRecord} */
            const author = fakeDB.agents.randomExecutor(salesDep.id, false)
            /** @type {AgentRecord} */
            const executor = fakeDB.agents.randomExecutor(salesDep.id)
            // TODO: get executor from same project
            const time = this.randomTime()
            const type = this.randomItineraryType()

            const adult_count = FakeHelper.randomMaxMinInt(1, 3)
            const child_count = FakeHelper.randomMaxMinInt(0, 3)

            /** @type {ProjectRecord} */
            const project = fakeDB.projects.findByPK(executor.mainProjectId)

            const lead = {
                from_iata_id: randomIata.from.id,
                to_iata_id: randomIata.to.id,

                client_id: newClient.id,
                client_phone: fakeDB.clients.generatePhone(newClient),
                client_email: fakeDB.clients.generateEmail(newClient),
                client_ip: faker.internet.ip(),

                external_id: fakeDB.externalResourceCategories.leadID(),
                external_resource_category_id: resource.id,
                created_by: author.id,
                updated_by: author.id,

                departure_at: time.departure_at.unixTimestamp(),
                return_at: type === 'roundTrip' ? time.return_at.unixTimestamp() : null,
                executor_id: executor ? executor.id : null,
                taken_at: executor ? FakeTable.currentTimeStamp() : null,
                itinerary_type: type,
                itinerary_class: this.randomItineraryClass(),
                adult_count: adult_count,
                child_count: child_count,
                infant_count: FakeHelper.randomMaxMinInt(0, adult_count),
                is_bonus: FakeHelper.randomMaxMinInt(0, 100) > 40 ? 1 : 0,
                created_at: time.departure_at.unixTimestamp() - 86400,
                remark: FakeHelper.randomBool() ? 'Some remark' : '',
                project_id: project.id,
                company_id: project.company_id,
            }

            const newLead = this.insert(lead)

            fakeDB.duplicateLeads.buildDuplicates(newLead)

            const pq = fakeDB.priceQuotes.buildNewPQWithSegments(newLead, randomLeadData)
            const expert_pq = fakeDB.priceQuotes.buildNewPQWithSegments(newLead, randomLeadData, true)

            if (pq) {
                newLead.itinerary_type = pq.itinerary_type

                if (FakeHelper.randomMaxMin(1, 3) > 2) {
                    const randomLeadData2 = fakeDB.segments.generateRandomLeadData()
                    fakeDB.priceQuotes.buildNewPQWithSegments(newLead, randomLeadData2)
                }
            }

            if (newLead.id >= leadsCount - 2) {
                for (let i = 0; i < 3; i++) {
                    fakeDB.externalPrices.buildNew(newLead.id)
                }
            }
        }
    }
}
