import FakeTable from '@/api/mock/lib/FakeTable'
import { faker } from '@faker-js/faker'
import fakeDB from '@/api/mock/fakeDB'
import FakeRecord from '@/api/mock/lib/FakeRecord'
import FakeHelper from '@/api/mock/lib/FakeHelper'

class AgentRecord extends FakeRecord {
    id = null
    first_name = ''
    last_name = ''
    email = 0
    _avatar_file_id = null
    status_id = 1 // active
    last_login_at = null
    last_refresh_at = null
    created_at = null
    department_id = null
    position_id = null
    team_id = null
    curator_id = null
    replaced_by = null
    replaced_till = null
    _is_online = null
    // voip_ext = '123'
    start_shift = null
    ringcentral_id = null
    note = null
    main_company_id = null
    companies = []
    payload = null

    get mainProjectId() {
        return this.mainProject.id
    }

    set mainProjectId(val) {
        /** @type {ProjectRecord} */
        const project = fakeDB.projects.findByPK(val)
        this.main_company_id = project.company_id
    }

    get mainProject() {
        //return fakeDB.projects.findOne({})
        return fakeDB.projects.findOne({ company_id: this.main_company_id, is_main: 1 })
    }

    isCompanyEnabled(company_id) {
        return this.companies.includes(company_id)
    }

    enableCompany(company_id) {
        if (!this.companies.includes(company_id)) {
            this.companies.push(company_id)
        }
    }

    disableCompany(company_id) {
        if (this.companies.includes(company_id)) {
            this.companies = this.companies.filter(company => company !== company_id)
        }

        if (this.main_company_id === company_id) {
            this.main_company_id = this.companies[0] || null
        }
    }

    get avatar_file_id() {
        return this._avatar_file_id
    }

    set avatar_file_id(v) {
        if (v > 0) {
            const file = fakeDB.files.findByPK(Number(v))
            file.file_type_system_name = 'agent_avatar'
            this._avatar_file_id = file.id
        } else {
            this._avatar_file_id = null
        }
    }

    get isOnline() {
        return this._is_online
    }

    get newDevicesCount() {
        return 0
    }

    /**
     * @param table {AgentTable}
     */
    $setDefaultValues(table) {
        this.$setDefaultValue('created_at', FakeTable.currentTimeStamp())
        this.$setDefaultValue('_is_online', FakeHelper.randomBool() ? 1 : 0)
        this.$setDefaultValue('payload', {})
    }

    get department() {
        return fakeDB.departments.findByPK(this.department_id)
    }

    get position() {
        return fakeDB.positions.findByPK(this.position_id)
    }

    get team() {
        return fakeDB.teams.findByPK(this.team_id)
    }

    get keywords() {
        return `${this?.first_name}|${this?.last_name}|${this.phone}|${this.email}`
    }

    get fullName() {
        return `${this?.first_name} ${this?.last_name}`
    }

    /**
     * @return {FileRecord|null}
     */
    get avatarFile() {
        return fakeDB.files.findByPK(this.avatar_file_id)
    }

    get avatar() {
        return this.avatarFile?.file_url
    }

    get replaced() {
        return fakeDB.agents.findByPK(this.replaced_by)
    }

    /**
     * @return {AgentProjectSettingRecord[]}
     */
    get projectSettings() {
        return fakeDB.agentProjectSettings.find({ agent_id: this.id })
    }

    /**
     * @return {AgentProjectSettingRecord|null}
     */
    get defaultProjectSetting() {
        return fakeDB.agentProjectSettings.findOne({ agent_id: this.id, is_default: 1 })
    }

    get defaultEmail() {
        return this.defaultProjectSetting.email
    }

    get defaultPhone() {
        return this.defaultProjectSetting.phone
    }

    get defaultVoipExt() {
        return this.defaultProjectSetting.voip_ext
    }

    get positionName() {
        return this.position.name
    }

    get departmentName() {
        return this.department.name
    }

    get departmentSystemName() {
        return this.department.system_name
    }

    get teamName() {
        return this.team.name
    }

    get isActive() {
        return this.status_id == 1
    }

    set isActive(v) {
        this.status_id = v ? 1 : 0
    }

    get isBot() {
        return false
    }

    get personalInfo() {
        return {
            'first_name': this.payload?.first_name,
            'last_name': this.payload?.last_name,
            'birthday_at': this.payload?.birthday_at,
            'sex': this.payload?.sex,
            'phone': this.payload?.phone,
            'email': this.payload?.email,
            'address': this.payload?.address,
            'serial_nr': this.payload?.serial_nr,
            'idnp': this.payload?.idnp,
        }
    }

    set personalInfo(data) {
        this.payload = data
    }

    /**
     * @return {TaskRecord[]}
     */
    get tasks() {
        return fakeDB.tasks.find({ model_id: this.id, 'model_name': 'agent' })
    }

    get summary() {
        const todayTakenCount = FakeHelper.randomMaxMinInt(0, 8)

        return {
            'monthTakenCount': todayTakenCount * 28,
            'weekTakenCount': todayTakenCount * 5,
            'shiftTakenCount': todayTakenCount,
            'yesterdayTakenCount': todayTakenCount,
            'beforeYesterdayTakenCount': todayTakenCount,
            'unprocessedCount': fakeDB.leads.find({ hasAgentsPriceQuotes: todayTakenCount > 2 ? Math.floor(todayTakenCount / 2) : 0 }).length,
            'pastHourCount': FakeHelper.randomMaxMinInt(1, 4),
            'procesessedWellCount': todayTakenCount,
            'procesessedNotWellCount': todayTakenCount,
        }
    }

    get shiftEnd() {
        return FakeTable.currentTimeStamp() + FakeHelper.randomMaxMinInt(3000, 3600 * 7)
    }

    get loginIn() {
        return FakeTable.currentTimeStamp()
    }

    get personalInfoFullName() {
        if (this.personalInfo?.first_name) {
            return `${this.personalInfo?.first_name} ${this.personalInfo?.last_name}`
        }

        return ''
    }
}

export default class AgentTable extends FakeTable {
    get recordModel() {
        return AgentRecord
    }

    insert(data, safe = true) {
        // console.log(this)
        // console.log(data)

        return super.insert(data, safe)
    }

    constructor() {
        super('agents', [])
    }

    /**
     * @param department_id
     * @param maybe_empty
     * @return {AgentRecord|null}
     */
    randomExecutor(department_id, maybe_empty = true) {
        const list = this.find({ department_id })

        if (maybe_empty) {
            list.push(null)
        }

        const index = FakeHelper.randomMaxMinInt(0, list.length - 1)

        return list[index]
    }

    generateData() {
        const positionManager = fakeDB.positions.findOne({ system_name: 'manager' })
        const positionSupervisor = fakeDB.positions.findOne({ system_name: 'supervisor' })
        const positionAgent = fakeDB.positions.findOne({ system_name: 'agent' })

        const departmentMarketing = fakeDB.departments.findOne({ 'system_name': 'marketing' })
        const teamMarketing = fakeDB.teams.generateNew('Admin Team', 1, 1, departmentMarketing.id)

        const admin = this.insert({
            team_id: teamMarketing.id,
            department_id: 1,
            position_id: positionManager.id,
            curator_id: null,
            first_name: 'Admin',
            last_name: 'Admin',
            email: '<EMAIL>',
            mainProjectId: 1,
            companies: [1, 2],
        })

        try {
            fakeDB.activityLogs.buildNewForAgent(admin.id)
        } catch (e) {
            console.log('fakeDB.activityLogs.buildNewForAgent', e)
        }

        // Employments

        for (const company of fakeDB.companies.records) {
            for (const department of fakeDB.departments.records) {
                if (department.system_name !== 'empty') {
                    try {
                        let manager = {}
                        let team = {}

                        // const have_wide_companies_access =  ['bookkeeping', 'customer_support', 'it', 'marketing'].includes(department.system_name)

                        const main_project = fakeDB.projects.findOne({ company_id: company.id, is_main: 1 })

                        if (department.system_name === 'it') {
                            manager = admin
                            manager.department_id = department.id
                            team = teamMarketing
                        } else {
                            manager = this.insert({
                                team_id: null,
                                department_id: department.id,
                                position_id: positionManager.id,
                                curator_id: null,
                                first_name: faker.person.firstName(),
                                last_name: FakeHelper.randomBool() ? '' : faker.person.lastName(),
                                email: `${department.system_name}_manager@${company.system_name}.loc`,
                                mainProjectId: main_project.id,
                                companies: [main_project.company_id],
                            })
                        }
                        team = fakeDB.teams.generateNew(manager.first_name, manager.id, main_project.id, department.id)
                        const managerAva = fakeDB.files.generateAva(manager.id, admin.id)
                        fakeDB.activityLogs.buildNewForAgent(manager.id)
                        // console.log(manager.id, department.system_name, manager.email)
                        manager = this.update(manager.id, {
                            team_id: team.id,
                            avatar_file_id: managerAva.id,
                            // department_id: department.id,
                        })

                        const supervisor = this.insert({
                            team_id: team.id,
                            department_id: department.id,
                            position_id: positionSupervisor.id,
                            curator_id: manager.id,
                            first_name: faker.person.firstName(),
                            last_name: '',
                            email: `${department.system_name}_supervisor@${company.system_name}.loc`,
                            mainProjectId: main_project.id,
                            companies: [main_project.company_id],
                        })

                        const supervisorAva = fakeDB.files.generateAva(supervisor.id, admin.id)
                        this.update(supervisor.id, { avatar_file_id: supervisorAva.id })
                        fakeDB.activityLogs.buildNewForAgent(supervisor.id)

                        const agent = this.insert({
                            team_id: team.id,
                            department_id: department.id,
                            position_id: positionAgent.id,
                            curator_id: supervisor.id,
                            first_name: faker.person.firstName(),
                            last_name: faker.person.lastName(),
                            email: `${department.system_name}_agent@${company.system_name}.loc`,
                            mainProjectId: main_project.id,
                            companies: [main_project.company_id],
                        })

                        const agentAva = fakeDB.files.generateAva(agent.id, admin.id)
                        this.update(agent.id, { avatar_file_id: agentAva.id })

                        fakeDB.activityLogs.buildNewForAgent(agent.id)

                        // Employments
                    } catch (e) {
                        console.log('Agent Create', company, department)
                        console.log(e)
                    }
                }
            }
        }

        return true
    }
}
