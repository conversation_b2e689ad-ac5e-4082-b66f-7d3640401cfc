import CrudModelDictionary from '@/lib/core/crud/CrudModelDictionary'
import WebUser from '@/lib/service/WebUser'
import CardHelper from '@/lib/core/helper/CardHelper'
import { getWorkspaceProject } from '@/lib/core/helper/WorkspaceProjectHelper'
import ProjectCardWithPartnersModel from '@/api/models/Project/ProjectCardWithPartnersModel'
import { toHumanPhrase } from '~/lib/Helper/StringHelper'

const payTypes = [
    {
        value: 'invoice',
        label: 'Check',
    },
    {
        value: 'HCC',
        label: 'Holder CC',
    },
    {
        value: 'PW',
        label: 'Pax Wire', // pax wire
    },
    // {
    //     value: 'other',
    //     label: 'Other',
    // },
]

const inhouseCC = null

export default class ProductEntity extends CrudModelDictionary {
    static apiPath = 'products'

    static apiVersion = 'v1'

    static fields() {
        return {
            id: 'Product ID',
            sale_id: 'Sale id',
            type: 'type',
            created_at: '',
            external_number: '',
            fare: 'Fare',
            tax: 'Tax',
            net_price: 'Price',
            net_price_base: 'Price',
            commission: 'commission value',
            commission_ps: 'commission percent',
            balance: 'value of product', // value?
            issuing_fee: '',
            consolidator_order_id: '',
            check_payment: 'CK value',
            check_payment_ps: 'CK ps depends of pq check_payment_ps and payment gateway',

            pay_type: 'Pay type - CC',
            issued_at: 'product given to customer at',
            remark: '',
            status: 'status',
            card_identity: '',
            isAward: '',
        }
    }

    static getPaymentType(key) {
        return this.getPaymentTypeList().find(item => item.value === key)?.label ?? key
    }

    static getCCPaymentTypeText(id, pay_type, values) {
        if (['oldComVCC', 'oldNewVCC'].includes(pay_type)) {
            pay_type = 'comVCC'
        }
        const keyString = pay_type + '-' + id
        const index = values.findIndex(item => item.value === keyString)

        if (index > -1) {
            return values[index].label
        } else {
            return this.getPaymentType(pay_type)
        }
    }

    static async getInhouseCC(instance, sale_id) {
        if (!instance) {
            return []
        }

        if (inhouseCC !== null) {
            return inhouseCC
        }

        const { result } = await ProjectCardWithPartnersModel.contextInstance(instance).find({
            project_id: getWorkspaceProject(instance),
            pageSize: 0,
            where: [{ eq: ['category_id', 1]}],
            sale_id: sale_id,
        })

        return result
    }

    static composeInhouseVcc(inhouse_vcc = [], canEditPayments = false, current_project) {
        const types = []

        if (canEditPayments) {
            types.push({
                groupLabel: 'Com VCC',
                value: `comVCC-`,
                label: `New`,
            })
        }

        if (inhouse_vcc?.length > 0) {
            inhouse_vcc.forEach(credit_card => {
                const item_workspace = useService('workspace').getProjectWorkspace(credit_card.project_id)
                const same_project = current_project === item_workspace
                types.push({
                    // sort: same_project ? 0 : 10,
                    groupLabel: same_project ? `Com VCC` : 'Partners VCC',
                    value: `comVCC-${credit_card.id}`,
                    label: `VCC ${String(credit_card.strip)}`,
                    disabled: !!credit_card.is_disabled,
                })
            })
        }

        return types
    }

    static composeInhouseCC(inhouse_cc = [], current_project) {
        const types = []

        if (inhouse_cc.length > 0) {
            inhouse_cc.forEach(credit_card => {
                const item_workspace = useService('workspace').getProjectWorkspace(credit_card.project_id)
                const same_project = current_project === item_workspace

                // const info = CardHelper.detectCardType(credit_card.strip)
                types.push({
                    groupLabel: same_project ? `Com CC` : 'Partners CC',
                    value: `comCC-${credit_card.id}`,
                    label: `CC **${String(credit_card.strip).slice(-4)}`,
                    disabled: !!credit_card.is_disabled,
                })
            })
        }

        return types
    }

    static composePaxCC(paxCC = []) {
        const types = []

        if (paxCC.length > 0) {
            paxCC.forEach(credit_card => {
                const info = CardHelper.detectCardType(credit_card.payload.card)
                types.push({
                    // sort: 100,
                    groupLabel: 'Pax CC',
                    value: `CC-${credit_card.id}`,
                    label: `PAX **${String(credit_card.payload.card).slice(-4)}`,
                })
            })
        }

        return types
    }

    static composePayTypes(payTypes = []) {
        const types = []

        payTypes.forEach(payType => {
            types.push({
                groupLabel: '',
                value: payType.value,
                label: payType.label,
            })
        })

        return types
    }

    /**
     * Compose grouped list of payment methods for cardIdentity select.
     * @param {Object} options - Available Credit Cards.
     * {paxCC: [], inhouse_vcc: [], inhouse_cc: [], onlyPaxCC: false}
     * @param options.inhouse_vcc {Array} - Virtual CC.
     * @param options.inhouse_cc {Array} - TBC CC.
     * @param options.paxCC {Array} - Pax CC.
     * @param options.onlyPaxCC {boolean} onlyPaxCC
     * @param options.canEditPayments {boolean} canEditPayments
     * @param instance component instance
     */
    static getPaymentTypeListWithCards(options, instance) {
        const current_project = useService('workspace').getProjectWorkspace(getWorkspaceProject(instance))

        const types = []

        types.push(...ProductEntity.composePaxCC(options?.paxCC))

        if (!options?.onlyPaxCC) {
            types.push(...ProductEntity.composePayTypes(payTypes))
        }

        types.push(...ProductEntity.composeInhouseCC(options.inhouse_cc, current_project))
        types.push(...ProductEntity.composeInhouseVcc(options?.inhouse_vcc, options.canEditPayments, current_project))

        // const result = types.sort((a, b) => {
        //     if (a.groupLabel < b.groupLabel) {
        //         return -1
        //     }
        //
        //     if (a.groupLabel > b.groupLabel) {
        //         return 1
        //     }
        //
        //     return 0
        // })

        // force push to end
        types.push(
            {
                groupLabel: '',
                value: `other`,
                label: `Other`,
            },
        )

        return types
    }

    static getPaymentTypeList() {
        const types = payTypes.map(item => item)
        types.push({
            value: 'CC',
            label: 'Pax CC',
        })
        types.push({
            value: 'comCC',
            label: 'Com CC',
        })
        types.push({
            value: 'comVCC',
            label: 'Com VCC',
        })

        return types
    }

    static getPaymentTypeLabel(code) {
        return this.getPaymentTypeList().find(item => item.value === code).label
    }

    static getIncentiveTypeList(data) {
        const types = [
            {
                groupLabel: '',
                value: 'Tips',
                label: 'Tips',
            },
        ]

        // TODO: ensure if sale is from click-to-book?
        types.push({
            groupLabel: '',
            value: 'Flexible Ticket',
            label: 'Flexible Ticket',
        })

        if (WebUser.can('manage', 'Sale', data)) {
            types.push({
                groupLabel: '',
                value: 'Extra GP',
                label: 'Extra GP',
                access: 'manage',
            })
        }

        const SaleInsuranceTypes = useDictionary('SaleInsuranceType').records.map(record => ({
            groupLabel: toHumanPhrase(record.group),
            value: record.id,
            label: record.title,
        }))

        types.push(...SaleInsuranceTypes)

        // types.push({
        //     groupLabel: '',
        //     value: 'Insurance',
        //     label: 'Insurance',
        //     disabled: true,
        // })

        return types
    }

    static getAdditionalTypeList() {
        return [
            {
                value: 'Baggage',
                label: 'Baggage',
            },
            {
                value: 'Check-in',
                label: 'Check-in',
            },
            {
                value: 'Ticket Refund',
                label: 'Ticket Refund',
            },
            {
                value: 'Commission Refund',
                label: 'Commission Refund',
            },
            {
                value: 'Cash Upgrade',
                label: 'Cash Upgrade',
            },
            {
                value: 'Award Upgrade',
                label: 'Award Upgrade',
            },
            {
                value: 'Other',
                label: 'Other',
            },
            {
                value: 'Voucher',
                label: 'Voucher',
            },
            {
                value: 'Seats',
                label: 'Seats',
            },
            {
                value: 'Special Services Fee',
                label: 'Special Services Fee',
            },
            {
                value: 'Emd',
                label: 'EMD',
            },
            {
                value: 'Points Trade',
                label: 'Points Trade',
            },
            {
                value: 'Airline Reimbursement Fee',
                label: 'Airline Reimbursement Fee',
            },
        ]
    }
}
