<template>
    <AppModalWrapper class="!w-fit">
        <form class="card border w-[432px]" @submit.prevent="submit">
            <div class="card__header card__header--small">
                <span class="card__title">
                    Add PQ
                </span>
                <AppButton
                    class="--ghost --square -mr-2.5"
                    type="button"
                    @click="close"
                >
                    <CloseIcon />
                </AppButton>
            </div>
            <div class="card__body grid grid-cols-2 gap-4">
                <FormField
                    :form="form"
                    field="lead_pk"
                    label="Lead ID"
                    required
                    class="col-span-2 text-xs font-medium"
                >
                    <InputSelect
                        v-model="form.data.lead_pk"
                        :options="leadOptions"
                        with-empty
                        search
                        size="small"
                        @search="handleLeadSearch"
                    />
                </FormField>

                <FormField
                    :form="form"
                    field="pq"
                    class="col-span-2 text-xs font-medium"
                >
                    <InputTextarea
                        v-model="form.data.pq"
                        size="small"
                        :rows="40"
                    />
                </FormField>

                <FormField
                    :form="form"
                    field="pcc"
                    label="PCC"
                    class="text-xs font-medium"
                >
                    <InputText
                        v-model="form.data.pcc"
                        size="small"
                    />
                </FormField>

                <FormField
                    :form="form"
                    field="pq_currency"
                    label="PQ currency"
                    class="text-xs font-medium"
                >
                    <InputText
                        v-model="form.data.pq_currency"
                        size="small"
                    />
                </FormField>
            </div>
            <div class="card__footer card__footer--small py-2">
                <AppButton
                    class="--small flex-1"
                    type="button"
                    @click="close"
                >
                    Cancel
                </AppButton>
                <AppButton class="--primary --small flex-1" @click="submit">
                    Continue process PQ
                </AppButton>
            </div>
        </form>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import CloseIcon from '@/assets/icons/CloseIcon.svg?component'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { getFullName } from '~/lib/Helper/PersonHelper'

defineOptions({
    name: 'PqPrepareModal',
    modal: {
        position: 'center',
    },
})

const props = defineProps<{
    formData?: {
        pq?: string,
        pcc?: string,
        pq_currency?: string,
        lead_pk?: PrimaryKey,
    },
    close(): void
}>()

const emit = defineEmits<{
    close: [],
}>()

const form = useForm<{
    lead_pk: PrimaryKey | undefined,
    pq: string | undefined,
    pq_currency: string | undefined,
    pcc: string | undefined,
}>({
    lead_pk: undefined,
    pq: '',
    pq_currency: undefined,
    pcc: undefined,
}, {
    lead_pk: ValidationRules.Required(),
    pq: ValidationRules.Required(),
    pq_currency: ValidationRules.Required(),
    pcc: ValidationRules.Required(),
})

const { useModel } = useContext()
const leadModel = useModel('Lead')
const leadList = leadModel.useList({ with: ['clientPreview']})

if (props.formData?.lead_pk) {
    await leadList.fetch({
        where: (and) => {
            and.eq('can_add_pq', true)
            and.eq('id', props.formData.lead_pk)
        },
    })
}

if (props.formData) {
    form.updateInitialData({
        lead_pk: leadList.records.length ? props.formData.lead_pk : undefined,
        pcc: props.formData.pcc,
        pq: props.formData.pq,
        pq_currency: props.formData.pq_currency,
    })
}

const submit = form.useSubmit(async (data) => {
    const modal = useModal((await import('@/components/Modals/priceQuote/PriceQuoteModal.vue')).default)
    await modal.open({
        lead_id: Number(data.lead_pk),
        prepopulateFrom: {
            option: data.pq,
            // carrier_code: data.pcc,
        },

    })
    emit('close')
})

const leadOptions = computed(() => {
    return leadList.records.map((lead) => ({
        title: `#${lead.pk} | ${getFullName(lead.clientPreview)}`,
        subtitle: `${lead.from_iata_code} -> ${lead.to_iata_code}`,
        value: lead.pk,
    }))
})

const handleLeadSearch = (query: string) => {
    query = query.trim()

    if (query.length < 1) {
        return
    }

    leadList.fetch({
        where: (and) => {
            and.eq('can_add_pq', true)
            and.search(query)
        },
    })
}
</script>
