<template>
    <div
        class="card bg-secondary-100 border border-secondary-200 border-1 rounded p-2 relative group w-fit pr-6"
        :class="{
            '!border-danger-100 !bg-danger-50 dark:!bg-danger-950': isDanger
        }"
    >
        <AppButton
            class="p-1.5 --only --xs absolute top-0 right-0 rounded-br-none rounded-tl-none hidden group-hover:flex"
            @click="copyToClipboard(segments.join('\n'))"
        >
            <CopyIcon />
        </AppButton>
        <ul class="list-disc list-outside flex flex-col gap-1">
            <li
                v-for="segment in segments"
                :key="segment"
                class="whitespace-normal text-2xs ml-4"
            >
                {{ segment }}
            </li>
        </ul>
    </div>
</template>

<script setup lang="ts">
import { copyToClipboard } from '@/lib/core/helper/ClipboardHelper'

defineProps<{
    segments: string[]
    isDanger?: boolean
}>()
</script>
