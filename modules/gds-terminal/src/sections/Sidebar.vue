<template>
    <div
        class="flex flex-col gap-1.5 relative"
        :class="{
            'ml-2': !minimized
        }"
    >
        <AppButton
            class="h-8 focus:ring-0 focus:outline-none flex justify-center gap-0.5 text-2xs leading-[14px] px-2 !text-secondary"
            :class="{
                'hidden': terminalHotkeyStore.isTerminalHotkeyBarOpened || minimized
            }"
            @click="terminalHotkeyStore.toggleHotkeyBar(true)"
        >
            <ChevronsLeftIcon />
            <span class="block">PF KEYS</span>
        </AppButton>
        <div
            ref="menu"
            :class="{
                'border border-secondary-100 h-full w-20 card flex flex-col justify-between': !minimized,
            }"
        >
            <div
                :class="{
                    'hidden': minimized
                }"
            >
                <Dropdown
                    placement="left-start"
                    :trigger="$refs.menu"
                >
                    <template #toggle="{ toggle, isActive }">
                        <AppButton
                            class="flex flex-col gap-0 h-14 w-full border-0 border-b focus:ring-0 focus:outline-none"
                            :class="{
                                'bg-secondary-50': isActive,
                                'bg-[#FCFCFD]': !isActive
                            }"
                            @click="toggle"
                        >
                            <div class="pt-1.5 text-secondary uppercase">
                                {{ new Date().toLocaleDateString(undefined, {weekday: 'short'}) }}
                            </div>
                            <div
                                class="font-semibold text-2xl text-secondary-600"
                                :class="{
                                    'text-secondary-900': isActive
                                }"
                            >
                                {{ new Date().getDate() }}
                            </div>
                        </AppButton>
                    </template>
                    <template #content>
                        <Calendar />
                    </template>
                </Dropdown>
                <div class="p-1.5 flex flex-col gap-1">
                    <Dropdown
                        placement="left-end"
                    >
                        <template #toggle="{ toggle, isActive }">
                            <AppButton
                                v-tooltip="{content: `Grid`, placement: 'left'}"
                                class="w-full h-10 focus:ring-0 focus:outline-none"
                                :class="{'bg-secondary-50': isActive}"
                                @click="toggle"
                            >
                                <Grid2Icon class="icon stroke-1" :class="{'text-secondary': !isActive}" />
                            </AppButton>
                        </template>
                        <template #content="{ close }">
                            <InputGrid
                                :model-value="toolStore.getActiveSessionStore().getLayout()"
                                class="mr-4"
                                @update:model-value="($event) => {toolStore.getActiveSessionStore().setLayout($event); close()}"
                            />
                        </template>
                    </Dropdown>
                    <Dropdown
                        placement="left-end"
                    >
                        <template #toggle="{ toggle, isActive }">
                            <AppButton
                                v-tooltip="{content: `History of Commands`, placement: 'left'}"
                                class="w-full h-10 focus:ring-0 focus:outline-none"
                                :class="{'bg-secondary-50': isActive}"
                                @click="toggle"
                            >
                                <IterationCwIcon class="icon stroke-1" :class="{'text-secondary': !isActive}" />
                            </AppButton>
                        </template>
                        <template #content="{ close }">
                            <HistoryCommands
                                style="width: 264px"
                                :close="close"
                                class="mr-4"
                            />
                        </template>
                    </Dropdown>
                    <Dropdown
                        placement="left-end"
                    >
                        <template #toggle="{ toggle, isActive }">
                            <AppButton
                                class="w-full h-10 focus:ring-0 focus:outline-none"
                                :class="{'bg-secondary-50': isActive}"
                                @click="toggle"
                            >
                                <div class="flex gap-x-0.5 items-center" :class="{'text-secondary': !isActive}">
                                    <CodeIcon class="rotate-90 icon stroke-1" />
                                    <span class="text-xs font-medium">Tools</span>
                                </div>
                            </AppButton>
                        </template>
                        <template #content>
                            <ToolsList
                                style="width: 264px"
                                class="mr-4"
                            />
                        </template>
                    </Dropdown>

                    <AppButton
                        v-if="toolStore.options.highlightProductionEnvironment"
                        class="w-full h-10 focus:ring-0 focus:outline-none new_btn"
                        :class="{
                            '--danger': isProdMode,
                        }"
                        @click="isProdMode = !isProdMode"
                    >
                        <span :class="[isProdMode ? 'text-white' : 'text-secondary']">
                            PROD
                        </span>
                    </AppButton>

                    <hr class="mt-1.5 mb-1">

                    <div class="text-secondary-400 text-2xs uppercase text-center font-semibold mb-1" style="letter-spacing: 0.1em;">
                        {{ activeGDS?.name }}
                    </div>

                    <AppButton
                        v-for="({ sessionStore, activePcc }, index) in availableSessionStores"
                        :key="index"
                        class="relative --primary text-xs h-11 !border focus:ring-0 focus:outline-none"
                        :class="{
                            '--outline !bg-primary-50 !border-primary-200': toolStore.getActiveSessionStore().gdsSession.name
                                !== sessionStore.gdsSession.name,
                        }"
                        @click="toolStore.setActiveSessionStore(sessionStore)"
                    >
                        <div
                            v-if="activePcc"
                            class="absolute text-3xs -right-1 -top-1 bg-white text-primary rounded-full px-1 h-[16px] text-center flex justify-center"
                        >
                            {{ activePcc }}
                        </div>

                        {{ sessionStore.gdsSession.name }}
                    </AppButton>
                </div>
            </div>
            <AppButton
                v-if="!minimized"
                class="flex gap-2 focus:ring-0 focus:outline-none border-t text-secondary h-9 w-20 px-0"
                @click="minimized = !minimized"
            >
                <Minimize2Icon class="block" />
                <span class="uppercase ">Menu</span>
            </AppButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import InputGrid from '../components/InputGrid.vue'
import Calendar from '../components/Calendar.vue'
import Grid2Icon from '../assets/icons/Grid2Icon.svg?component'
import IterationCwIcon from '../assets/icons/IterationCwIcon.svg?component'
import HistoryCommands from '~modules/gds-terminal/src/sections/HistoryCommands.vue'
import ToolsList from '~modules/gds-terminal/src/sections/ToolsList.vue'
import { useTerminalHotkeyStore } from '~modules/gds-terminal/src/piniaStore/useTerminalHotkeyStore'
import type TerminalTool from '~modules/gds-terminal/src/lib/TerminalTool'
import { TerminalEnvironment } from '~modules/gds-terminal/src/lib/TerminalTool'

defineOptions({
    name: 'Sidebar',
})

const toolStore = inject<TerminalTool>('terminalTool')!
const terminalHotkeyStore = useTerminalHotkeyStore()

const activeGDS = computed(() => toolStore.getActiveGds())

const areasSettings = computed(() => toolStore.getActiveGds().getGdsAppSettings().areas.value)

const availableSessionStores = computed(() => {
    if (!activeGDS.value) {
        return [] as never
    }

    return toolStore.getGDSSessionStores(activeGDS.value).map((sessionStore) => {
        const activePcc = sessionStore.activePcc.value ?? areasSettings.value?.[sessionStore.gdsSession.name]

        return {
            sessionStore,
            activePcc,
        }
    })
})

//

const minimized = ref(false)

//

const isProdMode = computed({
    get() {
        return toolStore.getEnvironment() === TerminalEnvironment.Production
    },
    set(isProd) {
        toolStore.setEnvironment(
            isProd
                ? TerminalEnvironment.Production
                : toolStore.options.defaultEnvironment,
        )
    },
})

defineExpose({
    minimized,
})
</script>
