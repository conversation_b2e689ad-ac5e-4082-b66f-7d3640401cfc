<template>
    <AppModalWrapper
        class="!max-w-[500px]"
        header="Edit PF Keys"
        close-button
    >
        <div class="card">
            <div class="card__body card__body--partholder">
                <div class="card card__body px-4 pt-3 pb-4 grid grid-cols-6 gap-1">
                    <AppButton
                        v-for="(hotkey, index) in hotkeys"
                        :key="index"
                        :class="[
                            hotkeyExists(hotkey) ? '--primary' : '--neutral',
                            {
                                '--soft': !arraysAreEqual(hotkey, selectedTerminalHotkey)
                            }
                        ]"
                        @click="selectHotkey(hotkey)"
                    >
                        {{ hotkey.map(toUpperCase).join(' + ') }}
                    </AppButton>
                </div>
                <form
                    v-if="selectedTerminalHotkey"
                    class="card card__body"
                    @submit.prevent="submit"
                >
                    <div class="grid grid-cols-2 gap-4">
                        <FormField
                            :form="form"
                            field="title"
                            label="Label"
                            required
                        >
                            <InputText v-model="form.data.title" placeholder="Short name for this macro" />
                        </FormField>

                        <div />
                        <!--                        <FormField-->
                        <!--                            :form="form"-->
                        <!--                            field="autorun"-->
                        <!--                            label=" "-->
                        <!--                        >-->
                        <!--                            <label class="flex gap-2 items-center mt-2">-->
                        <!--                                <InputCheckbox v-model="form.data.autorun" />-->
                        <!--                                <span class="block select-none">Autorun</span>-->
                        <!--                            </label>-->
                        <!--                        </FormField>-->

                        <FormField
                            :form="form"
                            field="command"
                            label="Command"
                            class="col-span-2"
                            required
                            :help="`Use ${commandSplitCharacter} to split commands and execute them one by one. Alt + Enter to insert ${commandSplitCharacter}. <br>You can use new lines for better readability.`"
                        >
                            <InputTextarea
                                ref="commandInput"
                                v-model="command"
                                placeholder="List of commands"
                                rows="6"
                                @keydown="updateHotKeyCommand($event)"
                            />
                        </FormField>

                        <FormField
                            :form="form"
                            field="description"
                            label="Description"
                            class="col-span-2"
                        >
                            <InputTextarea v-model="form.data.description" placeholder="Description" />
                            <span class="mt-1 text-secondary text-xs">
                                The description will appear as a tooltip
                            </span>
                        </FormField>
                    </div>
                </form>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-between gap-4">
                <AppButton
                    class="mr-auto"
                    :disabled="form.loading.value"
                    @click="$emit('close')"
                >
                    Close
                </AppButton>

                <AppButton
                    v-if="form.data.pk"
                    class="--danger-lite"
                    @click="removeBinding"
                >
                    Remove binding
                </AppButton>

                <AppButton
                    class="--primary"
                    :loading="form.loading.value"
                    @click="submit"
                >
                    Apply changes
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'

import { toUpperCase } from 'uri-js/dist/esnext/util'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'
import { arraysAreEqual } from '~/lib/Helper/ArrayHelper'
import { replaceEventKey, tryIdentifyExceptionKey } from '~modules/gds-terminal/src/helpers/InputHelper'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { useTerminalHotkeyStore } from '~modules/gds-terminal/src/piniaStore/useTerminalHotkeyStore'
import { commandSplitCharacter } from '~modules/gds-terminal/src/composables/terminal/useTerminalHotKeys'

const props = defineProps<{
    pcc: string
}>()

const hotkeys = [['F1'], ['F2'], ['F3'], ['F4'], ['F5'], ['F6'], ['F7'], ['F8'], ['F9'], ['F10'],
                 ['F11'], ['F12'], ['F13'], ['F14'], ['F15'], ['F16'], ['F17'], ['F18'], ['F19'], ['F20']]

const { useModel, currentUserPk } = useContext()

const terminalHotkeyModel = useModel('TerminalHotkey')
const terminalHotkeyStore = useTerminalHotkeyStore()

const terminalHotkeyList = computed(() => terminalHotkeyStore.getHotkeysByPcc(props.pcc))

const selectedTerminalHotkey = ref<string[]>([])

const form = useForm<{
    pk: PrimaryKey | undefined,
    title: string,
    autorun: boolean,
    command: string,
    description: string,
}>({
    pk: undefined,
    title: '',
    autorun: false,
    command: '',
    description: '',
}, {
    title: ValidationRules.Required(),
    command: ValidationRules.Required(),
})

const submit = form.useSubmit(async (data) => {
    if (data.pk) {
        await terminalHotkeyModel.actions.update({
            pk: data.pk,
            title: data.title,
            autorun: data.autorun,
            command: data.command,
            description: data.description,
            keys: selectedTerminalHotkey.value,
        })
    } else {
        const newPk = (await terminalHotkeyModel.actions.create({
            title: data.title,
            description: data.description,
            command: data.command,
            autorun: data.autorun,
            keys: selectedTerminalHotkey.value,
            consolidator_system_name: props.pcc,
        })).pk

        form.updateData({
            pk: newPk,
        })
    }

    toastSuccess('PF key updated successfully!')
}, {
    resetOnSuccess: false,
})

const hotkeyExists = (hotkey: string[]) => {
    return terminalHotkeyList.value.find((terminalHotkey) => arraysAreEqual(terminalHotkey.keys, hotkey))
}

const selectHotkey = (hotkey: string[]) => {
    selectedTerminalHotkey.value = hotkey

    const alreadyExisted = terminalHotkeyList.value.find((terminalHotkey) => arraysAreEqual(terminalHotkey.keys, hotkey))

    if (alreadyExisted) {
        form.updateInitialData({
            pk: alreadyExisted.pk,
            title: alreadyExisted.title,
            autorun: alreadyExisted.autorun,
            command: alreadyExisted.command,
            description: alreadyExisted.description,
        })
    } else {
        form.updateInitialData({
            pk: undefined,
            title: '',
            autorun: false,
            command: '',
            description: '',
        })
    }
}

const updateHotKeyCommand = (event: KeyboardEvent) => {
    const exceptionKeyToReplace = tryIdentifyExceptionKey(event)

    if (exceptionKeyToReplace) {
        event.preventDefault()
        replaceEventKey(event, exceptionKeyToReplace)

        return
    }

    if (event.key === 'Enter' && event.altKey) {
        event.preventDefault()
        form.data.command += '^\n'
    }
}

const commandInput = ref()

const command = computed({
    get() {
        return form.data.command
    },
    set(value: string) {
        const cursorPosition = commandInput.value?.input.selectionStart
        form.data.command = value.toUpperCase()

        nextTick(() => {
            if (cursorPosition) {
                commandInput.value?.input.setSelectionRange(cursorPosition, cursorPosition)
            }
        })
    },
})

onMounted(() => {
    selectHotkey(hotkeys[0])
})

async function removeBinding() {
    const pk = form.data.pk

    if (!pk) {
        return
    }

    await terminalHotkeyModel.actions.delete({ pk })

    await waitForResourceEvent('TerminalHotkeyList', 'update', currentUserPk)

    toastSuccess('PF key removed successfully!')

    form.updateInitialData({
        pk: undefined,
        title: '',
        autorun: false,
        command: '',
        description: '',
    })
}
</script>
