<template>
    <AppButton
        v-tooltip="{ content: !!enriched ? 'Client info' : 'No client data available'}"
        class="--only"
        :class="{
            '--outline --muted': isDark,
            'relative overflow-visible --warning': showAnimation && animate
        }"
        :disabled="!enriched"
        @click="openClientEnrichedInfoModal"
    >
        <div
            v-if="showAnimation && animate"
            class="absolute inline-flex w-full h-full rounded-[4px] bg-orange-300 opacity-75 pulse-button z-1"
        />
        <InfoIcon class="dark:text-white" />
    </AppButton>
</template>

<script setup lang="ts">
import ClientEnrichedInfoModal from '~/sections/Client/modals/ClientEnrichedInfoModal.vue'
import type { ModelAttributes } from '~types/lib/Model'

defineOptions({
    name: 'ClientEnrichedButton',
})

const props = withDefaults(defineProps<{
    clientPk: PrimaryKey,
    animate?: boolean,
}>(), {
    animate: false,
})

const { useModel, record: client, currentUserPk } = await useNewContext('ClientAdditionalInfo', props.clientPk)
const { isDark } = useDarkMode()
const enriched = ref<ModelAttributes<'ClientAdditionalEnrichmentInfo'>>()

const showAnimation = computed(() => {
    if (!enriched.value) {
        return false
    }

    return !enriched.value.has_seen
})

watchOnce(() => client.has_enriched_info, () => {
    fetchEnrichedInfo()
})

const fetchEnrichedInfo = async () => {
    if (!client.has_enriched_info || !currentUserPk || !props.clientPk) {
        return
    }

    const result = await useModel('ClientAdditionalInfo').actions.getEnrichedInfo({
        auth_pk: currentUserPk,
        pk: props.clientPk,
    })

    if (result?.data) {
        enriched.value = result
    }
}
fetchEnrichedInfo()

const clientEnrichedInfoModal = useModal(ClientEnrichedInfoModal)

function openClientEnrichedInfoModal() {
    if (!enriched.value) {
        return
    }
    clientEnrichedInfoModal.open({
        client: client,
        enriched: enriched.value,
    })
}
</script>

