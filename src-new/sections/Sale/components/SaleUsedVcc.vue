<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <div class="flex items-center justify-between mt-5">
                <h6 class="m-0">
                    Virtual Credit Cards (VCC)
                </h6>
            </div>
            <div class="card-old mt-2">
                <div class="card-old-body p-[12px] ">
                    <AppTable
                        :columns="columns"
                        :items="cards"
                        zebra
                        class="rounded overflow-hidden"
                    >
                        <template #not-found>
                            <div class="list-table-v2__not-found">
                                No Virtual Credit Cards created for this sale.
                            </div>
                        </template>
                        <template #body>
                            <tr
                                v-for="item in cards"
                                :key="item.id"
                            >
                                <td>
                                    **** - {{ item.strip.slice(-4) }}
                                </td>
                                <td>
                                    {{ item.card_name }}
                                </td>

                                <td>
                                    {{ $format.money(item.vccInfo.amount_limit, {withCurrency: 'USD'}) }}
                                </td>

                                <td>
                                    {{ $format.money(item.vccInfo.amount_to_pay, {withCurrency: 'USD'}) }}
                                </td>

                                <td>
                                    {{ $format.money(item.vccInfo.amount_used, {withCurrency: 'USD'}) }}
                                </td>

                                <td>
                                    <div class="flex gap-2 items-center justify-between">
                                        <div class="flex flex-grow">
                                            <div
                                                v-if="item.vccInfo.amount_used === 0"
                                                class="flex w-full gap-2 items-center justify-between text-secondary-400"
                                            >
                                                Not used
                                                <AlertCircleIcon class="w-3.5 h-3.5" />
                                            </div>
                                            <div
                                                v-else
                                                class="flex w-full gap-2 items-center justify-between"
                                            >
                                                {{ `Used - ${item.vccInfo.airline.code}` }}

                                                <CheckIcon class="text-success w-3.5 h-3.5" />
                                            </div>
                                        </div>

                                        <AppButton
                                            v-tooltip="'Preview'"
                                            class="--ghost --only flex-none --small"
                                            @click="previewCardDetails(item.pk)"
                                        >
                                            <EyeIcon />
                                        </AppButton>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </AppTable>
                </div>
            </div>
        </template>

        <template #fallback>
            <PlaceholderBlock class="h-[253px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import { useNewContext } from '~/composables/useNewContext'
import { ProjectCardCategory } from '~/api/models/Card/ProjectCard'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import VCCPreviewModal from '~/sections/Card/VirtualCard/modals/VCCPreviewModal.vue'

defineOptions({
    name: 'SaleUsedVcc',
})
const props = defineProps<{
    salePk: PrimaryKey,
}>()

const { useModel, useDictionary } = await useNewContext('Sale', props.salePk)
const airlineDictionary = useDictionary('Airline')

const saleRecord = useModel('Sale').useRecord({
    with: ['saleProjectCards', 'saleProjectCards.vccInfo', 'saleProjectCards.vccInfo.airline'],
})

const cards = computed(() => {
    return saleRecord.record.value.saleProjectCards.filter((card) => card.category === ProjectCardCategory.ProjectVirtualCard)
})

const suspense = useSuspensableComponent(async () => {
    await saleRecord.fetch(props.salePk)
})

const columns = useTableColumns({
    strip: {
        label: 'Card Number',
        width: '200px',
    },
    card_name: {
        label: 'Passenger Name',
    },
    amount_limit: {
        label: 'Limit',
        width: 10,
    },
    amount_to_pay: {
        label: 'To pay',
        width: 10,
    },
    amount_used: {
        label: 'Used',
        width: 10,
    },
    airline_pk: {
        label: 'Usage Status',
        width: '200px',
    },
})

const previewModal = useModal(VCCPreviewModal)

function previewCardDetails(pk: PrimaryKey) {
    previewModal.open({
        cardPk: pk,
    })
}
</script>
