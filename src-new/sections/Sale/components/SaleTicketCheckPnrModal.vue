<template>
    <AppModalWrapper
        close-button
        :header="header"
        class="!max-w-[830px]"
    >
        <div class="p-2 border-t bg-secondary-50 flex flex-col gap-y-2 dark:bg-dark-2">
            <div class="card p-3">
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <div
                            v-if="errorMessage"
                            class="text-danger flex items-center justify-center bg-white dark:bg-dark-3 rounded p-2"
                        >
                            Error: {{ errorMessage }}
                        </div>

                        <div
                            v-else-if="!checkPnrFields.data.external_number"
                            class="border rounded flex items-center gap-2 p-2 border-warning-500 bg-warning-50 text-xs"
                        >
                            <div class="rounded-full w-[28px] h-[28px]  bg-warning-100 flex justify-center items-center">
                                <div class="rounded-full w-[20px] h-[20px] bg-warning-200 flex justify-center items-center">
                                    <AlertTriangleIcon class="text-warning icon w-[14px] h-[14px]" />
                                </div>
                            </div>
                            <div class="text-xs ">
                                <div class="font-semibold">
                                    Ticket number is required.
                                </div>
                                <div class="text-secondary">
                                    Please select from the dropdown.
                                </div>
                            </div>
                        </div>

                        <div
                            v-else-if="haveCompareAlerts"
                            class="flex items-center gap-2 text-secondary dark:text-secondary-100 bg-white rounded p-2 dark:bg-dark-3"
                        >
                            <AlertCircleIcon />
                            <div class="text-xs">
                                Updating will replace the current ticket details with the latest GDS data
                            </div>
                        </div>

                        <div v-else class="flex items-center justify-between bg-white rounded p-2 dark:bg-dark-3">
                            <div class="font-semibold text-xs">
                                Comparison of ticket data with the latest GDS information
                            </div>
                        </div>
                    </div>
                    <div>
                        <div
                            v-if="selectTicketOptions.length"
                            class="flex items-center bg-white rounded p-2 dark:bg-dark-3"
                        >
                            <InputSelect
                                v-model="selectTicketValue"
                                class="w-full"
                                :options="selectTicketOptions"
                                placeholder="Select ticket"
                                @update:model-value="seedForm"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="!errorMessage">
                <SuspenseManual :state="suspense">
                    <div class="grid grid-cols-2 gap-x-2">
                        <div
                            v-for="(ticket, index) in records"
                            :key="index"
                            class="px-5 py-4 bg-white dark:bg-dark-3 rounded flex flex-col gap-y-3"
                        >
                            <div class="font-semibold">
                                {{ ticket.description }}
                            </div>

                            <!--   PAX-->
                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        First name:
                                    </div>

                                    <InputText
                                        v-if="index === 0"
                                        :model-value="ticket.passenger_first_name "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.passenger_first_name ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputText
                                        v-else
                                        v-model="checkPnrFields.data.passenger_first_name"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.passenger_first_name"
                                        :input-attrs="{
                                            class: compareAlert.passenger_first_name ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div
                                        v-if="compareAlert.passenger_first_name"
                                        class="text-danger absolute right-2 top-2"
                                    >
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.passenger_first_name && index === 1"
                                    v-model="fieldsToUpdate.passenger_first_name"
                                    class="dark:border-secondary-300"
                                />
                            </div>
                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Last name:
                                    </div>
                                    <InputText
                                        v-if="index === 0"
                                        :model-value="ticket.passenger_last_name "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.passenger_last_name ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputText
                                        v-else
                                        v-model="checkPnrFields.data.passenger_last_name"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.passenger_last_name"
                                        :input-attrs="{
                                            class: compareAlert.passenger_last_name ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div
                                        v-if="compareAlert.passenger_last_name"
                                        class="text-danger absolute right-2 top-2"
                                    >
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.passenger_last_name && index === 1"
                                    v-model="fieldsToUpdate.passenger_last_name"
                                    class="dark:border-secondary-300"
                                />
                            </div>
                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Mid. name:
                                    </div>
                                    <InputText
                                        v-if="index === 0"
                                        :model-value="ticket.passenger_middle_name "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.passenger_middle_name ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputText
                                        v-else
                                        v-model="checkPnrFields.data.passenger_middle_name"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.passenger_middle_name"
                                        :input-attrs="{
                                            class: compareAlert.passenger_middle_name ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div
                                        v-if="compareAlert.passenger_middle_name"
                                        class="text-danger absolute right-2 top-2"
                                    >
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.passenger_middle_name && index === 1"
                                    v-model="fieldsToUpdate.passenger_middle_name"
                                    class="dark:border-secondary-300"
                                />
                            </div>
                            <div class="flex items-center gap-x-2">
                                <div class="flex flex-col w-full">
                                    <div class="w-full relative flex items-center gap-x-2">
                                        <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                            Birth Date:
                                        </div>
                                        <InputDate
                                            v-if="index === 0"
                                            :timestamp="ticket.passenger_birthday_at"
                                            readonly
                                            picker-mode="date"
                                            class="w-full"
                                            :input-attrs="{
                                                class: compareAlert.passenger_birthday_at ? '!border-danger-200 dark:!border-danger-500' : ''
                                            }"
                                        />
                                        <InputDate
                                            v-else
                                            v-model:timestamp="checkPnrFields.data.passenger_birthday_at"
                                            picker-mode="date"
                                            class="w-full"
                                            :readonly="fieldsToUpdate.passenger_birthday_at"
                                            :input-attrs="{
                                                class: compareAlert.passenger_birthday_at ? '!border-danger-200 dark:!border-danger-500' : ''
                                            }"
                                        />

                                        <div
                                            v-if="compareAlert.passenger_birthday_at"
                                            class="text-danger absolute right-2 top-2"
                                        >
                                            <AlertCircleIcon />
                                        </div>
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.passenger_birthday_at && index === 1"
                                    v-model="fieldsToUpdate.passenger_birthday_at"
                                    class="dark:border-secondary-300"
                                />
                            </div>
                            <!--   PAX-->

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Tkt. No:
                                    </div>
                                    <InputText
                                        v-if="index === 0"
                                        :model-value="ticket.external_number "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.external_number ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputText
                                        v-else
                                        v-model="checkPnrFields.data.external_number"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.external_number"
                                        :input-attrs="{
                                            class: compareAlert.external_number ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div v-if="compareAlert.external_number" class="text-danger absolute right-2 top-2">
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.external_number && index === 1"
                                    v-model="fieldsToUpdate.external_number"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="flex flex-col w-full">
                                    <div class="w-full relative flex items-center gap-x-2">
                                        <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                            Issued at:
                                        </div>
                                        <InputDate
                                            v-if="index === 0"
                                            :timestamp="ticket.issued_at"
                                            readonly
                                            picker-mode="datetime"
                                            class="w-full"
                                            :input-attrs="{
                                                class: compareAlert.issued_at ? '!border-danger-200 dark:!border-danger-500' : ''
                                            }"
                                        />
                                        <InputDate
                                            v-else
                                            v-model:timestamp="checkPnrFields.data.issued_at"
                                            picker-mode="datetime"
                                            :readonly="fieldsToUpdate.issued_at"
                                            class="w-full"
                                            :input-attrs="{
                                                class: compareAlert.issued_at ? '!border-danger-200 dark:!border-danger-500' : ''
                                            }"
                                        />

                                        <div v-if="compareAlert.issued_at" class="text-danger absolute right-2 top-2">
                                            <AlertCircleIcon />
                                        </div>
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.issued_at && index === 1"
                                    v-model="fieldsToUpdate.issued_at"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Net price:
                                    </div>

                                    <InputMoney
                                        v-if="index === 0"
                                        :model-value="ticket.net_price"
                                        class="w-full"
                                        readonly
                                    />
                                    <InputMoney
                                        v-else
                                        v-model="checkPnrFields.data.net_price"
                                        class="w-full"
                                        readonly
                                    />

                                    <div v-if="compareAlert.net_price" class="text-info-400 absolute right-2 top-2">
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Tax:
                                    </div>
                                    <InputMoney
                                        v-if="index === 0"
                                        :model-value="ticket.tax "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.tax ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputMoney
                                        v-else
                                        v-model="checkPnrFields.data.tax"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.tax"
                                        :input-attrs="{
                                            class: compareAlert.tax ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div v-if="compareAlert.tax" class="text-danger absolute right-2 top-2">
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.tax && index === 1"
                                    v-model="fieldsToUpdate.tax"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Fare:
                                    </div>
                                    <InputMoney
                                        v-if="index === 0"
                                        :model-value="ticket.fare "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.fare ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputMoney
                                        v-else
                                        v-model="checkPnrFields.data.fare"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.fare"
                                        :input-attrs="{
                                            class: compareAlert.fare ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div v-if="compareAlert.fare" class="text-danger absolute right-2 top-2">
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.fare && index === 1"
                                    v-model="fieldsToUpdate.fare"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        CK:
                                    </div>
                                    <InputMoney
                                        v-if="index === 0"
                                        :model-value="ticket.check_payment "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.check_payment ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputMoney
                                        v-else
                                        v-model="checkPnrFields.data.check_payment"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.check_payment"
                                        :input-attrs="{
                                            class: compareAlert.check_payment ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div v-if="compareAlert.check_payment" class="text-danger absolute right-2 top-2">
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.check_payment && index === 1"
                                    v-model="fieldsToUpdate.check_payment"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Comm.:
                                    </div>
                                    <InputMoney
                                        v-if="index === 0"
                                        :model-value="ticket.commission "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.commission ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputMoney
                                        v-else
                                        v-model="checkPnrFields.data.commission"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.commission"
                                        :input-attrs="{
                                            class: compareAlert.commission ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div v-if="compareAlert.commission" class="text-danger absolute right-2 top-2">
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.commission && index === 1"
                                    v-model="fieldsToUpdate.commission"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Iss.fee:
                                    </div>
                                    <InputMoney
                                        v-if="index === 0"
                                        :model-value="ticket.issuing_fee "
                                        class="w-full"
                                        readonly
                                        :input-attrs="{
                                            class: compareAlert.issuing_fee ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />
                                    <InputMoney
                                        v-else
                                        v-model="checkPnrFields.data.issuing_fee"
                                        class="w-full"
                                        :readonly="fieldsToUpdate.issuing_fee"
                                        :input-attrs="{
                                            class: compareAlert.issuing_fee ? '!border-danger-200 dark:!border-danger-500' : ''
                                        }"
                                    />

                                    <div v-if="compareAlert.issuing_fee" class="text-danger absolute right-2 top-2">
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.issuing_fee && index === 1"
                                    v-model="fieldsToUpdate.issuing_fee"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        Carrier:
                                    </div>
                                    <InputSelect
                                        v-if="index === 0"
                                        :model-value="ticket.validating_carrier_pk"
                                        class="w-full"
                                        :class="{
                                            '!border-danger-200 dark:!border-danger-500': compareAlert.validating_carrier_pk
                                        }"
                                        :options="airlineOptionsForSelect"
                                        readonly
                                    />
                                    <InputSelect
                                        v-else
                                        v-model="checkPnrFields.data.validating_carrier_pk"
                                        class="w-full pr-6"
                                        :options="airlineOptionsForSelect"
                                        :readonly="fieldsToUpdate.validating_carrier_pk"
                                        :class="{
                                            '!border-danger-200 dark:!border-danger-500': compareAlert.validating_carrier_pk
                                        }"
                                    />

                                    <div
                                        v-if="compareAlert.validating_carrier_pk"
                                        class="text-danger absolute right-2 top-2"
                                    >
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.validating_carrier_pk && index === 1"
                                    v-model="fieldsToUpdate.validating_carrier_pk"
                                    class="dark:border-secondary-300"
                                />
                            </div>

                            <div class="flex items-center gap-x-2">
                                <div class="w-full relative flex items-center gap-x-2">
                                    <div class="text-secondary text-xs dark:text-secondary-100 w-[80px]">
                                        PCC:
                                    </div>
                                    <InputSelect
                                        v-if="index === 0"
                                        :model-value="ticket.consolidator_area_pk"
                                        class="w-full"
                                        :class="{
                                            '!border-danger-200 dark:!border-danger-500': compareAlert.consolidator_area_pk
                                        }"
                                        :options="consolidatorAreaOptionsForSelect"
                                        readonly
                                    />
                                    <InputSelect
                                        v-else
                                        v-model="checkPnrFields.data.consolidator_area_pk"
                                        class="w-full pr-6"
                                        :readonly="fieldsToUpdate.consolidator_area_pk"
                                        :options="consolidatorAreaOptionsForSelect"
                                        :class="{
                                            '!border-danger-200 dark:!border-danger-500': compareAlert.consolidator_area_pk
                                        }"
                                    />

                                    <div
                                        v-if="compareAlert.consolidator_area_pk"
                                        class="text-danger absolute right-2 top-2"
                                    >
                                        <AlertCircleIcon />
                                    </div>
                                </div>
                                <InputCheckbox
                                    v-if="compareAlert.consolidator_area_pk && index === 1"
                                    v-model="fieldsToUpdate.consolidator_area_pk"
                                    class="dark:border-secondary-300"
                                />
                            </div>
                        </div>
                    </div>

                    <template #fallback>
                        <PlaceholderBlock class="w-full h-[650px]" />
                    </template>
                </SuspenseManual>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-between">
                <AppButton @click="emit('close')">
                    Close
                </AppButton>

                <AppButton
                    class="--primary"
                    :disabled="!!errorMessage || !canSubmit"
                    @click="submit"
                >
                    Update ticket
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import type z from 'zod'
import type { CheckPnrResponse } from '~/api/models/Sale/SaleVersion'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { AlertCircleIcon } from '@zhuowenli/vue-feather-icons'
import type SelectOption from '~types/structures/SelectOption'
import { getFullName } from '~/lib/Helper/PersonHelper'

defineOptions({
    name: 'SaleTicketCheckPnrModal',
})

const props = defineProps<{
    salePk: PrimaryKey,
    ticketPk: PrimaryKey,
    consolidatorAreaPk?: PrimaryKey | null,
    pnr: string
}>()

const emit = defineEmits<{
    close: []
}>()

const { useModel, useDictionary } = await useNewContext('Sale', props.salePk)

const header = computed(() => {
    return `Check PNR: ${props.pnr} in Sale #${props.salePk}`
})

const selectTicketValue = ref<string>()
const selectTicketOptions = computed(() => {
    return checkTickets.value.map((ticket) => {
        return {
            title: `${getFullName({
                first_name: ticket.passenger_first_name,
                last_name: ticket.passenger_last_name,
                middle_name: ticket.passenger_middle_name,
            })} ${useService('formatter').date(ticket.passenger_birthday_at, 'UTC', { full: true })} (${ticket.passenger_type})`,
            subtitle: `Tkt. no: ${ticket.external_number}`,
            value: ticket.external_number,
        } as SelectOption
    })
})

type CheckPnrData = z.infer<typeof CheckPnrResponse>
const airlineDictionary = useDictionary('Airline')

const airlineOptionsForSelect = computed(() => {
    return airlineDictionary.mapRecords.forSelectWithCode()
})

const consolidatorAreaDictionary = useDictionary('ConsolidatorArea')

const consolidatorAreaOptionsForSelect = computed(() => {
    return consolidatorAreaDictionary.mapRecords.forSelect()
})

const suspense = useSuspensableComponent(async () => {
    await checkPnr()
})

const records = computed(() => {
    const result = [{ ...currentTicket.value, description: 'Current Ticket' }]

    if (!!errorMessage.value || !selectTicketValue.value) {
        return result
    }
    result.push({
        ...checkTicket.value,
        description: 'Check',
    })

    return result
})

const currentTicket = ref<CheckPnrData | null>()
const checkTickets = ref<CheckPnrData[]>([])
const checkTicket = ref<CheckPnrData | null>()
const errorMessage = ref<string | null>()

const haveCompareAlerts = computed(() => {
    return Object.keys(compareAlert.value).length > 0
})

const compareAlert = computed(() => {
    const result = {}

    if (!!errorMessage.value) {
        return result
    }

    if (checkTicket.value) {
        for (const [key, value] of Object.entries(checkPnrFields.data)) {
            if (!value) {
                result[key] = false
            } else if (key === 'passenger_birthday_at' && currentTicket.value) {
                const newDate = Date.fromUnixTimestamp(value).toFormatUTC('dd/MM/yyyy')
                const oldDate = Date.fromUnixTimestamp(currentTicket.value[key]).toFormatUTC('dd/MM/yyyy')

                result[key] = newDate !== oldDate
            } else if (key === 'issued_at' && currentTicket.value) {
                const newDate = Date.fromUnixTimestamp(value).toFormatUTC('dd/MM/yyyy HH:mm')
                const oldDate = Date.fromUnixTimestamp(currentTicket.value[key]).toFormatUTC('dd/MM/yyyy HH:mm')

                result[key] = newDate !== oldDate
            } else if (currentTicket.value) {
                result[key] = currentTicket.value[key] !== value
            }
        }
    }

    return result as { [key: keyof CheckPnrData]: boolean }
})

// forms

const checkPnrFields = useForm<{
    external_number: string | undefined,
    issued_at: number | undefined,
    net_price: number | undefined,
    tax: number | undefined,
    fare: number | undefined,
    check_payment: number | undefined,
    commission: number | undefined,
    issuing_fee: number | undefined,
    validating_carrier_pk: PrimaryKey | undefined,
    consolidator_area_pk: PrimaryKey | undefined,
    passenger_first_name: string | undefined,
    passenger_last_name: string | undefined,
    passenger_middle_name: string | undefined,
    passenger_birthday_at: number | undefined,
    passenger_type: string | undefined,
}>({
    external_number: undefined,
    issued_at: undefined,
    net_price: 0,
    tax: 0,
    fare: 0,
    check_payment: 0,
    commission: 0,
    issuing_fee: 0,
    validating_carrier_pk: undefined,
    consolidator_area_pk: undefined,
    passenger_first_name: undefined,
    passenger_last_name: undefined,
    passenger_middle_name: undefined,
    passenger_birthday_at: undefined,
    passenger_type: undefined,
})

const fieldsToUpdate = reactive({
    external_number: false,
    issued_at: false,
    net_price: false,
    tax: false,
    fare: false,
    check_payment: false,
    commission: false,
    issuing_fee: false,
    validating_carrier_pk: false,
    consolidator_area_pk: false,
    passenger_first_name: false,
    passenger_last_name: false,
    passenger_middle_name: false,
    passenger_birthday_at: false,
    passenger_type: false,
})

// auto update net price

watch([() => checkPnrFields.data.fare, () => checkPnrFields.data.tax], () => {
    const fare = checkPnrFields.data.fare || 0
    const tax = checkPnrFields.data.tax || 0
    checkPnrFields.data.net_price = tax + fare
})

async function checkPnr() {
    const { current, tickets, error_message } = await useModel('SaleVersion').actions.checkPnr({
        ticket_pk: props.ticketPk,
        consolidator_area_pk: props.consolidatorAreaPk,
        pnr: props.pnr,
    })
    currentTicket.value = current

    errorMessage.value = error_message
    checkTickets.value = tickets ? [...tickets] : []
}

function seedForm(value: string) {
    const ticket = checkTickets.value.find((ticket) => ticket.external_number === value)

    if (!ticket) {
        return
    }
    checkTicket.value = ticket
    checkPnrFields.updateInitialData(checkTicket.value)
}

//

type CheckPnrResponseType = z.infer<typeof CheckPnrResponse>

const canSubmit = computed(() => {
    return Object.keys(fieldsToUpdate).some(key => !!fieldsToUpdate[key])
})

const submit = checkPnrFields.useSubmit(async () => {
    if (errorMessage.value) {
        return
    }

    const updateData = Object.entries(fieldsToUpdate).reduce((acc, cur) => {
        if (cur[1]) {
            acc[cur[0]] = checkPnrFields.data[cur[0]]
        }

        return acc
    }, {})

    await useModel('SaleVersion').actions.applyCheckPnr({
        ticket_pk: props.ticketPk,
        data: updateData as CheckPnrResponseType,
    })

    toastSuccess('Ticket was updated')
    emit('close')
})
</script>

