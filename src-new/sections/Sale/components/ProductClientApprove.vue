<template>
    <div class="flex gap-2 justify-end items-center">
        <div v-if="clientApprove && !clientApprove?.is_sent_at && !clientApprove?.is_error" v-tooltip="'Processing'">
            <Loader />
        </div>
        <div
            v-if="clientApprove?.is_error"
            v-tooltip="'Something went wrong'"
            class="text-danger text-xs"
        >
            <AlertTriangleIcon />
        </div>

        <div
            v-if="clientApprove?.is_approved_at"
            v-tooltip="'Mail approved by client'"
            class="text-success text-xs"
        >
            <MailViewedIcon />
        </div>

        <div
            v-else-if="clientApprove?.is_sent_at"
            v-tooltip="'Mail sent'"
            class="text-primary-1 text-xs"
        >
            <MailSentIcon />
        </div>

        <div
            v-else-if="clientApprove?.is_viewed_at "
            v-tooltip="'Mail viewed'"
            class="text-primary-1 text-xs"
        >
            <MailViewedIcon />
        </div>

        <AppButton
            v-if="canEditSale && !isSaleAdjusted && !isSaleClosed"
            v-tooltip="'Send to client for approve'"
            class="--ghost --only --sm"
            @click="sendForApproveAdditionalExpense()"
        >
            <MailIcon />
        </AppButton>
    </div>
    <a
        v-if="clientApprove?.offer_url"
        :href="clientApprove.offer_url"
        target="_blank"
        class="text-primary-1 dark:text-blue-400 cursor-pointer text-xs text-center block"
    >
        Offer #{{ clientApprove?.id }}
    </a>
</template>

<script setup lang="ts">
import type { CreditCard } from '~/lib/Helper/CreditCardHelper'
import MailSentIcon from '~/assets/icons/MailSentIcon.svg?component'
import MailViewedIcon from '~/assets/icons/MailViewedIcon.svg?component'
import { toastError } from '@/lib/core/helper/ToastHelper'
import AdditionalExpensesSendForApproveModal from '~/sections/Sale/modals/AdditionalExpensesSendForApproveModal.vue'
import type { ModelAttributes } from '~types/lib/Model'

defineOptions({
    name: 'ProductClientApprove',
})

const props = defineProps<{
    salePk: PrimaryKey,
    saleVersionPk: PrimaryKey,
    productPk: PrimaryKey,
    creditCards: CreditCard[]
    isSaleAdjusted: boolean
    isSaleClosed: boolean
}>()

const { useModel, record: sale, hasPermission } = await useNewContext('Sale', props.salePk)

const {
    record: product,
    fetch: productFetch,
} = useModel('Product').useRecord({
    with: ['clientApprove'],
}).destructable()

const suspense = useSuspensableComponent(async () => {
    await productFetch(props.productPk)
})

const clientApprove = computed(() => {
    return product.value?.clientApprove || undefined as ModelAttributes<'ProductClientApprove'>
})

const canEditSale = computed(() => {
    return hasPermission('edit', 'Sale', sale)
})

const sendProductModal = useModal(AdditionalExpensesSendForApproveModal)

function sendForApproveAdditionalExpense() {
    if (props.isSaleAdjusted) {
        toastError('Sale adjusted!')

        return
    }

    sendProductModal.open({
        salePk: props.salePk,
        saleVersionPk: props.saleVersionPk,
        productPk: props.productPk,
        sellPrice: product.value.sell_price,
        creditCards: props.creditCards,
    })
}
</script>

