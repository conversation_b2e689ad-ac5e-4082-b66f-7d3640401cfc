<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <div class="flex items-center justify-between mt-5">
                <h6 class="m-0">
                    Extra profits
                </h6>
                <!--                <button-->
                <!--                    class="btn btn-sm btn-outline-secondary box ml-auto cursor-pointer"-->
                <!--                    @click="addExtraProfit"-->
                <!--                >-->
                <!--                    <PlusIcon class="w-4 h-4 mr-1.5" />-->
                <!--                    Add extra profit-->
                <!--                </button>-->
            </div>
            <div class="card-old mt-2">
                <div class="card-old-body p-5">
                    <AppTable
                        :columns="columns"
                        :items="form.data.extra_profits"
                        zebra
                    >
                        <template #body>
                            <tr
                                v-for="item in form.data.extra_profits"
                                :key="item.id"
                            >
                                <td>
                                    {{ item.id }}
                                </td>

                                <td>
                                    <FormField
                                        v-if="editExtraProfit === item.id"
                                        :form="form"
                                        :field="'created_at'"
                                    >
                                        <InputDate
                                            v-model:timestamp="item.created_at"
                                            size="small"
                                        />
                                    </FormField>
                                    <span v-else>{{ item?.created_at ? $format.date(item.created_at) : '----' }}</span>
                                </td>

                                <td>
                                    <FormField
                                        v-if="editExtraProfit === item.id"
                                        :form="form"
                                        :field="'amount'"
                                    >
                                        <InputMoney
                                            v-model="item.amount"
                                            size="small"
                                        />
                                    </FormField>

                                    <span v-else>{{ $format.money(item.amount) }}</span>
                                </td>

                                <td>
                                    {{ item.product_pk }}
                                </td>

                                <td>
                                    <FormField
                                        v-if="editExtraProfit === item.id"
                                        :form="form"
                                        :field="'field_type'"
                                    >
                                        <InputSelect
                                            v-model="item.field_type"
                                            :options="productFieldTypeOptions"
                                            size="small"
                                        />
                                    </FormField>

                                    <span v-else>{{ getProductFieldOption(item.field_type)?.title || '----' }}</span>
                                </td>

                                <td>
                                    <FormField
                                        v-if="editExtraProfit === item.id"
                                        :form="form"
                                        :field="'remark'"
                                        class="w-[200px]"
                                    >
                                        <InputText
                                            v-model="item.remark"
                                            size="small"
                                        />
                                    </FormField>

                                    <div
                                        v-else
                                        v-tooltip="`${item.remark}`"
                                        class="max-w-[200px] truncate"
                                    >
                                        {{ item.remark }}
                                    </div>
                                </td>

                                <td>
                                    <div class="flex justify-end">
                                        <AppButton
                                            v-if="editExtraProfit === item.id"
                                            class="--secondary --ghost --only"
                                            @click="stopEdit"
                                        >
                                            <XIcon />
                                        </AppButton>
                                        <AppButton
                                            v-if="editExtraProfit === item.id"
                                            class="--secondary --ghost --only"
                                            @click="saveExtraProfit(item)"
                                        >
                                            <SaveIcon />
                                        </AppButton>
                                        <AppButton
                                            v-if="editExtraProfit !== item.id"
                                            class="--primary --ghost --only"
                                            @click="startEdit(item.id)"
                                        >
                                            <Edit2Icon />
                                        </AppButton>
                                        <AppButton
                                            v-if="editExtraProfit !== item.id"
                                            class="--danger --ghost --only"
                                            @click="deleteExtraProfit(item.id)"
                                        >
                                            <Trash2Icon />
                                        </AppButton>
                                    </div>
                                </td>
                            </tr>

                            <tr class="text-primary-1">
                                <td colspan="2">
                                    <span class="text-gray-500">Positive:</span> {{ $format.money(totalPositive) }}
                                </td>
                                <td colspan="2">
                                    <span class="text-gray-500">Negative:</span> {{ $format.money(totalNegative) }}
                                </td>
                                <td colspan="2" />
                                <td colspan="2">
                                    <span class="text-gray-500">Total:</span>
                                    {{ $format.money(totalPositive + totalNegative) }}
                                </td>
                            </tr>
                        </template>
                    </AppTable>
                </div>
            </div>
        </template>

        <template #fallback>
            <PlaceholderBlock class="h-[253px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import { useNewContext } from '~/composables/useNewContext'
import type { ProductFieldType } from '~/api/models/Product/ProductAdditionalFieldAmount'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import FormField from '~/components/Form/FormField.vue'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'

defineOptions({
    name: 'SaleExtraProfits',
})
const props = defineProps<{
    salePk: PrimaryKey,
}>()

const { useModel } = await useNewContext('Sale', props.salePk)

const list = useModel('SaleExtraProfit').useList({
    with: ['sale'],
    where: (q) => {
        q.eq('sale_pk', props.salePk)
    },
})

type ExtraProfitItem = {
    id: number,
    amount: number,
    field_type: ProductFieldType,
    remark: string,
    created_at: number | undefined
    product_pk: PrimaryKey
}

const form = useForm({
    extra_profits: [] as ExtraProfitItem[],
})

const runFetchAction = async () => {
    await list.fetch()

    form.updateInitialData({
        extra_profits: list.records.map((record) => {
            return {
                id: record.id,
                amount: record.amount,
                field_type: record.field_type,
                remark: record.remark,
                created_at: record?.created_at || undefined,
                product_pk: record.product_pk,
            }
        }),
    })
}

const suspense = useSuspensableComponent(async () => {
    await runFetchAction()
})

const productFieldTypeDictionary = useGeneralDictionary('ProductFieldType')

const productFieldTypeOptions = computed(() => {
    return productFieldTypeDictionary.mapRecordsForExtraProfitsFilter.forSelect()
})

function getProductFieldOption(field_type: ProductFieldType) {
    return productFieldTypeDictionary.find(field_type)
}

const columns = useTableColumns({
    id: {
        label: 'ID',
        width: 'min',
    },
    created_at: {
        label: 'Created at',
        width: 'min',
    },
    amount: {
        label: 'Amount',
        width: 'min',
    },
    product_pk: {
        label: 'Product #',
        width: 'min',
    },
    field_type: {
        label: 'Type',
        width: 'min',
    },
    remark: {
        label: 'Remark',
        width: 'min',
    },
    actions: {
        label: '',
        width: 'min',
    },
})

const totalPositive = computed(() => {
    let result = 0
    form.data.extra_profits.forEach(record => {
        if (record.amount >= 0) {
            result += record.amount
        }
    })

    return result
})

const totalNegative = computed(() => {
    let result = 0
    form.data.extra_profits.forEach(record => {
        if (record.amount < 0) {
            result += record.amount
        }
    })

    return result
})

const editExtraProfit = ref<number>()

function startEdit(id: number) {
    if (form.hasChanges) {
        form.reset()
    }
    editExtraProfit.value = id
}

function stopEdit() {
    editExtraProfit.value = undefined
}

async function deleteExtraProfit(id: number) {
    await $confirm(
        {
            text: 'You are trying to delete Sale Extra Profit',
            confirmButton: 'Confirm',
        },
    )
    await useModel('SaleExtraProfit').actions.delete({
        pk: String(id),
    })
    toastSuccess('Extra profit deleted')

    await runFetchAction()
}

async function saveExtraProfit(record: ExtraProfitItem) {
    await useModel('SaleExtraProfit').actions.save({
        pk: String(record.id),
        data: {
            amount: record.amount,
            field_type: record.field_type,
            remark: record.remark,
            created_at: record?.created_at || null,
            product_pk: record.product_pk,
        },
    })
    toastSuccess('Extra profit saved')
    stopEdit()
    await runFetchAction()
}
</script>

