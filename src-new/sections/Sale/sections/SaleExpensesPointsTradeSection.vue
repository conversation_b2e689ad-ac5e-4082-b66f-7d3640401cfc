<template>
    <div class="">
        <div class="form-label text-xs font-medium text-gray-600">
            Select award accounts*
        </div>
        <div class="card__body card__body--partholder !bg-transparent">
            <div
                v-for="(awardAccount, i) in form.data.records"
                :key="i"
                class="card card__body relative dark:border dark:border-secondary-600 flex gap-3"
            >
                <FormField
                    :form="form"
                    :field="`records.${i}.award_account_pk`"
                    label="Account №"
                    class="text-xs flex-[4.2]"
                    required
                    hide-error
                >
                    <InputSelect
                        v-model="awardAccount.award_account_pk"
                        :options="awardAccountsOptions"
                        placeholder="Search"
                        size="small"
                        search
                        sync-on-options-change
                        @search="handleSearch"
                    />
                </FormField>

                <FormField
                    :form="form"
                    :field="`records.${i}.miles_count`"
                    label="Miles"
                    class="text-xs flex-[2]"
                    required
                    hide-error
                >
                    <InputMoney
                        v-model="awardAccount.miles_count"
                        step="10"
                        size="small"
                    />
                </FormField>

                <FormField
                    :form="form"
                    :field="`records.${i}.price_per_mile`"
                    label="Price per point"
                    class="text-xs flex-[2]"
                    required
                    hide-error
                >
                    <InputMoney
                        v-model="awardAccount.price_per_mile"
                        size="small"
                        :step="0.000001"
                    />
                </FormField>
                <div
                    class="absolute -top-2 -right-2 bg-white dark:bg-dark-1 cursor-pointer text-danger shadow-md p-2 border-transparent badge --xs --rounded  --only"
                    @click="removeAwardAccount(Number(i))"
                >
                    <XIcon />
                </div>
            </div>
            <div v-if="noAwardAccountError" class="text-center text-xs text-danger">
                {{ noAwardAccountError }}
            </div>
            <AppButton
                class="flex --small gap-2 border-dotted --primary bg-white text-primary w-full dark:bg-dark-1 dark:border-secondary-300"
                @click="addAwardAccount"
            >
                Add award account <PlusIcon />
            </AppButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'

type PointsTradeItem = {
    award_account_pk: PrimaryKey,
    miles_count: number,
    price_per_mile: number,
}

const { useModel } = useContext()

const form = useForm<{
    records: PointsTradeItem[]
}>({
    records: [],
}, {
    records: [
        ValidationRules.Required('Please add at least one award account'),
        ValidationRules.Array({
            award_account_pk: ValidationRules.Required(),
            miles_count: ValidationRules.Required(),
            price_per_mile: ValidationRules.Required(),
        }),
    ],
})

const noAwardAccountError = computed(() => {
    return form.errors.get('records')[0] ?? ''
})

//

const awardAccountsList = useModel('AwardAccount').useList()

const selectedAwardAccountPks = computed(() => {
    return form.data.records.map((record) => record.award_account_pk).filter(Boolean)
})

const awardAccountsOptions = computed(() => awardAccountsList.records
    .map((account) => ({
        title: account.account_number,
        value: usePk(account),
    })))

const handleSearch = (query: string) => {
    query = query.trim()

    if (query.length < 1) {
        return
    }
    awardAccountsList.fetch({
        where: (and) => {
            and.or(condition => {
                if (selectedAwardAccountPks.value.length) {
                    condition.in('id', selectedAwardAccountPks.value)
                }
                condition.search(query)
            })
        },
    })
}

const addAwardAccount = () => {
    form.data.records.push({
        award_account_pk: '',
        miles_count: 0,
        price_per_mile: 0,
    })

    form.errors.clear('records')
}

const removeAwardAccount = (index: number) => {
    form.data.records = form.data.records.filter((_, i) => i !== index)
}

const validateForm = () => {
    return form.validate()
}

const getPointsTrade = (): PointsTradeItem[] => {
    return form.data.records
}

defineExpose({
    validateForm,
    getPointsTrade,
})
</script>
