<template>
    <AppModalWrapper header="Add miles" class="!max-w-[400px]">
        <form class="p-4 border-t grid grid-cols-2 gap-4" @submit.prevent="submit">
            <FormField
                :form="form"
                :field="'consolidator_pk'"
                label="Consolidator"
                class="col-span-2"
            >
                <InputSelect
                    v-model="form.data.consolidator_pk"
                    :options="consolidatorOptions"
                    :placeholder="'Select consolidator'"
                />
            </FormField>

            <FormField
                v-if="showAreaField"
                :form="form"
                :field="'consolidator_area_pk'"
                label="Consolidator area"
                class="col-span-2"
            >
                <InputSelect
                    v-model="form.data.consolidator_area_pk"
                    :options="consolidatorAreaOptions"
                    :placeholder="'Select consolidator area'"
                />
            </FormField>

            <FormField
                :form="form"
                :field="'amount'"
                label="Miles amount"
            >
                <InputMoney
                    v-model="form.data.amount"
                    step="10"
                    autofocus
                />
            </FormField>

            <FormField
                :form="form"
                :field="'price'"
                label="Cost per mile (CPM)"
            >
                <InputMoney
                    v-model="form.data.cpm"
                    show-currency
                    :step="0.00001"
                />
            </FormField>

            <FormField
                :form="form"
                :field="'remark'"
                label="Remark"
                class="col-span-2"
            >
                <InputTextarea v-model="form.data.remark" />
            </FormField>
        </form>

        <template #footer="{ close }">
            <div class="flex items-center gap-4 justify-between">
                <AppButton @click="close">
                    Cancel
                </AppButton>

                <div v-if="total !== null" class="w-[fit-content] ml-auto">
                    Total price: <b>{{ $format.money(total, { withCurrency: true }) }}</b>
                </div>

                <AppButton
                    class="--primary"
                    :disabled="total === null"
                    @click="submit"
                >
                    Add
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup lang="ts">
import FormField from '~/components/Form/FormField.vue'
import ValidationRules from '~/lib/ValidationRule/ValidationRules'

defineOptions({
    name: 'AwardAccountAddMilesModal',
    modal: {
        promise: true,
    },
})

const props = defineProps<{
    accountPk: PrimaryKey,
    consolidatorPk?: PrimaryKey
}>()

const emit = defineEmits<{
    close: [],
    resolve: [],
}>()

//

const { useModel } = await useNewContext('AwardAccount', props.accountPk)

//
// TODO: validation
const form = useForm({
    amount: 0,
    cpm: 0,
    remark: '',
    consolidator_pk: props.consolidatorPk || undefined,
    consolidator_area_pk: undefined,
}, {
    consolidator_area_pk: ValidationRules.RequiredWhen(() => showAreaField.value, 'Consolidator area is required'),
    // amount: ValidationRules.Required(),
    // cpm: ValidationRules.Required(),
    remark: ValidationRules.Required(),
})

//

const submit = form.useSubmit(async () => {
    await useModel('AwardAccount').actions.addMiles({
        pk: props.accountPk,
        data: {
            consolidator_pk: form.data.consolidator_pk || null,
            consolidator_area_pk: form.data.consolidator_area_pk || null,
            amount: form.data.amount,
            remark: form.data.remark,
            cpm: form.data.cpm || 0,
        },
    })

    useService('event').emit('refreshActivityLog')

    emit('resolve')
})

//

const consolidatorAreaDictionary = useGeneralDictionary('ConsolidatorArea')

const consolidatorAreaOptions = consolidatorAreaDictionary.mapInhouseRecords.forSelect()

const showAreaField = computed((): boolean => {
    return form.data.consolidator_pk === consolidatorAreaDictionary.inhouseConsolidatorPk
})

const consolidatorOptions = useGeneralDictionary('Consolidator').mapAwardRecords.forSelect()

// TODO: price can be 0
const total = computed(() => {
    const amount = form.data.amount
    const price = form.data.cpm

    if (!amount) {
        return null
    }

    return Math.ceil(price * amount * 100) / 100
})
</script>

