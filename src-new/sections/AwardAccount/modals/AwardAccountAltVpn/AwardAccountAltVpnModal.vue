<template>
    <AppModalWrapper
        class="!max-w-[720px]"
        close-button
        :header="isEditMode ? 'Edit VPN' : 'Create VPN'"
    >
        <div class="card__body card__body--partholder p-2">
            <form class="flex flex-col gap-5">
                <div class="card">
                    <div class="card__header">
                        Proxy settings
                        <div class="flex gap-4 text-xs">
                            <label class="cursor-pointer flex items-center gap-2">
                                <InputRadio
                                    v-model="isCustomProxy"
                                    name="proxy_type"
                                    :value="false"
                                />
                                Default
                            </label>

                            <label class="cursor-pointer flex items-center gap-2">
                                <InputRadio
                                    v-model="isCustomProxy"
                                    name="proxy_type"
                                    :value="true"
                                />
                                Custom
                            </label>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4 card__body">
                        <FormField
                            :form="form"
                            field="name"
                            label="Name"
                            required
                        >
                            <InputText
                                v-model="form.data.name"
                                placeholder="Name"
                            />
                        </FormField>
                        <FormField
                            :form="form"
                            field="country"
                            label="Country"
                        >
                            <InputSelect
                                v-model="form.data.country"
                                placeholder="Random"
                                with-empty="Random"
                                :options="countryOptions"
                                filter
                                @change="resetLocationData"
                            />
                        </FormField>
                        <FormField
                            :form="form"
                            field="region"
                            label="Region"
                        >
                            <InputSelect
                                v-model="form.data.region"
                                placeholder="Random"
                                :options="regionOptions"
                                with-empty="Random"
                                filter
                            />
                        </FormField>
                        <FormField
                            :form="form"
                            field="city"
                            label="City"
                        >
                            <InputSelect
                                v-model="form.data.city"
                                placeholder="Random"
                                :options="cityOptions"
                                filter
                                with-empty="Random"
                                @change="updateRegionFromCity($event)"
                            />
                        </FormField>
                        <FormField
                            :form="form"
                            field="note"
                            label="Note"
                            class="col-span-2"
                        >
                            <InputTextarea
                                v-model="form.data.note"
                            />
                        </FormField>
                    </div>
                </div>

                <div v-if="isCustomProxy" class="card card__body gap-4 grid grid-cols-6 auto-rows-auto">
                    <div class="col-span-2">
                        <FormField
                            :form="form"
                            field="protocol"
                            label="Protocol"
                            required
                        >
                            <InputSelect
                                v-model="form.data.protocol"
                                placeholder="Select protocol"
                                :options="[{title: 'HTTP', value: 'http'}, {title: 'SOCKS5', value: 'socks5'}]"
                                with-empty
                            />
                        </FormField>
                    </div>
                    <div class="col-span-2">
                        <FormField
                            :form="form"
                            field="username"
                            label="Username"
                            required
                        >
                            <InputText
                                v-model="form.data.username"
                                placeholder="Username"
                            />
                        </FormField>
                    </div>
                    <div class="col-span-2">
                        <FormField
                            :form="form"
                            field="password"
                            label="Password"
                            required
                        >
                            <InputText
                                v-model="form.data.password"
                                placeholder="Password"
                            />
                        </FormField>
                    </div>

                    <div class="col-span-3">
                        <FormField
                            :form="form"
                            field="host"
                            label="Host"
                            required
                        >
                            <InputText
                                v-model="form.data.host"
                                placeholder="Host"
                            />
                        </FormField>
                    </div>
                    <div class="col-span-3">
                        <FormField
                            :form="form"
                            field="port"
                            label="Port"
                            required
                        >
                            <InputNumber
                                v-model="form.data.port"
                                placeholder="Port"
                            />
                        </FormField>
                    </div>

                    <AppButton
                        class="--outline --primary col-span-3"
                        type="button"
                        :loading="proxyGenerateIsActive"
                        @click="generateProxy"
                    >
                        Generate proxy
                    </AppButton>

                    <AppButton
                        class="--outline --success col-span-3"
                        type="button"
                        :loading="proxyCheckIsActive"
                        @click="checkProxy"
                    >
                        Check proxy
                    </AppButton>

                    <AppTip
                        v-if="isProxyChecked && isProxyValid"
                        class="tip--small col-span-3 items-center py-0"
                        type="success"
                    >
                        <span>Proxy is valid</span>
                    </AppTip>
                    <AppTip
                        v-else-if="isProxyChecked && !isProxyValid"
                        class="tip--small col-span-3 items-center py-0"
                        type="danger"
                    >
                        <span>Proxy is invalid</span>
                    </AppTip>
                </div>

                <div class="card">
                    <div class="card__header">
                        <span class="card__title">
                            Advanced Settings
                        </span>
                        <AppButton
                            type="button"
                            class="--primary --soft"
                            @click="isShowedAdvancedSettings = !isShowedAdvancedSettings"
                        >
                            Show more <ChevronDownIcon :class="{'rotate-180': isShowedAdvancedSettings}" />
                        </AppButton>
                    </div>

                    <div v-if="isShowedAdvancedSettings" class="card__body grid grid-cols-2 gap-x-5">
                        <div class="flex flex-col gap-5">
                            <div class="flex flex-col gap-1">
                                <FormField
                                    :form="form"
                                    field="screen_masking"
                                >
                                    <label class="form-label flex gap-2 items-center">
                                        Screen Resolution <HelpCircleIcon v-tooltip="`Pick a value that matches or is smaller than your device's native size. For teams, use the lowest resolution among members. Keep the window maximized for best results.`" class="icon --small text-secondary-400" />
                                    </label>
                                    <InputSelect
                                        v-model="form.data.screen_masking"
                                        :options="screenResolutionTypeOptions"
                                    />
                                </FormField>

                                <FormField
                                    v-if="form.data.screen_masking === 'custom'"
                                    :form="form"
                                    field="screen_resolution_custom"
                                    class="p-2 border rounded border-secondary-100 bg-[#FCFCFD] dark:bg-dark-4"
                                >
                                    <InputSelect
                                        v-model="form.data.screen_resolution_custom"
                                        with-empty
                                        placeholder="Select"
                                        :options="screenResolutionOptions"
                                    />
                                </FormField>
                            </div>

                            <FormField
                                :form="form"
                                field="media_devices_masking"
                            >
                                <label class="form-label flex gap-2 items-center">
                                    Media devices <HelpCircleIcon v-tooltip="`Pick &quot;Real&quot; if you need to use the camera in your profile.`" class="icon --small text-secondary-400" />
                                </label>
                                <InputSelect
                                    v-model="form.data.media_devices_masking"
                                    :options="mediaDeviceTypeOptions"
                                />
                            </FormField>

                            <div v-if="form.data.media_devices_masking === 'custom'" class="flex gap-3 p-2 border rounded border-secondary-100 bg-[#FCFCFD] dark:bg-dark-4">
                                <FormField
                                    :form="form"
                                    field="video_input_count"
                                    label="Video input"
                                    class="text-xs"
                                >
                                    <InputNumber
                                        v-model="form.data.video_input_count"
                                        size="small"
                                        with-buttons
                                        :min="0"
                                        :max="1"
                                    />
                                    <span class="text-secondary-500 text-2xs">
                                        From 0 to 1
                                    </span>
                                </FormField>

                                <FormField
                                    :form="form"
                                    field="audio_input_count"
                                    label="Audio input"
                                    class="text-xs"
                                >
                                    <InputNumber
                                        v-model="form.data.audio_input_count"
                                        size="small"
                                        with-buttons
                                        :min="1"
                                        :max="4"
                                    />
                                    <span class="text-secondary-500 text-2xs">
                                        From 1 to 4
                                    </span>
                                </FormField>

                                <FormField
                                    :form="form"
                                    field="audio_output_count"
                                    label="Audio output"
                                    class="text-xs"
                                >
                                    <InputNumber
                                        v-model="form.data.audio_output_count"
                                        size="small"
                                        with-buttons
                                        :min="1"
                                        :max="4"
                                    />
                                    <span class="text-secondary-500 text-2xs">
                                        From 1 to 4
                                    </span>
                                </FormField>
                            </div>

                            <FormField
                                :form="form"
                                field="graphics_masking"
                            >
                                <label class="form-label">
                                    WebGL metadata
                                </label>
                                <InputSelect
                                    v-model="form.data.graphics_masking"
                                    :options="metadataTypeOptions"
                                />
                            </FormField>

                            <FormField
                                :form="form"
                                field="graphics_noise"
                            >
                                <label class="form-label">
                                    WebGL graphics
                                </label>
                                <InputSelect
                                    v-model="form.data.graphics_noise"
                                    :options="webGlTypeOptions"
                                />
                            </FormField>

                            <FormField
                                :form="form"
                                field="canvas_noise"
                            >
                                <label class="form-label">
                                    Canvas graphics
                                </label>
                                <InputSelect
                                    v-model="form.data.canvas_noise"
                                    :options="canvasTypeOptions"
                                />
                            </FormField>

                            <FormField
                                :form="form"
                                field="audio_masking"
                            >
                                <label class="form-label">
                                    AudioContext
                                </label>
                                <InputSelect
                                    v-model="form.data.audio_masking"
                                    :options="audioContextTypeOptions"
                                />
                            </FormField>
                        </div>
                        <div class="flex flex-col gap-5">
                            <FormField
                                :form="form"
                                field="navigator_masking"
                            >
                                <label class="form-label flex gap-2 items-center">
                                    Navigator <HelpCircleIcon v-tooltip="`Similar navigator_masking parameters across profiles are normal and help you blend in with regular users.`" class="icon --small text-secondary-400" />
                                </label>
                                <InputSelect
                                    v-model="form.data.navigator_masking"
                                    :options="navigatorMaskingTypeOptions"
                                />
                            </FormField>

                            <div v-if="form.data.navigator_masking === 'custom'" class="grid grid-cols-2 gap-3 p-2 border rounded border-secondary-100 bg-[#FCFCFD] text-xs dark:bg-dark-4">
                                <FormField
                                    :form="form"
                                    field="user_agent"
                                    class="col-span-2"
                                    label="User-Agent"
                                >
                                    <InputText v-model="form.data.user_agent" />
                                </FormField>

                                <FormField
                                    :form="form"
                                    field="platform"
                                    label="Platform"
                                >
                                    <InputText v-model="form.data.platform" />
                                </FormField>

                                <FormField
                                    :form="form"
                                    field="hardware_concurrency"
                                    label="Hardware Concurrency"
                                >
                                    <InputSelect v-model="form.data.hardware_concurrency" :options="hardwareConcurrencyOptions" />
                                </FormField>

                                <FormField
                                    :form="form"
                                    field="oscpu"
                                    class="col-span-2"
                                    label="OSCPU (optional)"
                                >
                                    <InputText v-model="form.data.oscpu" />
                                </FormField>
                            </div>

                            <FormField
                                :form="form"
                                field="ports_masking"
                            >
                                <label class="form-label">
                                    Port scan protection
                                </label>
                                <InputSelect
                                    v-model="form.data.ports_masking"
                                    :options="portScanProtectionTypeOptions"
                                />
                            </FormField>

                            <div v-if="form.data.ports_masking === 'custom'" class="p-2 border rounded border-secondary-100 bg-[#FCFCFD] text-xs dark:bg-dark-4">
                                <FormField
                                    :form="form"
                                    field="whitelisted_ports"
                                    label="Whitelisted ports (comma-separated)"
                                >
                                    <InputTextarea
                                        v-model="form.data.whitelisted_ports"
                                    />
                                </FormField>
                            </div>

                            <FormField
                                :form="form"
                                field="fonts_masking"
                            >
                                <label class="form-label flex gap-2 items-center">
                                    Font data <HelpCircleIcon v-tooltip="`Similar Font data parameters across profiles are normal and help you blend in with regular users.`" class="icon --small text-secondary-400" />
                                </label>
                                <InputSelect
                                    v-model="form.data.fonts_masking"
                                    :options="fontDataTypeOptions"
                                />
                            </FormField>

                            <FormField
                                :form="form"
                                field="browser"
                            >
                                <label class="form-label flex gap-2 items-center">
                                    Browser <HelpCircleIcon v-tooltip="`Mimic is built on Chrome and Stealthfox – on Firefox.`" class="icon --small text-secondary-400" />
                                </label>
                                <InputSelect
                                    v-model="form.data.browser_type"
                                    :disabled="isEditMode"
                                    :options="browserOptions"
                                />
                            </FormField>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <template #footer>
            <div class="flex justify-end gap-4">
                <AppButton type="button" @click="$emit('close')">
                    Cancel
                </AppButton>
                <AppButton
                    class="--primary"
                    type="button"
                    :loading="form.loading"
                    @click="submit"
                >
                    {{ isEditMode ? "Save" : "Create" }}
                </AppButton>
            </div>
        </template>
    </AppModalWrapper>
</template>

<script setup async lang="ts">
import { computed, ref } from 'vue'
import { useVpnOptions } from '~/sections/AwardAccount/modals/AwardAccountAltVpn/composable/useVpnOptions'
import { useVpnForm } from '~/sections/AwardAccount/modals/AwardAccountAltVpn/composable/useVpnForm'
import { useLocationOptions } from '~/sections/AwardAccount/modals/AwardAccountAltVpn/composable/useLocationOptions'
import { ExternalUserAgentStatus } from '~/api/models/ExternalUserAgent/ExternalUserAgent'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { ExternalUserAgentProxyType } from '~/api/models/ExternalUserAgent/ExternalUserAgentProxy'
import FormField from '~/components/Form/FormField.vue'
import { preventDuplication } from '~/lib/Helper/PromiseHelper'

defineOptions({
    name: 'AwardAccountAltVpnModal',
    modal: { promise: true },
})

const props = withDefaults(defineProps<{
    externalUserAgentPk?: PrimaryKey
    awardAccountPk?: PrimaryKey
    skipApprove?: boolean
}>(), {
    externalUserAgentPk: undefined,
    awardAccountPk: undefined,
    skipApprove: false,
})

const emit = defineEmits<{
    'resolve': [external_user_agent_pk: PrimaryKey]
    'close': []
}>()

// State
const isEditMode = computed(() => !!props.externalUserAgentPk)
const isCustomProxy = ref(false)
const isShowedAdvancedSettings = ref(false)

// Context and services
const { useModel } = useContext()
const externalUserAgentModel = useModel('ExternalUserAgent')
const externalUserAgentProxyModel = useModel('ExternalUserAgentProxy')
const multilogin = useService('multilogin')

// Setup form and options
const { form } = useVpnForm(isCustomProxy)
const { countryOptions, regionOptions, cityOptions, resetLocationData, resetCredentialData, updateRegionFromCity } = await useLocationOptions(form, isCustomProxy)
const {
    metadataTypeOptions,
    webGlTypeOptions,
    screenResolutionTypeOptions,
    mediaDeviceTypeOptions,
    navigatorMaskingTypeOptions,
    portScanProtectionTypeOptions,
    canvasTypeOptions,
    audioContextTypeOptions,
    fontDataTypeOptions,
    screenResolutionOptions,
    hardwareConcurrencyOptions,
    browserOptions,
} = useVpnOptions()

// Form submission
const createAdvancedSettings = (data: any) => ({
    screen_masking: data.screen_masking,
    media_devices_masking: data.media_devices_masking,
    graphics_noise: data.graphics_noise,
    graphics_masking: data.graphics_masking,
    canvas_noise: data.canvas_noise,
    audio_masking: data.audio_masking,
    navigator_masking: data.navigator_masking,
    ports_masking: data.ports_masking,
    fonts_masking: data.fonts_masking,
    browser_type: data.browser_type,
    screen_resolution_custom: data.screen_resolution_custom ?? null,
    video_input_count: data.video_input_count ?? null,
    audio_input_count: data.audio_input_count ?? null,
    audio_output_count: data.audio_output_count ?? null,
    user_agent: data.user_agent ?? null,
    platform: data.platform ?? null,
    hardware_concurrency: data.hardware_concurrency ?? null,
    oscpu: data.oscpu ?? null,
    whitelisted_ports: data.whitelisted_ports ?? null,
})

const submit = form.useSubmit(async (data) => {
    const proxy = {
        type: isCustomProxy.value ? ExternalUserAgentProxyType.NodeMaven : ExternalUserAgentProxyType.Multilogin,
        location: data.country ? {
            country: data.country,
            city: data.city?.value ?? null,
            region: data.region ?? null,
        } : null,

        credential: isCustomProxy.value ? {
            protocol: data.protocol!,
            username: data.username!,
            password: data.password!,
            host: data.host!,
            port: data.port!,
        } : null,
    }

    if (isEditMode.value && props.externalUserAgentPk) {
        await externalUserAgentModel.actions.save({
            pk: props.externalUserAgentPk,
            note: data.note,
            name: data.name,
            advanced_settings: createAdvancedSettings(data),
            proxy: proxy,
        })

        toastSuccess('Alt VPN Saved')
        emit('close')
    } else {
        const result = await externalUserAgentModel.actions.create({
            award_account_pk: props.awardAccountPk,
            name: data.name,
            note: data.note,
            advanced_settings: createAdvancedSettings(data),
            proxy: proxy,
        })

        const externalUserAgent = await externalUserAgentModel
            .useRecord()
            .fetch(result.pk)

        if (externalUserAgent.value.status !== ExternalUserAgentStatus.Approved) {
            // @ts-ignore
            multilogin.login(externalUserAgent.value, { skipApprove: props.skipApprove })
        }

        emit('resolve', result.pk)
        emit('close')
    }
})

async function loadExistingVpnData(externalUserAgentPk: PrimaryKey) {
    const externalUserAgent = await externalUserAgentModel
        .useRecord({ with: ['proxy']})
        .fetch(externalUserAgentPk)

    let locationData: {
        country: null | string,
        region: null | string,
        city: null | string,
    } = {
        country: null,
        region: null,
        city: null,
    }

    let credentialData: {
        protocol: string | null,
        username: string | null,
        password: string | null,
        host: string | null,
        port: number | null,
    } = {
        protocol: null,
        username: null,
        password: null,
        host: null,
        port: null,
    }

    if (externalUserAgent.value.proxy) {
        isCustomProxy.value = externalUserAgent.value.proxy.type === ExternalUserAgentProxyType.NodeMaven
        locationData = externalUserAgent.value.proxy.location

        if (externalUserAgent.value.proxy.credential) {
            credentialData = externalUserAgent.value.proxy.credential
        }
    }

    form.updateInitialData({
        name: externalUserAgent.value.name,

        country: locationData.country === 'any' ? null : locationData.country,
        region: locationData.region,
        city: locationData.city && locationData.region ? {
            value: locationData.city,
            region: locationData.region,
        } : null,

        protocol: credentialData.protocol,
        username: credentialData.username,
        password: credentialData.password,
        host: credentialData.host,
        port: credentialData.port,

        note: externalUserAgent.value.note,

        ...externalUserAgent.value.advanced_settings,
    })
}

async function initializeNewVpn(awardAccountPk: PrimaryKey) {
    const awardAccount = await useModel('AwardAccount')
        .useRecord()
        .fetch(awardAccountPk)

    form.updateInitialData({
        name: `VPN ${awardAccount.value.account_number}`,
        country: 'us',
        region: null,
        city: null,
        note: '',
        screen_masking: 'mask',

        protocol: null,
        username: null,
        password: null,
        host: null,
        port: null,

        media_devices_masking: 'mask',
        video_input_count: 0,
        audio_input_count: 1,
        audio_output_count: 1,

        graphics_noise: 'mask',
        graphics_masking: 'mask',
        canvas_noise: 'mask',
        audio_masking: 'natural',

        navigator_masking: 'mask',

        ports_masking: 'mask',

        fonts_masking: 'mask',
        browser_type: 'stealthfox',
    })
}

if (props.externalUserAgentPk) {
    await loadExistingVpnData(props.externalUserAgentPk)
} else if (props.awardAccountPk) {
    await initializeNewVpn(props.awardAccountPk)
}

watch(isCustomProxy, () => {
    resetCredentialData()
    resetLocationData()
})

const proxyCheckIsActive = ref(false)
const proxyGenerateIsActive = ref(false)
const isProxyValid = ref(false)
const isProxyChecked = ref(false)

const checkProxy = async () => {
    await preventDuplication(async () => {
        try {
            await multilogin.checkProxy({
                type: form.data.protocol,
                host: form.data.host,
                port: form.data.port,
                username: form.data.username,
                password: form.data.password,
            })
            isProxyValid.value = true
        } catch (error) {
            isProxyValid.value = false
        } finally {
            isProxyChecked.value = true
        }
    }, proxyCheckIsActive)
}

const generateProxy = async () => {
    await preventDuplication(async () => {
        if (!form.validate({
            country: form.data.country,
            region: form.data.region,
            city: form.data.city,
        })) {
            return
        }

        const proxyParams = {
            type: isCustomProxy.value ? ExternalUserAgentProxyType.NodeMaven : ExternalUserAgentProxyType.Multilogin,
            protocol: form.data.protocol ?? null,
            location: form.data.country ? {
                country: form.data.country,
                city: form.data.city?.value ?? null,
                region: form.data.region ?? null,
            } : null,
        }

        const proxy = await externalUserAgentProxyModel.actions.generateProxy(proxyParams)

        form.data.host = proxy.host
        form.data.port = proxy.port
        form.data.username = proxy.username
        form.data.password = proxy.password
        form.data.protocol = proxy.protocol

        form.errors.clear()
    }, proxyGenerateIsActive)
}
</script>
