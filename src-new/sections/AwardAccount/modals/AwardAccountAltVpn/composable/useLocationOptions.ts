export type City = {
    value: string
    region: string
}

export async function useLocationOptions(form: any, isCustomProxy: Ref<boolean>) {
    const { useModel } = useContext()
    const externalUserAgentProxyModel = useModel('ExternalUserAgentProxy')

    const {
        multilogin_countries: multiloginCountries,
        node_maven_countries: nodeMavenCountries,
    } = await externalUserAgentProxyModel.actions.getProxyLocationOptions()

    const countryOptions = computed(() =>
        isCustomProxy.value ? nodeMavenCountries : multiloginCountries,
    )

    const selectedCountry = computed(() =>
        countryOptions.value.find(country => country.value === form.data.country),
    )

    const regionOptions = computed(() =>
        selectedCountry.value?.regions || [],
    )

    const cityOptions = computed(() => {
        const cities = selectedCountry.value?.cities || []

        const filteredCities = form.data.region
            ? cities.filter(city => city.region === form.data.region)
            : cities

        return filteredCities.map(city => ({
            title: city.title,
            value: {
                value: city.value,
                region: city.region,
            },
        }))
    })

    function resetLocationData() {
        form.data.city = null
        form.data.region = null
    }

    function resetCredentialData() {
        form.data.protocol = 'socks5'
        form.data.password = null
        form.data.username = null
        form.data.port = null
        form.data.host = null
    }

    function updateRegionFromCity(city: City) {
        if (city?.region && form.data.region !== city.region) {
            form.data.region = city.region
        }
    }

    return {
        countryOptions,
        regionOptions,
        cityOptions,
        resetLocationData,
        resetCredentialData,
        updateRegionFromCity,
    }
}
