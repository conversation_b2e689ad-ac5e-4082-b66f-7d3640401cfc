<template>
    <tr
        v-bind="$attrs"
        class="h-10"
        :class="{
            'border-b': showOutgoingRequests,
            'table-tr--highlighted': showOutgoingRequests,
        }"
        @dblclick="toggleShowOutgoingRequests"
    >
        <td>
            <span class="badge --success --small --soft --outline">
                Top up
            </span>
        </td>
        <td>{{ request.id }}</td>
        <td class="truncate">
            {{ consolidatorName }}
        </td>
        <td>
            {{ $format.money(request.balance, { fraction: 0 }) }}
        </td>
        <td
            :class="{
                'line-through': request.is_deleted
            }"
        >
            {{ $format.money(request.miles_amount, { fraction: 0 }) }}
        </td>
        <td
            :class="{
                'line-through': request.is_deleted
            }"
        >
            {{ $format.money(request.price, { fraction: 2 }) }}
        </td>
        <td>{{ $format.money(request.price / request.miles_amount, { fraction: 5 }) }}</td>
        <td @dblclick.stop>
            <EditableText
                class="max-w-[50ch]"
                :model-value="request.remark"
                :on-save="saveRemark"
            />
        </td>
        <td>{{ $format.datetime(request.created_at) }}</td>
        <td class="text-2xs !py-0">
            <div v-if="request.product" class="flex items-center gap-2">
                <ModelInfo :model="{ model_name: 'Sale', model_pk: request.product.sale_pk }" />
                <div class="leading-tight">
                    <div>
                        <span class="text-secondary">Product:</span> <span class="select-all">#{{ request.product_pk }}</span>
                    </div>
                </div>
            </div>
        </td>
        <td class="text-right !py-0">
            <AppButton
                v-if="request.balance === request.miles_amount"
                v-tooltip="'Reject request'"
                class="--danger --ghost --only"
                @click="deleteRequest"
            >
                <Trash2Icon />
            </AppButton>
            <AppButton
                v-if="outgoingRequests.length"
                v-tooltip="'Outgoing requests'"
                class="--primary --ghost --only"
                @click="toggleShowOutgoingRequests"
            >
                <ChevronDownIcon v-if="!showOutgoingRequests" />
                <ChevronUpIcon v-else />
            </AppButton>
        </td>
    </tr>
    <template v-if="showOutgoingRequests">
        <AwardAccountRequestListSectionOutgoingRequest
            v-for="outgoingRequest in outgoingRequests"
            :key="outgoingRequest.pk"
            :request="outgoingRequest"
        />
    </template>
</template>

<script setup lang="ts">
import type { ModelRef } from '~types/lib/Model'
import AwardAccountRequestListSectionOutgoingRequest
    from '~/sections/AwardAccount/RequestList/AwardAccountRequestListSectionOutgoingRequest.vue'
import EditableText from '~/components/Input/Editable/EditableText.vue'
import AwardAccountDeleteIncomingRequestModal
    from '~/sections/AwardAccount/modals/AwardAccountDeleteIncomingRequestModal.vue'
import ModelInfo from '~/components/Model/ModelInfo.vue'

defineOptions({
    name: 'AwardAccountRequestListSectionIncomingRequest',
    inheritAttrs: false,
})

const props = defineProps<{
    request: ModelRef<'AwardAccountIncomingRequest', 'product'>
    outgoingRequests: ModelRef<'AwardAccountOutgoingRequest', 'product'>[]
}>()

//

const { useModel, useDictionary } = useContext()

//

const consolidatorName = computed(() => {
    return useDictionary('Consolidator').find(props.request.consolidator_pk)?.name
})
//

const showOutgoingRequests = ref(false)

function toggleShowOutgoingRequests() {
    showOutgoingRequests.value = !showOutgoingRequests.value
}

//

async function saveRemark(remark: string) {
    await useModel('AwardAccountIncomingRequest').actions.editRemark({
        pk: usePk(props.request),
        remark,
    })
}

const deleteRequestModal = useModal(AwardAccountDeleteIncomingRequestModal)
const deleteRequest = () => {
    deleteRequestModal.open({
        pk: usePk(props.request),
    })
}
</script>
