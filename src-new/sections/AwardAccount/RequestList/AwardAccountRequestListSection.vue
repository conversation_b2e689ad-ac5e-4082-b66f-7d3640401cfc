<template>
    <div class="fancy-scroll overflow-y-auto">
        <SuspenseManual :state="suspense">
            <template #default>
                <AppTable
                    :columns="columns"
                    :items="incomingRequests"
                >
                    <template #body>
                        <AwardAccountRequestListSectionIncomingRequest
                            v-for="(request, $i) in incomingRequests"
                            :key="request.pk"
                            :class="zebra[$i]"
                            :request="request"
                            :outgoing-requests="getChildRequests(request)"
                        />
                    </template>
                </AppTable>
            </template>

            <template #fallback>
                <div class="p-8 flex justify-center">
                    <Loader>
                        Loading requests...
                    </Loader>
                </div>
            </template>
        </SuspenseManual>
    </div>
</template>

<script setup lang="ts">
import { sortByKeyFn } from '~/lib/Helper/ArrayHelper'
import AwardAccountRequestListSectionIncomingRequest
    from '~/sections/AwardAccount/RequestList/AwardAccountRequestListSectionIncomingRequest.vue'
import { useZebraClasses } from '~/composables/useZebraClasses'
import type { ModelIdentification } from '~types/lib/Model'

defineOptions({
    name: 'AwardAccountRequestListSection',
})

const props = defineProps<{
    accountPk: PrimaryKey,
}>()

//

const { useModel } = await useNewContext('AwardAccount', props.accountPk)

const suspense = useSuspensableComponent(async () => {
    await Promise.all([
        incomingRequestList.fetch(),
        outgoingRequestList.fetch(),
    ])
})

//

const incomingRequestList = useModel('AwardAccountIncomingRequest').useResourceList({
    name: 'AwardAccountAwardAccountIncomingRequestList',
    pk: props.accountPk,
}, {
    with: ['product'],
})

const outgoingRequestList = useModel('AwardAccountOutgoingRequest').useResourceList({
    name: 'AwardAccountAwardAccountOutgoingRequestList',
    pk: props.accountPk,
}, {
    with: ['product'],
})

const incomingRequests = computed(() => {
    return incomingRequestList.records.sort(sortByKeyFn('created_at', 'asc'))
})

const outgoingRequests = computed(() => {
    return outgoingRequestList.records.sort(sortByKeyFn('created_at', 'asc'))
})

const getChildRequests = (parentAccount: ModelIdentification) => {
    const parentPk = usePk(parentAccount)

    return outgoingRequests.value.filter(request => request.parent_pk === parentPk)
}

//

const columns = useTableColumns({
    side: {
        label: 'Direction',
        width: 12,
    },
    id: {
        label: '#',
        width: 6,
    },
    consolidator_pk: {
        label: 'Supplier',
        width: 20,
    },
    balance: {
        label: 'Miles balance',
        width: 10,
    },
    miles_amount: {
        label: 'Amount miles',
        width: 10,
    },
    price: {
        label: 'Price',
        width: 8,
    },
    cpm: {
        label: 'CPM',
        width: 5,
    },
    remark: {
        label: 'Remark',
        width: 50,
    },
    created_at: {
        label: 'Date',
        width: 15,
    },
    info: {
        width: 30,
    },
    actions: {
        width: '54px',
    },
})

const zebra = useZebraClasses(incomingRequests)
</script>

