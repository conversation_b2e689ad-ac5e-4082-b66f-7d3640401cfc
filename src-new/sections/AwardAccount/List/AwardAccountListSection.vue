<template>
    <div>
        <div class="card">
            <div class="p-2 flex items-center gap-4">
                <AwardAccountListSectionFilter :search-controller="searchController" class="mr-auto" />
                <AppPaginationCompact :pagination="accountList.pagination" />
                <AppPageSize :pagination="accountList.pagination" />
            </div>

            <SuspenseManual :state="suspense">
                <template #default>
                    <AppTable
                        class="table-fixed"
                        :columns="columns"
                        :items="accounts"
                        :search-tags="searchController.tags"
                        :sort-controller="sortController"
                        :zebra="zebra"
                    >
                        <template #body>
                            <AwardAccountListSectionRow
                                v-for="(account, $i) in accounts"
                                :key="account.pk"
                                :account="account"
                                editable
                                :class="zebra[$i]"
                                :search-controller="searchController"
                            />
                        </template>
                    </AppTable>
                </template>
                <template #fallback>
                    <PlaceholderBlock class="w-full h-[300px]" />
                </template>
            </SuspenseManual>
        </div>

        <AppTablePagination class="mt-6" :pagination="accountList.pagination" />
    </div>
</template>

<script setup lang="ts">
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import SearchController from '~/lib/Search/SearchController'
import SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import SortController from '~/lib/Search/SortController'
import AppTable from '~/components/Table/AppTable.vue'
import TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import NumberSearchTag from '~/lib/Search/Tag/NumberSearchTag'
import AwardAccountListSectionRow from '~/sections/AwardAccount/List/AwardAccountListSectionRow.vue'
import AwardAccountListSectionFilter from '~/sections/AwardAccount/List/AwardAccountListSectionFilter.vue'
import { AwardAccountBookingTypeField } from '~/api/dictionaries/Static/AwardAccount/AwardAccountBookingTypeDictionary'
import { AwardAccountWarningFlag } from '~/api/models/AwardAccount/AwardAccount'
import NumberRangeSearchTag from '~/lib/Search/Tag/NumberRangeSearchTag'
import BooleanSelectSearchTag from '~/lib/Search/Tag/BooleanSelectSearchTag'

defineOptions({
    name: 'AwardAccountListSection',
})

//

const {
    useModel,
    useDictionary,
    hasPermission,
} = useContext()

//

const suspense = useSuspensableComponent(async () => {
    await accountList.fetch()
})

//

const columns = useTableColumns({
    id: {
        label: 'ID',
        width: 5,
        sortable: true,
    },
    mile_price_program_pk: {
        label: 'Pr.',
        width: 5,
        sortable: true,
        tooltip: 'Mile price program',
    },
    vpn: {
        label: 'VPN',
        width: 10,
    },
    alt_vpn: {
        enabled: hasPermission('useAltVpn', 'all'),
        label: 'Alt. VPN',
        width: 19,
    },
    holder_name: {
        label: 'Holder name',
        width: 16,
        sortable: true,
    },
    account_number: {
        label: 'Account №',
        width: 15,
        sortable: true,
    },
    password: {
        label: 'Pass',
        width: 4,
        tooltip: 'Account password',

    },
    consolidator_pk: {
        label: 'Supplier',
        width: 16,
        sortable: true,
    },
    balance: {
        label: 'Miles',
        width: 10,
        sortable: true,
    },
    cpm: {
        label: 'CPM',
        width: 7,
        sortable: true,
    },
    status: {
        label: 'Status',
        width: 10,
        sortable: true,
        tooltip: 'Account status',
    },
    info: {
        label: 'In use till',
        width: 14,
    },
    counts: {
        label: 'Ex/Ret/Tot/TUp',
        tooltip: 'Exchange / Refund / Total bookings / Top-ups',
        width: 12,
    },
    booking_type: {
        label: 'Booking type',
        width: 14,
    },
    remark: {
        label: 'Remark',
    },
    type: {
        label: 'Type',
        width: 10,
        sortable: true,
        tooltip: 'Account type',
    },
    actions: {
        width: 10,
    },
})

const sortController = SortController.fromColumns(columns)

const accountList = useModel('AwardAccount').useList({
    sort: sortController,
    where: (and) => searchController.applyCondition(and),
    pageSize: 14,
    with: ['holder', 'credentials', 'parent', 'groupAccount', 'externalUserAgent'],
})

//

async function getMilesMaxValue() {
    return await useModel('AwardAccount').actions.getMilesMaxValue()
}

const searchController = SearchController.forModel('AwardAccount', {
    id: new NumberSearchTag('ID'),

    mile_price_program_pk: new SelectSearchTag(
        'Mile price program',
        useGeneralDictionary('MilePriceProgram').mapRecords.forSelect(),
    ),

    vpn: new TextSearchTag('VPN'),
    alt_vpn: new BooleanSelectSearchTag('Alt. VPN').hideInSearch(!hasPermission('useAltVpn', 'all')),
    holder_name: new TextSearchTag('Holder'),
    account_number: new TextSearchTag('Account'),

    consolidator_pk: new SelectSearchTag(
        'Consolidator',
        useDictionary('Consolidator').mapAwardRecords.forSelect(),
    ),

    balance: new NumberRangeSearchTag('Miles', [0, await getMilesMaxValue()]),

    status: new SelectSearchTag('Status', useGeneralDictionary('AwardAccountStatus').mapRecords.forSelect()),

    booking_type: new SelectSearchTag(
        'Booking type',
        useGeneralDictionary('AwardAccountBookingType').mapRecords.forSelect(),
        {
            applyCondition: (tag, _, and) => {
                if ((tag.values ?? []).find(value => value === AwardAccountBookingTypeField.Any)) {
                    and.or((q) => {
                        for (const field of Object.values(AwardAccountBookingTypeField)) {
                            if (field === AwardAccountBookingTypeField.Any) {
                                continue
                            }

                            q.eq(field, true)
                        }
                    })

                    return
                }

                and.or(or => {
                    for (const key of (tag.values ?? []) as AwardAccountBookingTypeField[]) {
                        or.eq(key, true)
                    }
                })
            },
        },
    ),

    type: new SelectSearchTag('Type', useGeneralDictionary('AwardAccountType').mapRecords.forSelect()),

    //

    is_reserved: new SelectSearchTag('Reserved', [
        {
            title: 'Reserved',
            value: true,
        },
        {
            title: 'Not reserved',
            value: false,
        },
    ], {
        placeholder: 'All',
    }),
    group_account_number: new TextSearchTag('Main account'),
}).useList(accountList)

const accounts = computed(() => accountList.records)

const zebra = useZebraClasses(accounts, (account) => {
    switch (account.warning_flag) {
        case AwardAccountWarningFlag.DoNotUse:
        case AwardAccountWarningFlag.UnderRisk:
        case AwardAccountWarningFlag.Chargeback:
            return 'table-tr--highlighted-danger'
        case AwardAccountWarningFlag.CantUse:
            return 'table-tr--highlighted-warning'
    }
})

//

const eventService = useService('event')

onMounted(() => {
    eventService.on('awardAccountCreated', accountList.fetch.bind(accountList))
})
onUnmounted(() => {
    eventService.off('awardAccountCreated', accountList.fetch.bind(accountList))
})

// eslint-disable-next-line
defineExpose({
    searchController,
})
</script>
