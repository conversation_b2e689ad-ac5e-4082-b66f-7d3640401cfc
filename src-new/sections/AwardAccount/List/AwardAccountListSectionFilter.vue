<template>
    <div class="flex flex-row justify-start">
        <div class="flex items-center gap-x-1">
            <AppButton
                class="--small"
                :class="{
                    '--ghost': selectedPrograms.length,
                    '--no-hover': !selectedPrograms.length,
                }"
                @click="searchController.tags.mile_price_program_pk.reset()"
            >
                All
            </AppButton>

            <div class="w-px h-4 bg-secondary-200 mx-1" />

            <AppButton
                v-for="program in searchController.tags.mile_price_program_pk.selectOptions"
                :key="program.value"
                class="--small"
                :class="{
                    '--ghost': !selectedPrograms.includes(program.value),
                    '--primary': selectedPrograms.includes(program.value),
                }"
                @click="toggleProgram(program)"
            >
                {{ program.title }}
            </AppButton>
        </div>

        <div class="flex items-center gap-x-1 ml-24">
            <AppButton
                class="--small"
                :class="{
                    '--ghost': selectedStatuses.length,
                    '--no-hover': !selectedStatuses.length,
                }"
                @click="searchController.tags.status.reset()"
            >
                All
            </AppButton>

            <div class="w-px h-4 bg-secondary-200 mx-1" />

            <AppButton
                v-for="item in searchController.tags.status.selectOptions"
                :key="item.value"
                class="--small"
                :class="{
                    '--ghost': !selectedStatuses.includes(item.value),
                    [`--${item.classes}`]: selectedStatuses.includes(item.value),
                }"
                @click="toggleStatus(item)"
            >
                {{ item.title }}
            </AppButton>
        </div>

        <div class="flex items-center gap-x-1 ml-24">
            <AppButton
                class="--small"
                :class="{
                    '--ghost': reserved.length,
                    '--no-hover': !reserved.length,
                }"
                @click="searchController.tags.is_reserved.reset()"
            >
                All
            </AppButton>

            <AppButton
                v-for="item in searchController.tags.is_reserved.selectOptions"
                :key="item.value"
                class="--small"
                :class="{
                    '--ghost': !reserved.includes(item.value),
                    '--primary': reserved.includes(item.value),
                }"
                @click="searchController.tags.is_reserved.setValues([item.value])"
            >
                {{ item.title }}
            </AppButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import type SearchController from '~/lib/Search/SearchController'
import type SelectSearchTag from '~/lib/Search/Tag/SelectSearchTag'
import { unique } from '~/lib/Helper/ArrayHelper'
import type SelectOption from '~types/structures/SelectOption'

defineOptions({
    name: 'AwardAccountListSectionFilter',
})

const props = defineProps<{
    searchController: SearchController<{
        mile_price_program_pk: SelectSearchTag,
        status: SelectSearchTag,
        is_reserved: SelectSearchTag,
    }>,
}>()

//

const selectedPrograms = computed(() => (props.searchController.tags.mile_price_program_pk.values || [] as PrimaryKey[]))

function toggleProgram(program: SelectOption) {
    let values = props.searchController.tags.mile_price_program_pk.values || [] as PrimaryKey[]

    if (selectedPrograms.value.includes(program.value)) {
        values = values.filter(value => value !== program.value)
    } else {
        values.push(program.value)
    }

    props.searchController.tags.mile_price_program_pk.setValues(unique(values))
}

//

const selectedStatuses = computed(() => (props.searchController.tags.status.values || []) as PrimaryKey[])

function toggleStatus(status: SelectOption) {
    let values = props.searchController.tags.status.values || [] as PrimaryKey[]

    if (selectedStatuses.value.includes(status.value)) {
        values = values.filter(value => value !== status.value)
    } else {
        values.push(status.value)
    }

    props.searchController.tags.status.setValues(unique(values))
}

//

// @todo Fix type PrimaryKey
const reserved = computed(() => (props.searchController.tags.is_reserved.values || []) as PrimaryKey[])
</script>
