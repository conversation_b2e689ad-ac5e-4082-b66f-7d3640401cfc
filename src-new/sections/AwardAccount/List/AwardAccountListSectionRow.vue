<template>
    <tr class="table-tr--truncate">
        <TableCellLink :to="routeToAwardAccount(account.pk)">
            {{ account.id }}
        </TableCellLink>
        <td class="text-center">
            {{ milePriceProgramName }}
        </td>
        <td>{{ account.vpn }}</td>
        <td v-if="hasPermission('useAltVpn', 'all')" class="items-center">
            <template v-if="isAccountStatusAvailable || account.external_user_agent_pk">
                <div v-if="account.externalUserAgent" class="grid grid-cols-2 gap-2 items-center">
                    <AppButton
                        class="--outline --xs !border"
                        :class="[account.externalUserAgent?.status == ExternalUserAgentStatus.Pending ? '--pending' : '--success']"
                        :loading="isMultiloginLoading"
                        :disabled="isMultiloginLoading"
                        @click="openMultilogin(account.externalUserAgent)"
                    >
                        <CheckIcon />
                        Online
                    </AppButton>
                    <div class="flex justify-center w-full gap-4">
                        <EditIcon
                            class="cursor-pointer --secondary w-3.5 h-3.5"
                            @click="openExternalUserAgentUpdateModal(usePk(account.externalUserAgent))"
                        />
                        <TrashIcon
                            class="cursor-pointer --secondary w-3.5 h-3.5"
                            @click="removeExternalUserAgent(usePk(account.externalUserAgent), usePk(account))"
                        />
                    </div>
                </div>
                <div v-else class="grid grid-cols-2 gap-2 items-center justify-center">
                    <AppButton
                        class="--xs --outline --muted !border dark:text-white"
                        @click="openExternalUserAgentCreateModal(usePk(account))"
                    >
                        Create VPN
                    </AppButton>
                    <AppButton
                        class="--xs --outline --muted !border dark:text-white"
                        @click="openExternalUserAgentLinkModal(account)"
                    >
                        Link VPN
                    </AppButton>
                </div>
            </template>
        </td>
        <td>
            <div class="flex items-center">
                <template v-if="account.groupAccount">
                    <UserIcon
                        v-if="account.parent_award_account_pk"
                        class="mr-1 icon colored-text --danger --xs flex-none cursor-pointer"
                        @click="filterByAccountGroup(account.groupAccount)"
                    />
                    <UsersIcon
                        v-else
                        class="mr-1 icon colored-text --danger --xs flex-none cursor-pointer"
                        @click="filterByAccountGroup(account.groupAccount)"
                    />
                </template>
                <div class="truncate">
                    {{ getFormalFullName(account.holder) }}
                </div>
            </div>
        </td>

        <td v-copy class="cursor-[copy] select-all">
            <div class="flex items-center">
                <AlertTriangleIcon
                    v-if="account.warning_flag"
                    v-tooltip="getWarningFlagTooltip(account.warning_flag)"
                    class="mr-1 icon colored-text --danger --xs flex-none"
                />
                <LockIcon
                    v-if="account.is_reserved"
                    v-tooltip="'Reserved'"
                    class="mr-1 icon --xs flex-none text-secondary"
                />
                <div class="truncate">
                    {{ account.account_number }}
                </div>
            </div>
        </td>
        <td class="text-center">
            <AppCopy v-copy="account.credentials.password" />
        </td>
        <td>
            {{ areaName || consolidatorName }}
        </td>
        <td>{{ $format.money(account.balance, {fraction: 0}) }}</td>
        <td>{{ $format.money(account.cpm, {fraction: 4}) }}</td>
        <td>
            <div class="flex items-center gap-2 colored-text min-w-28" :class="`--${accountStatusInfo.style}`">
                <Component :is="accountStatusInfo.icon" />
                {{ accountStatusInfo.title }}
            </div>
        </td>
        <td class="!py-0">
            <RouterLink
                v-if="account.last_sale_pk"
                class="link"
                :to="routeToSale(account.last_sale_pk)"
            >
                Sale #{{ account.last_sale_pk }}
            </RouterLink>
            <div>
                {{ $format.date(account.in_use_till_date) }}
                {{ account.active_sale_count ? `(${account.active_sale_count})` : '' }}
            </div>
        </td>
        <td class="font-mono">
            <span
                :class="account.exchange_sale_count ? 'text-info' : 'text-secondary-300'"
                v-html="String(account.exchange_sale_count || '--').padStart(2, '&nbsp;')"
            />|<!--
             --><span
            :class="account.refund_sale_count ? 'text-pending' : 'text-secondary-300'"
            v-html="String(account.refund_sale_count || '--').padStart(2, '&nbsp;')"
            />|<!--
             --><span
            :class="account.total_sale_count ? 'text-secondary' : 'text-secondary-300'"
            v-html="String(account.total_sale_count || '--').padStart(2, '&nbsp;')"
            />|<!--
             --><span
            :class="account.top_up_count ? 'text-success' : 'text-secondary-300'"
            v-html="String(account.top_up_count || '--').padStart(2, '&nbsp;')"
        />
        </td>
        <td>
            {{ bookingTypeFormatted }}
        </td>
        <td>
            {{ account.remark }}
        </td>
        <td>
            {{ accountTypeInfo.title }}
        </td>
        <td>
            <div class="flex -my-1 gap-2 justify-end">
                <AppButton
                    v-tooltip="'Account info'"
                    class="--sm --only"
                    @click="openAccount(account)"
                >
                    <InfoIcon />
                </AppButton>
                <Dropdown v-if="hasPermission('manageAwardDetails', 'Sale')" teleport>
                    <template #toggle="{ toggle }">
                        <AppButton
                            v-tooltip="'Account actions'"
                            class="--sm --only"
                            @click="toggle"
                        >
                            <MoreVerticalIcon />
                        </AppButton>
                    </template>
                    <template #content>
                        <ContextMenu :options="menuOptions" />
                    </template>
                </Dropdown>
            </div>
        </td>
    </tr>
</template>

<script setup lang="ts">
import type { ModelAttributes, ModelIdentification, ModelRef } from '~types/lib/Model'
import type AwardAccount from '~/api/models/AwardAccount/AwardAccount'
import { AwardAccountStatus } from '~/api/models/AwardAccount/AwardAccount'
import type { AwardAccountWarningFlag } from '~/api/models/AwardAccount/AwardAccount'
import { $confirm, $confirmDelete } from '@/plugins/ConfirmPlugin'
import { usePk } from '~/composables/usePk'
import { Edit3Icon, PlusIcon } from '@zhuowenli/vue-feather-icons'
import AwardAccountCreateModal from '~/sections/AwardAccount/modals/AwardAccountCreateModal.vue'
import ContextMenu from '@/components/ContextMenu/ContextMenu.vue'
import AwardAccountInfoSideModal from '~/sections/AwardAccount/modals/AwardAccountInfoSideModal.vue'
import type { ContextMenuOption } from '@/plugins/ContextMenuPlugin'
import TableCellLink from '~/components/Table/TableCellLink.vue'
import { toastSuccess } from '@/lib/core/helper/ToastHelper'
import { AwardAccountBookingTypeField } from '~/api/dictionaries/Static/AwardAccount/AwardAccountBookingTypeDictionary'
import type SearchController from '~/lib/Search/SearchController'
import type TextSearchTag from '~/lib/Search/Tag/TextSearchTag'
import { getFormalFullName } from '~/lib/Helper/PersonHelper'
import AwardAccountAltVpnModal from '~/sections/AwardAccount/modals/AwardAccountAltVpn/AwardAccountAltVpnModal.vue'
import { ExternalUserAgentStatus } from '~/api/models/ExternalUserAgent/ExternalUserAgent'
import AwardAccountLinkWithAltVpnModal from '~/sections/AwardAccount/modals/AwardAccountLinkWithAltVpn/AwardAccountLinkWithAltVpnModal.vue'
import { routeToAwardAccount } from '@/lib/core/helper/RouteNavigationHelper'

defineOptions({
    name: 'AwardAccountListSectionRow',
})

const props = defineProps<{
    account: ModelRef<'AwardAccount', 'holder' | 'credentials' | 'groupAccount' | 'externalUserAgent'>
    searchController: SearchController<{
        group_account_number: TextSearchTag,
    }>,
    editable?: boolean
}>()

//

const {
    useModel,
    hasPermission,
} = useContext()

const multilogin = useService('multilogin')

const addModal = useModal(AwardAccountCreateModal)
const externalUserAgentModel = useModel('ExternalUserAgent')

const isMultiloginLoading = ref(false)

const isAccountStatusAvailable = computed(() => {
    const availableStatuses = [
        AwardAccountStatus.Active,
        AwardAccountStatus.InUse,
        AwardAccountStatus.Used,
        AwardAccountStatus.Leftover,
        AwardAccountStatus.Pending,
        AwardAccountStatus.Blocked,
    ]

    return availableStatuses.includes(props.account.status)
})

//

const menuOptions = computed(() => {
    const statusActions = accountStatusDictionary.records.filter(status => {
        return props.account.status !== status.value
    }).map(status => {
        return {
            text: 'Move to: ' + status.title,
            onClick: () => changeStatus(status.value),
            icon: status.icon,
            class: `colored-text --${status.style}`,
        }
    })

    return [
        {
            text: 'Edit',
            icon: Edit3Icon,
            onClick: () => editModal.open(),
        },
        props.account.parent_award_account_pk ? undefined : {
            text: 'Create sub-account',
            icon: PlusIcon,
            onClick: () => addModal.open({ parent: props.account }),
        },
        ...statusActions,
    ].filter(Boolean) as ContextMenuOption[]
})

//

const milePriceProgramName = computed(() => {
    return useGeneralDictionary('MilePriceProgram').find(props.account.mile_price_program_pk)?.name
})

const accountStatusDictionary = useGeneralDictionary('AwardAccountStatus')
const accountStatusInfo = computed(() => accountStatusDictionary.find(props.account.status))

const accountTypeDictionary = useGeneralDictionary('AwardAccountType')
const accountTypeInfo = computed(() => accountTypeDictionary.find(props.account.type))

const consolidatorDictionary = useGeneralDictionary('Consolidator')
const consolidatorAreaDictionary = useGeneralDictionary('ConsolidatorArea')

const consolidatorName = computed(() => {
    if (!props.account.consolidator_pk) {
        return
    }

    return consolidatorDictionary.find(props.account.consolidator_pk)?.name
})

const areaName = computed(() => {
    if (!props.account.consolidator_area_pk) {
        return
    }

    return consolidatorAreaDictionary.find(props.account.consolidator_area_pk)?.name
})

const bookingTypeDictionary = useGeneralDictionary('AwardAccountBookingType')

const bookingTypeFormatted = computed(() => {
    const values = Object.values(AwardAccountBookingTypeField).map(field => {
        const value = field in props.account ? props.account[field as keyof typeof props.account] : undefined

        if (!value) {
            return
        }

        const bookingType = bookingTypeDictionary.find(field)

        if (!bookingType) {
            return
        }

        return bookingType.title
    }).filter(Boolean)

    if (values.length === Object.keys(AwardAccountBookingTypeField).length - 1) {
        return 'Any'
    }

    return values.join(', ')
})

//

const warningFlagDictionary = useGeneralDictionary('AwardAccountWarningFlag')

const getWarningFlagTooltip = (warningFlag: AwardAccountWarningFlag) => {
    const warningFlagInfo = warningFlagDictionary.find(warningFlag)

    if (!warningFlagInfo) {
        return
    }

    return warningFlagInfo.title
}

//

const editModal = useModal(AwardAccountCreateModal, { account: props.account })

//

const accountModal = useModal(AwardAccountInfoSideModal)

function openAccount(account: ModelIdentification) {
    // @todo Handle side modals via hash
    accountModal.open({ pk: usePk(account) })
}

function filterByAccountGroup(mainAccount: ModelAttributes<'AwardAccount'>) {
    props.searchController.tags.group_account_number.setValues([mainAccount.account_number])
}

//

async function changeStatus(status: AwardAccountStatus) {
    await $confirm({
        text: 'Are you sure you want to change account status?',
        confirmButton: 'Change status',
    })

    await useModel('AwardAccount').actions.changeStatus({
        pk: usePk(props.account),
        status,
    })

    toastSuccess('Account status changed')
}

async function rejectSale() {
    await $confirm({
        text: 'Are you sure you want to reject sale?',
        description: 'Account will be unlinked from all tickets',
        confirmButton: 'Proceed',
    })

    await useModel('AwardAccount').actions.rejectSale({
        pk: usePk(props.account),
    })
}

const openExternalUserAgentCreateModal = (awardAccountPk: PrimaryKey) => {
    useModal(AwardAccountAltVpnModal).open({
        awardAccountPk,
    })
}

const openExternalUserAgentUpdateModal = (externalUserAgentPk: PrimaryKey) => {
    useModal(AwardAccountAltVpnModal).open({
        externalUserAgentPk,
    })
}

const openExternalUserAgentLinkModal = async (awardAccount: ModelRef<AwardAccount>) => {
    const externalUserAgentPk = await useModal(AwardAccountLinkWithAltVpnModal).open({
        milePriceProgramPk: awardAccount.mile_price_program_pk,
    })

    if (externalUserAgentPk) {
        await externalUserAgentModel.actions.linkWithAwardAccount({
            external_user_agent_pk: externalUserAgentPk,
            award_account_pk: usePk(awardAccount),
        })
    }
}

const removeExternalUserAgent = async (externalUserAgentPk: PrimaryKey, awardAccountPk: PrimaryKey) => {
    const resp = await $confirmDelete({
        text: 'Are you sure you want to delete it?',
    })

    if (resp) {
        await externalUserAgentModel.actions.remove({ pk: externalUserAgentPk, award_account_pk: awardAccountPk })
    }
}

const openMultilogin = async (externalMultilogin: ModelAttributes<'ExternalUserAgent'>) => {
    isMultiloginLoading.value = true
    await multilogin.login(externalMultilogin)
    isMultiloginLoading.value = false
}
</script>
