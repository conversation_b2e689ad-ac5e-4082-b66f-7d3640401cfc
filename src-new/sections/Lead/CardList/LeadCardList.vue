<template>
    <SuspenseManual :state="suspense">
        <template #default>
            <div class="relative max-h-full !w-72 group">
                <VirtualList
                    ref="listRef"
                    :item-height="(index) => leadsListWithSelector[index]?.itemHeight"
                    :items="leadsListWithSelector"
                    :infinite-scroll-options="{ list: leadsList, distance: 300 }"
                    class="fancy-scroll pr-2 h-full min-w-[290px] !overflow-y-scroll "
                    @load="onLoad"
                >
                    <template #items="{ list }">
                        <div v-if="isLeadsListOutdated" class="hidden absolute bottom-[35px] w-full z-10 px-2 pr-3 h-[35px] mb-2 group-hover:block">
                            <div class="h-full flex button-group shadow">
                                <AppButton
                                    v-tooltip="{
                                        content: `Refresh list`
                                    }"
                                    :loading="suspense.loading.value"
                                    class="rounded-none rounded-t w-full h-full dark:bg-dark-1 dark:text-secondary-200"
                                    @click="refreshLeadList"
                                >
                                    <Loader v-if="suspense.loading.value" />
                                    Refresh list
                                </AppButton>
                            </div>
                        </div>
                        <div class="hidden absolute bottom-0 z-10 w-full px-2 pr-3 h-[35px] group-hover:block">
                            <div class="w-full h-full button-group grid grid-cols-2 shadow">
                                <AppButton
                                    v-tooltip="{
                                        content: `Press Alt+J (or Arrow Left) to navigate to the previous lead.`
                                    }"
                                    class="rounded-none rounded-t h-full dark:bg-dark-1 dark:text-secondary-200"
                                    @click="goToPreviousLead"
                                >
                                    <ChevronLeftIcon />
                                    Prev
                                </AppButton>
                                <AppButton
                                    v-tooltip="{
                                        content: `Press Alt+K (or Arrow Right) to navigate to the next lead.`
                                    }"
                                    class="rounded-none rounded-t h-full dark:bg-dark-1 dark:text-secondary-200"
                                    @click="goToNextLead"
                                >
                                    Next
                                    <ChevronRightIcon />
                                </AppButton>
                            </div>
                        </div>
                        <div class="p-2 pr-0">
                            <div class="sticky top-0 z-10 mb-2">
                                <div class="flex items-center">
                                    <div
                                        class="card relative flex flex-grow items-center justify-between text-xs pl-2.5 font-semibold text-center border dark:border-gray-700 text-secondary-300"
                                    >
                                        {{ leadsTitle }} ({{ leadsAmount }})
                                        <div
                                            v-if="isFiltered"
                                            class="absolute right-6 border-none p-1.5 --no-hover outline-none"
                                            @click="resetFilters"
                                        >
                                            <XIcon class="cursor-pointer !stroke-2" />
                                        </div>
                                        <button />
                                        <Dropdown>
                                            <template #toggle="{ toggle }">
                                                <div class="cursor-pointer h-full py-2 pr-2.5" @click="toggle">
                                                    <MenuIcon class="dark:text-white w-[14px] h-[14px]" />
                                                </div>
                                            </template>
                                            <template #content="{ close }">
                                                <div @click="close">
                                                    <div class="mt-2 mr-2 card min-w-[150px] shadow-sm border-2 border-secondary-100">
                                                        <div class="py-4 px-4 border-b flex justify-start">
                                                            <span class="leading-none font-semibold">Sort by</span>
                                                        </div>
                                                        <div class="p-2 flex flex-col">
                                                            <a
                                                                v-for="(item, index) in orderByLeadSortOptions"
                                                                :key="item.direction"
                                                                class="dropdown-menu__item"
                                                                :class="{
                                                                    'text-primary': item.direction !== LeadSortOptions.Default
                                                                }"
                                                                @click.stop="() => {
                                                                    leadsSortHandler(item.field, index)
                                                                }"
                                                            >
                                                                {{ item.label }}
                                                                <ArrowUpIcon
                                                                    v-if="item.direction === LeadSortOptions.Ascending"
                                                                    class="w-3 h-3 ml-auto"
                                                                />
                                                                <ArrowDownIcon
                                                                    v-if="item.direction === LeadSortOptions.Default"
                                                                    class="w-3 h-3 ml-auto"
                                                                />
                                                                <ArrowDownIcon
                                                                    v-if="item.direction === LeadSortOptions.Descending"
                                                                    class="w-3 h-3 ml-auto"
                                                                />

                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </div>
                                <div v-if="activeLeadRecord?.lead" class="bg-white dark:bg-dark-3">
                                    <div class="flex pr-2 pt-1">
                                        <div class="flex h-[1px] w-full items-center justify-center border-t border-secondary-100 dark:border-dark-6 my-3">
                                            <span class="text-xs px-2 bg-white font-semibold text-secondary dark:bg-dark-3 dark:text-secondary-300">{{ CardType.Selected }}</span>
                                        </div>
                                    </div>
                                    <LeadCard
                                        class="!mt-1"
                                        :lead="activeLeadRecord.lead"
                                        is-active
                                        :is-last-opened-active="isOpenedLastOpenedLeadsBlock"
                                        @update:remove-lead-from-last-opened-block="(pk: PrimaryKey)=>{
                                            removeLastOpenedLead(pk)
                                        }"
                                        @click="activeLeadRecord.lead?.isDeleted ? showIsForbiddenMessage() : setActiveLead(usePk(activeLeadRecord.lead))"
                                    />
                                </div>
                            </div>

                            <template
                                v-for="({ data, index }) in makeTypedList(list)"
                                :key="index"
                            >
                                <div
                                    v-if="data.type === CardType.LastOpened && data.showDivider"
                                    class="flex cursor-pointer"
                                    :class="{
                                        'customer-pass-tab-active': isOpenedLastOpenedLeadsBlock
                                    }"
                                    @click="toggleLastOpenedLeadsBlock(!isOpenedLastOpenedLeadsBlock)"
                                >
                                    <div
                                        class="w-full bg-secondary-100 rounded mt-1 dark:bg-dark-4"
                                        :class="{
                                            'rounded-b-none':isOpenedLastOpenedLeads
                                        }"
                                    >
                                        <div class="p-1 flex justify-between items-center">
                                            <span
                                                class="text-xs cursor-pointer transition"
                                                :class="isOpenedLastOpenedLeads ? 'rotate-90' : 'rotate-0'"
                                            >
                                                <ChevronRightIcon class="h-3 w-3 text-secondary" />
                                            </span>
                                            <span
                                                class="px-2 text-xs font-semibold text-secondary"
                                                :class="{
                                                    'text-secondary-900 dark:text-secondary-300':isOpenedLastOpenedLeads
                                                }"
                                            >
                                                Last opened
                                            </span>
                                            <span
                                                class="text-xs cursor-pointer pl-2"
                                                @click.stop="removeLastOpenedLeadsBlock"
                                            >
                                                <XIcon class="h-3 w-3 text-secondary" />
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if="data.type === CardType.Pinned && data.showDivider" class="flex">
                                    <div class="flex h-[1px] w-full items-center justify-center border-t border-secondary-100 dark:border-dark-6 mt-4 mb-3">
                                        <span class="px-2 bg-white text-xs font-semibold text-secondary dark:bg-dark-3 dark:text-secondary-300 ">{{ CardType.Pinned }}</span>
                                    </div>
                                </div>
                                <div v-else-if="data.type === CardType.Day && data.showDivider" class="flex">
                                    <div class="flex h-[1px] w-full items-center justify-center border-t border-secondary-100 dark:border-dark-6 dark:text-secondary-300 mt-5 mb-2">
                                        <span class="px-2 bg-white text-xs font-semibold text-secondary dark:bg-dark-3 dark:text-secondary-300 ">{{ $format.date(data.lead.created_at) }}</span>
                                    </div>
                                </div>
                                <div
                                    v-if="(data.type === CardType.LastOpened && isOpenedLastOpenedLeadsBlock) || data.type !== CardType.LastOpened"
                                    class="py-1.5 px-0.5"
                                    :class="{
                                        'bg-secondary-100 dark:bg-dark-4': data.type === CardType.LastOpened
                                    }"
                                >
                                    <LeadCard
                                        :lead="data.lead"
                                        :is-active="activeLeadPk === usePk(data.lead)"
                                        :is-pinned="data.type === CardType.Pinned"
                                        :is-last-opened="data.type === CardType.LastOpened"
                                        @update:remove-lead-from-last-opened-block="(pk: PrimaryKey)=>{
                                            removeLastOpenedLead(pk)
                                        }"
                                        @click="data.lead?.isDeleted ? showIsForbiddenMessage() : setActiveLead(usePk(data.lead))"
                                    />
                                </div>
                            </template>
                        </div>
                    </template>
                </VirtualList>
            </div>
        </template>
        <template #fallback>
            <div class="p-4 min-h-[670px] min-w-[290px]">
                <PlaceholderBlock class="w-full h-full " />
            </div>
        </template>
    </SuspenseManual>
</template>

<script lang="ts">
export enum CardType {
    Selected = 'Opened',
    LastOpened = 'last opened',
    Pinned = 'pinned',
    Day = 'day'
}

enum LeadSortOptions {
    Default = '',
    Ascending = 'asc',
    Descending = 'desc',
}
</script>

<script setup lang="ts">
import VirtualList from '~/components/VirtualList.vue'
import { useAppSetting } from '~/composables/useAppSettings'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import LeadCard from '~/sections/Lead/CardList/components/LeadCard.vue'
import type { ModelAttributes, ResourceListRef } from '~types/lib/Model'
import {
    goTo,
    goToLead,
    goToLeadManagement,
    routeToLead,
    routeToLeadManagement,
} from '@/lib/core/helper/RouteNavigationHelper'
import type { LeadSearchController, LeadSortController } from '~/sections/Lead/composable/useLeadSearchController'
import { unique } from '~/lib/Helper/ArrayHelper'
import type { ApiListResponse } from '~types/lib/Api'
import { useApiRoute } from '~/composables/_core/useApiRoute'
import ArrayConditionHelper from '~/lib/Helper/ArrayCondition/ArrayConditionHelper'
import { useRoute } from 'vue-router'
import useLeadManagementMode from '~/composables/useLeadManagementMode'
import { useLeadHotkeys } from '~/sections/Lead/composable/useLeadHotkeys'
import { toastError } from '@/lib/core/helper/ToastHelper'

defineOptions({
    name: 'LeadCardList',
})

const props = defineProps<{
    leadPk?: PrimaryKey,
    searchController: LeadSearchController,
    sortController: LeadSortController,
}>()

const CardHeight = {
    withCardDivider: 158,
    withoutCardDivider: 132,
    cardDivider: 29,
    hiddenCard: 0,
}

const {
    useModel,
    currentUserPk,
    useResourceFetch,
    resourceContextOptions,
} = useContext()

const leadModel = useModel('Lead')

//

const route = useRoute()

const isLeadManagementPage = useLeadManagementMode(route)

const activeLeadPk = computed(() => {
    return props.leadPk
})

const isVisibleLastOpenedLeadsBlock = ref(true)

await useResourceFetch('PinnedAgentLeadsList').fetchResource(currentUserPk)
const pinnedListResource = useRef(resourceContextOptions).resourceRef('PinnedAgentLeadsList', currentUserPk) as ResourceListRef

const pinnedLeadsPks = computed(() => {
    return pinnedListResource.list
})

const showDividersBySelector = {
    selected: true,
    lastOpened: true,
    pinned: true,
}

//

const lastOpenedLeadsPks = useLocalStorage(`state:lastOpenedLeadsPks:${currentUserPk}`, [] as PrimaryKey[])
const isOpenedLastOpenedLeadsBlock = useAppSetting('isOpenedLastOpenedLeadsBlock')
const lastOpenedLeadsPksInSession = ref(lastOpenedLeadsPks.value || [])

//

const isOpenedLastOpenedLeads = computed(() => {
    return isOpenedLastOpenedLeadsBlock.value
})

const suspense = useSuspensableComponent(async () => {
    await Promise.all([
        leadsList.fetch(),
        userSelectedLeads.fetch(),
    ])
})

const refreshLeadList = async () => leadsList.fetch()
//

const sortController = props.sortController

const orderByLeadSortOptions = computed(() => {
    if (sortController.state?.created_at) {
        return [
            {
                label: 'Created date',
                field: 'created_at',
                direction: sortController.state.created_at,
            },
            {
                label: 'Taken date',
                field: 'taken_date',
                direction: LeadSortOptions.Default,
            },
        ]
    }

    if (sortController.state?.taken_date) {
        return [
            {
                label: 'Created date',
                field: 'created_at',
                direction: LeadSortOptions.Default,
            },
            {
                label: 'Taken date',
                field: 'taken_date',
                direction: sortController.state.taken_date,
            },
        ]
    }

    return [
        {
            label: 'Created date',
            field: 'created_at',
            direction: LeadSortOptions.Default,
        },
        {
            label: 'Taken date',
            field: 'taken_date',
            direction: LeadSortOptions.Default,
        },
    ]
})

//

const leadsList = leadModel.useList({
    with: ['clientPreview', 'executor', 'email', 'phone', 'utm', 'listInformation', 'status', 'agentLeadInfo', 'expert', 'chat', 'chat.info', 'createdBy'],
    where: (and) => {
        searchController.applyCondition(and)
    },
    sort: sortController,
    pageSize: 10,
    pageFromPk: activeLeadPk.value,
    preload: {
        direction: 'both',
        pagesAmount: 1,
    },
})

const searchController = props.searchController.useList(leadsList)

//

const isLeadsListOutdated = computed(() => leadsList.records?.some((lead) => lead.isDeleted))

const showIsForbiddenMessage = () => {
    toastError('You can no longer access this lead')
}

function makeTypedList(virtualList: unknown) {
    return virtualList as {
        data: {
            lead: typeof leadsList['records'][number],
            type: CardType,
            showDivider: boolean,
            itemHeight: number,
        },
        index: number,
    }[]
}

//

const lastOpenedFilteredLeadsPks = computed(() => {
    return lastOpenedLeadsPksInSession.value.filter(pk => {
        return !pinnedLeadsPks.value.includes(pk)
    })
})

const shouldLoadLastOpenedLeadCards = computed(() => {
    return !isVisibleLastOpenedLeadsBlock.value ||
        lastOpenedFilteredLeadsPks.value.length === 0 ||
        (lastOpenedFilteredLeadsPks.value.length === 1 && lastOpenedFilteredLeadsPks.value[0] === activeLeadPk.value)
})

const computedLeadPks = computed(() => {
    if (shouldLoadLastOpenedLeadCards.value) {
        return [activeLeadPk.value, ...pinnedLeadsPks.value].filter(Boolean)
    }

    return [activeLeadPk.value, ...lastOpenedFilteredLeadsPks.value, ...pinnedLeadsPks.value].filter(Boolean)
})

const useLeadPks = ref<PrimaryKey[]>([])

watch(computedLeadPks, (pks) => {
    if (!pks.length) {
        return
    }

    pks = unique(pks.filter(Boolean))

    useHttp(leadModel.resourceContextOptions.http).get<ApiListResponse<PrimaryKey[]>>(useApiRoute().search('Lead'), {
        query: {
            limit: 100,
            where: (new ArrayConditionHelper()).in('id', pks).getParams(),
        },
    }).then((response) => {
        useLeadPks.value = computedLeadPks.value.filter((pk) => {
            return response.result.includes(pk)
        })
    })
}, { immediate: true })

const userSelectedLeads = leadModel.useResourceList(useLeadPks, {
    with: ['clientPreview', 'executor', 'email', 'phone', 'utm', 'listInformation', 'status', 'agentLeadInfo', 'expert', 'chat', 'chat.info', 'createdBy'],
})

//

const combinedLeadsSelectedByUserComputed = computed(() => {
    return [...userSelectedLeads.records, ...leadsList.records.filter(lead => {
        return !pinnedLeadsPks.value.includes(usePk(lead))
    })]
})

const activeLeadRecord = ref<typeof leadsListWithSelector['value'][number]>()

const leadsListWithSelector = computed(() => {
    const checkedLastOpenedPks: PrimaryKey[] = []

    if (combinedLeadsSelectedByUserComputed.value.length === 0) {
        return []
    }

    const leadsResultList = combinedLeadsSelectedByUserComputed.value.map((lead: ModelAttributes<'Lead'>, leadIndex: number) => {
        const leadType = leadTypeConditions(lead, leadIndex, checkedLastOpenedPks)

        if (leadType === CardType.LastOpened) {
            checkedLastOpenedPks.push(usePk(lead))
        }

        const shouldShowDivider = leadDividerConditions(leadType, leadIndex)
        let itemHeight = shouldShowDivider ? CardHeight.withCardDivider : CardHeight.withoutCardDivider

        if (leadType === CardType.LastOpened && shouldShowDivider && !isOpenedLastOpenedLeadsBlock.value) {
            itemHeight = CardHeight.cardDivider
        } else if (leadType === CardType.LastOpened && !shouldShowDivider && !isOpenedLastOpenedLeadsBlock.value) {
            itemHeight = CardHeight.hiddenCard
        }

        return {
            lead: lead,
            type: leadType,
            showDivider: shouldShowDivider,
            itemHeight: itemHeight,
        }
    })

    if (tryUsePk(leadsResultList[0]?.lead) === activeLeadPk.value) {
        activeLeadRecord.value = leadsResultList.shift()
    }

    return leadsResultList
})

//

function leadTypeConditions(lead: ModelAttributes<'Lead'>, leadIndex: number, checkedLastOpenedPks: PrimaryKey[] = []) {
    if (usePk(lead) === activeLeadPk.value && leadIndex === 0) {
        return CardType.Selected
    }

    if (lastOpenedFilteredLeadsPks.value.includes(usePk(lead)) && !checkedLastOpenedPks.includes(usePk(lead))) {
        return CardType.LastOpened
    }

    if (pinnedLeadsPks.value.includes(usePk(lead))) {
        return CardType.Pinned
    }

    return CardType.Day
}

function leadDividerConditions(leadType: CardType, leadIndex: number) {
    if (leadIndex === 0) {
        showDividersBySelector.selected = true
        showDividersBySelector.lastOpened = true
        showDividersBySelector.pinned = true
    }

    if (showDividersBySelector.selected && leadType === CardType.Selected) {
        showDividersBySelector.selected = false

        return true
    }

    if (leadType === CardType.LastOpened && showDividersBySelector.lastOpened) {
        showDividersBySelector.lastOpened = false

        return true
    }

    if (leadType === CardType.Pinned && showDividersBySelector.pinned) {
        showDividersBySelector.pinned = false

        return true
    }

    if (leadType === CardType.Day) {
        if (leadIndex === 0 || leadTypeConditions(combinedLeadsSelectedByUserComputed.value[leadIndex - 1], leadIndex - 1) !== CardType.Day) {
            return true
        }
        const previousLeadDate = Date.fromUnixTimestamp(combinedLeadsSelectedByUserComputed.value[leadIndex - 1].created_at).toFormatUTC('dd/MM/yyyy')
        const currentLeadDate = Date.fromUnixTimestamp(combinedLeadsSelectedByUserComputed.value[leadIndex].created_at).toFormatUTC('dd/MM/yyyy')

        return previousLeadDate !== currentLeadDate
    }

    return false
}

//

const leadsAmount = computed(() => {
    return leadsList.pagination.value?.items.count
})

const loadMoreDebounced = useDebounceFn(() => {
    return leadsList.pagination.value?.loadNextPage()
}, 100)

const onLoad = () => {
    if (leadsList.loading.value || leadsList.loadingMore.value) {
        return
    }

    return loadMoreDebounced()
}

//

const loadPreviousRecords = useDebounceFn(() => {
    return leadsList.pagination.value?.loadPreviousPage()
}, 100)

const onLoadPrevious = () => {
    if (leadsList.loading.value || leadsList.loadingMore.value) {
        return
    }

    return loadPreviousRecords()
}

//

function setActiveLead(pk: PrimaryKey) {
    if (!lastOpenedLeadsPks.value) {
        lastOpenedLeadsPks.value = [pk]
        isLeadManagementPage ? goToLeadManagement(pk, true) : goToLead(pk, true)

        return
    }
    let updatedLastOpenedLeadsPksInSession: PrimaryKey[] = [...lastOpenedLeadsPks.value]

    if (lastOpenedLeadsPks.value.length < 10) {
        updatedLastOpenedLeadsPksInSession = [pk, ...lastOpenedLeadsPks.value]
    } else {
        updatedLastOpenedLeadsPksInSession.pop()
        updatedLastOpenedLeadsPksInSession.unshift(pk)
    }
    lastOpenedLeadsPks.value = unique(updatedLastOpenedLeadsPksInSession.filter(pk => {
        return !pinnedLeadsPks.value.includes(pk)
    }))
    isLeadManagementPage ? goToLeadManagement(pk, true) : goToLead(pk, true)
}

//

function removeLastOpenedLead(removedLeadPk: PrimaryKey) {
    lastOpenedLeadsPksInSession.value = lastOpenedLeadsPksInSession.value.filter((pk) => {
        return pk !== removedLeadPk
    })
    lastOpenedLeadsPks.value = lastOpenedLeadsPksInSession.value
}

//

const isFiltered = computed(() => {
    return sortController.state?.created_at || sortController.state?.taken_date
})

function leadsSortHandler(field: string, index: number) {
    if (index === 0 && orderByLeadSortOptions.value[1].direction !== LeadSortOptions.Default) {
        orderByLeadSortOptions.value[1].direction = LeadSortOptions.Default
    } else if (index === 1 && orderByLeadSortOptions.value[0].direction !== LeadSortOptions.Default) {
        orderByLeadSortOptions.value[0].direction = LeadSortOptions.Default
    }

    if (orderByLeadSortOptions.value[index].direction === LeadSortOptions.Descending) {
        orderByLeadSortOptions.value[index].direction = LeadSortOptions.Ascending
        sortController.set(orderByLeadSortOptions.value[index].field, LeadSortOptions.Ascending)
    } else if (orderByLeadSortOptions.value[index].direction === LeadSortOptions.Ascending) {
        orderByLeadSortOptions.value[index].direction = LeadSortOptions.Default
        sortController.set(orderByLeadSortOptions.value[index].field, undefined)
    } else {
        orderByLeadSortOptions.value[index].direction = LeadSortOptions.Descending
        sortController.set(orderByLeadSortOptions.value[index].field, LeadSortOptions.Descending)
    }
}

function resetFilters() {
    sortController.reset()
}

//

const leadStatusDictionary = useGeneralDictionary('LeadStatus')

const leadsTitle = computed(() => {
    const activeTagsLabels = searchController.activeTags.map(activeTag => {
        return activeTag.queryLabel
    })

    if (searchController.activeTags.length === 1 && activeTagsLabels.includes('Bonus')) {
        const bonusSearchValue = (searchController.activeTags.find(activeTag => {
            return activeTag.queryLabel === 'Bonus'
        }))?.values

        if (bonusSearchValue && bonusSearchValue[0] === '0') {
            return 'Not bonus leads'
        }

        if (bonusSearchValue && bonusSearchValue[0] === '1') {
            return 'Bonus leads'
        }
    }

    if (searchController.activeTags.length === 1 && activeTagsLabels.includes('Status')) {
        const statusSearchValue = (searchController.activeTags.find(activeTag => {
            return activeTag.queryLabel === 'Status'
        }))?.values

        if (statusSearchValue) {
            const leadStatusTitle = (leadStatusDictionary.find(statusSearchValue[0] as PrimaryKey))?.name

            return `${leadStatusTitle} leads`
        }
    }

    if (searchController.hasActiveTags) {
        return 'Filtered leads'
    }

    return 'Leads'
})

// last opened leads

function removeLastOpenedLeadsBlock() {
    isVisibleLastOpenedLeadsBlock.value = false
    lastOpenedLeadsPks.value = []
    lastOpenedLeadsPksInSession.value = []
}

function toggleLastOpenedLeadsBlock(toggle: boolean) {
    isOpenedLastOpenedLeadsBlock.value = toggle
}

//

const listRef = ref<{
    // eslint-disable-next-line
    scrollTo: (index: number) => void
}>()

watch(listRef, () => {
    if (props.leadPk) {
        nextTick(() => {
            const activeLeadPk = props.leadPk

            const firstDayIndex = leadsListWithSelector.value.findIndex((item) => {
                return item.type === CardType.Day
            })

            const leadRecords =  leadsListWithSelector.value.slice(firstDayIndex).map((item) => usePk(item.lead))

            const activeLeadPkIndex = leadRecords.findIndex(pk => pk === activeLeadPk) + computedLeadPks.value.length

            if (activeLeadPkIndex !== -1) {
                listRef.value?.scrollTo(activeLeadPkIndex)
            }
        })
    }
})

const getRealIndex = (index: number) => {
    const firstDayIndex = leadsListWithSelector.value.findIndex((item) => {
        return item.type === CardType.Day
    })

    return firstDayIndex + index
}

const {
    goToPreviousLead,
    goToNextLead,
} = useLeadHotkeys({
    activeLeadPk: activeLeadPk,
    pkList: computed(() => {
        const firstDayIndex = leadsListWithSelector.value.findIndex((item) => {
            return item.type === CardType.Day
        })

        return leadsListWithSelector.value.slice(firstDayIndex).map((item) => usePk(item.lead))
    }),
    onNavigate: async (index, leadPk) => {
        const realIndex = getRealIndex(index)

        // @todo Should be called after navigation, but there is a bug with computed list in parent component that makes it impossible
        // When parent list will be updated correctly, this should be moved after goTo call
        listRef.value?.scrollTo(realIndex)

        const route = isLeadManagementPage ? routeToLeadManagement(leadPk) : routeToLead(leadPk)

        await goTo(route, true)
    },
    onBeforeNavigate: async (activeIndex, direction) => {
        if (direction === 'next' && leadsList.pagination.value) {
            const realIndex = getRealIndex(activeIndex)

            if (realIndex + 1 + 1 >= leadsListWithSelector.value.length) {
                await onLoad()
            }
        }

        if (direction === 'prev' && leadsList.pagination.value) {
            if (activeIndex - 1 <= 0) {
                await onLoadPrevious()
            }
        }
    },
})
</script>
