<template>
    <AppModalWrapper header="Processing queue" class="!max-w-[1800px]">
        <SuspenseManual :state="suspense">
            <template #default>
                <div class="p-4 pb-0">
                    <div class="card border border-secondary-100 dark:border-secondary-600">
                        <AppTable
                            :columns="columns"
                            :items="expertLeadsRecords"
                            zebra
                        >
                            <template #body>
                                <tr
                                    v-for="(lead, index) in expertLeadsRecords"
                                    :key="index"
                                    :class="{
                                        'table-tr--highlighted-primary' : lead.expertLeadInfo.expert_pk
                                    }"
                                >
                                    <TableCellLink :to="linkToExpertLead(usePk(lead))">
                                        {{ usePk(lead) }}
                                    </TableCellLink>
                                    <td>
                                        {{ lead.from_iata_code }}
                                    </td>
                                    <td>
                                        {{ lead.to_iata_code }}
                                    </td>
                                    <td>
                                        {{ $format.date(lead.departure_date, 'UTC', { full:true }) }}
                                    </td>
                                    <td>
                                        {{ lead.return_date ? $format.date(lead.return_date, 'UTC',{ full:true }) :
                                            dateFormatItinerary('', lead.itinerary_type) }}
                                    </td>
                                    <td class="max-w-[140px] truncate">
                                        <span v-if="lead.executor_pk" class="text-blue-500 ">
                                            {{ getFullName(lead.executor) }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ $format.datetime(lead.created_at) }}
                                    </td>
                                    <TableCellLink
                                        :to="hasPermission('view','Client') ? linkToClient(lead.client_pk) : undefined"
                                    >
                                        <div class="max-w-[140px] truncate">
                                            {{ getFullName(lead.clientPreview) }}
                                        </div>
                                    </TableCellLink>
                                    <td>
                                        <div class="flex gap-2 items-center h-full">
                                            <div class="flex flex-col gap-y-1 min-w-[122px]">
                                                <div class="text-xs">
                                                    Price
                                                </div>
                                                <div
                                                    v-tooltip="{content: !lead.expertLeadQueueInfo.lead_price ? 'Price is not set': ''}"
                                                    class="badge --soft --outline"
                                                    :class="lead.expertLeadQueueInfo.lead_price > 7000 ? '--success': '--muted dark:text-secondary-400'"
                                                >
                                                    Price<span v-if="lead.expertLeadQueueInfo.lead_price">: {{ lead.expertLeadQueueInfo.lead_price }}</span>  <span v-if="lead.expertLeadQueueInfo.lead_price > 7000"><CheckIcon /></span>
                                                </div>
                                            </div>
                                            <div class="h-full w-[1px] bg-secondary-100 dark:bg-secondary-600" />
                                            <div class="flex flex-col gap-y-1 min-w-[200px]">
                                                <div class="text-xs">
                                                    Lead status
                                                </div>
                                                <div class="flex gap-1 items-center">
                                                    <div
                                                        class="badge --soft --outline"
                                                        :class="lead.expertLeadQueueInfo.rush ? '--success': '--muted dark:text-secondary-400'"
                                                    >
                                                        Rush <span v-if="lead.expertLeadQueueInfo.rush"><CheckIcon /></span>
                                                    </div>
                                                    <div
                                                        class="badge --soft --outline"
                                                        :class="lead.expertLeadQueueInfo.reached ? '--success': '--muted dark:text-secondary-400'"
                                                    >
                                                        Reached <span v-if="lead.expertLeadQueueInfo.reached"><CheckIcon /></span>
                                                    </div>
                                                    <div
                                                        class="badge --soft --outline"
                                                        :class="lead.expertLeadQueueInfo.follow_up ? '--success': '--muted dark:text-secondary-400'"
                                                    >
                                                        Follow Up <span v-if="lead.expertLeadQueueInfo.follow_up"><CheckIcon /></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="h-full w-[1px] bg-secondary-100 dark:bg-secondary-600" />
                                            <div class="flex flex-col gap-y-1">
                                                <div class="text-xs">
                                                    Destination
                                                </div>
                                                <div class="flex gap-1 items-center">
                                                    <div
                                                        v-for="(destination, destinationIndex) in Object.keys(ExpertQueueDestination)"
                                                        :key="destinationIndex"
                                                    >
                                                        <div
                                                            class="badge --outline --soft"
                                                            :class="lead.expertLeadQueueInfo.destination === ExpertQueueDestination[destination] ? '--success': '--muted dark:text-secondary-400'"
                                                        >
                                                            {{ destination }} <span v-if="lead.expertLeadQueueInfo.destination === ExpertQueueDestination[destination]"><CheckIcon /></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="h-full w-[1px] bg-secondary-100 dark:bg-secondary-600" />
                                            <div class="flex flex-col gap-y-1">
                                                <div class="text-xs">
                                                    Request date
                                                </div>
                                                <div
                                                    class="badge --outline --soft --muted dark:text-secondary-200"
                                                >
                                                    {{ $format.date(lead.expertLeadQueueInfo.expert_request_at, 'UTC', { full: true }) }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </AppTable>
                    </div>
                </div>
            </template>
            <template #fallback>
                <PlaceholderBlock class="h-[300px] flex-grow" />
            </template>
        </SuspenseManual>
        <AppTablePagination class="mt-6 px-6 pb-4" :pagination="expertLeadsList.pagination" />
    </AppModalWrapper>
</template>

<script setup lang="ts">
import { linkToClient, linkToExpertLead } from '@/lib/core/helper/RouteNavigationHelper'
import TableCellLink from '~/components/Table/TableCellLink.vue'
import { getFullName } from '~/lib/Helper/PersonHelper'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import AppTable from '~/components/Table/AppTable.vue'
import { dateFormatItinerary } from '~/lib/Helper/LeadHelper'
import { ExpertQueueDestination } from '~/api/models/Lead/LeadAdditionalExpertQueueInformation'

defineOptions({
    name: 'ExpertLeadProcessingQueueModal',
})

const { useModel, hasPermission } = useContext()

const leadModel = useModel('Lead')

const suspense = useSuspensableComponent(async () => {
    await expertLeadsList.fetch()
})

const expertLeadsList = leadModel.useList({
    with: ['expertLeadQueueInfo', 'executor', 'clientPreview', 'expertLeadInfo'],
    pageSize: 10,
    customParams: { expertLeadProcessingQueue: true },
})

const expertLeadsRecords = computed(() => {
    return expertLeadsList.records
})

const columns = useTableColumns({
    id: {
        label: '#',
        sortable: false,
        width: 7,
    },
    from_iata_code: {
        label: 'From',
        sortable: false,
        width: 6,
    },
    to_iata_code: {
        label: 'To',
        sortable: false,
        width: 6,
    },
    departure_date: {
        label: 'Departure',
        sortable: false,
        width: 10,
    },
    return_date: {
        label: 'Return',
        sortable: false,
        width: 10,
    },
    executor_pk: {
        label: 'Requested by',
        sortable: false,
        width: 10,
    },
    created_at: {
        label: 'Created',
        sortable: false,
        width: 10,
    },
    client_fullname: {
        label: 'Client',
        sortable: false,
    },
    expert_status: {
        label: 'Leads queue info',
        tooltip: {
            html: true,
            content: `<div class="text-left">Handling lead logic<br>1)Published price  > 7000<br>2)Rush<br>3)Reached<br>4)Follow up<br>5)Destination places in shown order <br>6)Request date</div>`,
            placement: 'right',
            disabled: false,
        },
        sortable: false,
        width: 100,
        icon: InfoIcon,
        iconAfter: true,
    },
})
</script>

