<template>
    <div class="agents-table-details">
        <div class="flex flex-col h-full max-h-full">
            <Tabs
                v-model="currentTab"
                class="bg-white dark:bg-dark-3 dark:border-dark-6 px-3 pt-2 border-b-4 border-theme-39"
                :tabs="tabs"
            />

            <div
                v-if="currentTab === 'profile'"
                class="tab-content box-border bg-slate-50 p-4 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <AgentProfileSection :agent-pk="agentPk" />
            </div>
            <div
                v-if="currentTab === 'devices'"
                class="tab-content box-border bg-slate-50 p-4 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <AgentDevicesBlock :agent-pk="agentPk" />
            </div>
            <div
                v-if="currentTab === 'access-log'"
                class="tab-content box-border bg-slate-50 p-4 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <AgentHistoryBlock :agent-pk="agentPk" />
            </div>
            <div
                v-if="currentTab === 'activity-log'"
                class="tab-content box-border bg-slate-50 p-4 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <AgentActivityBlock :agent-pk="agentPk" />
            </div>
            <div
                v-if="currentTab === 'abilities' && $can('manage', 'all')"
                class="tab-content box-border bg-slate-50 p-4 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <AgentAbilitiesSection :agent-pk="agentPk" />
            </div>
            <div
                v-if="currentTab === 'time-tracking' && $can('manage', 'all')"
                class="tab-content box-border bg-slate-50 p-4 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <AgentTimeTrackingBlock :agent-pk="agentPk" />
            </div>

            <div
                v-if="currentTab === 'payhub'"
                class="tab-content box-border bg-slate-50 p-4 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <AgentPayHubSection :agent-pk="agentPk" />
            </div>
            <div
                v-if="currentTab === 'skills-chart'"
                class="tab-content box-border bg-slate-50 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <SkillsChartSection :agent-pk="agentPk" />
            </div>
            <div
                v-if="currentTab === 'activity-logs'"
                class="tab-content box-border bg-slate-50 h-flex dark:bg-dark-6 dark:border-dark-6"
            >
                <div class="bg-white h-full pr-2 pb-4 dark:bg-dark-3">
                    <ActivityLogDetailedListSection :model="{ model_name: 'Agent', model_pk: agentPk }" :header="'change log'" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import AgentDevicesBlock from '@/views/agents/agent/blocks/AgentDevicesBlock.vue'
import AgentHistoryBlock from '@/views/agents/agent/blocks/AgentHistoryBlock.vue'
import AgentActivityBlock from '@/views/agents/agent/blocks/AgentActivityBlock.vue'
import AgentTimeTrackingBlock from '@/views/agents/agent/blocks/AgentTimeTrackingBlock.vue'
import Tabs from '@/components/tabs/Tabs.vue'
import AgentPayHubSection from '~/sections/Agent/Info/AgentPayHubSection.vue'
import SkillsChartSection from '~/sections/Agent/Info/SkillsChartSection.vue'
import AgentProfileSection from '~/sections/Agent/Info/AgentProfileSection.vue'
import ActivityLogDetailedListSection
    from '~/sections/ActivityLogDetailed/components/ActivityLogDetailedListSection.vue'
import AgentAbilitiesSection from '~/sections/Agent/Info/AgentAbilitiesSection.vue'

defineOptions({
    name: 'AgentInfoSection',
})

const props = defineProps<{
    agentPk: PrimaryKey,
}>()

const {
    useDictionary,
    hasPermission,
} = useContext()

const canManageAll = hasPermission('manage', 'all')
const agent = useDictionary('Agent').find(props.agentPk)

const tabs = computed(() => {
    return [
        {
            title: 'Profile',
            id: 'profile',
        },
        canManageAll ? {
            title: 'Devices',
            id: 'devices',
        } : undefined,
        canManageAll ? {
            title: 'Access log',
            id: 'access-log',
        } : undefined,
        canManageAll ? {
            title: 'Activity log',
            id: 'activity-log',
        } : undefined,

        canManageAll ? {
            title: 'Abilities',
            id: 'abilities',
        } : undefined,
        canManageAll ? {
            title: 'Time Tracking',
            id: 'time-tracking',
        } : undefined,
        {
            title: 'Pay Hub',
            id: 'payhub',
        },
        hasPermission('viewSkillChart', 'Agent', agent) ? {
            title: 'Skills Chart',
            id: 'skills-chart',
        } : undefined,
        hasPermission('manage', 'all') ? {
            title: 'Change log',
            id: 'activity-logs',
        } : undefined,
    ].filter(Boolean)
})

//

const currentTab = ref(getDefaultTab())

function getDefaultTab() {
    const hash = window.location.hash
    const match = hash.match(/\btab-([\w-]+)/)

    const tab = match && match[1]

    if (tab && tabs.value.some(t => t.id === tab)) {
        return tab
    }

    return 'profile'
}

watch(() => currentTab.value, () => {
    window.location.hash = '#tab-' + currentTab.value
})
</script>
