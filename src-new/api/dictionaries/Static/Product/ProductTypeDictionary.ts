import Dictionary from '~/api/dictionaries/Dictionary'
import type SelectOption from '~types/structures/SelectOption'
import type { ForSelectMapperOptions } from '~/api/dictionaries/DictionaryAdapter'
import { ProductType } from '~/api/models/Product/Product'

interface Item {
    title: string,
    value: ProductType,
}

type Adapters = {
    forSelect: (record: Item, options?: ForSelectMapperOptions) => SelectOption,
}

export default class ProductTypeDictionary extends Dictionary<Item, Adapters> {
    public static indexBy = ['value']

    public records: Item[] = [

        {
            title: 'Baggage',
            value: ProductType.Baggage,
        },
        {
            title: 'Commission/Refund',
            value: ProductType.CommissionRefund,
        },
        {
            title: 'Tips',
            value: ProductType.Tips,
        },
        {
            title: 'Miles',
            value: ProductType.Miles,
        },
        {
            title: 'Ticket',
            value: ProductType.Ticket,
        },
        {
            title: 'CheckIn',
            value: ProductType.CheckIn,
        },
        {
            title: 'Insurance',
            value: ProductType.Insurance,
        },
        {
            title: 'Ticket Refund',
            value: ProductType.TicketRefund,
        },
        {
            title: 'Upgrade',
            value: ProductType.Upgrade,
        },
        {
            title: 'Upgrade Ticket',
            value: ProductType.UpgradeTicket,
        },
        {
            title: 'Cash Upgrade',
            value: ProductType.CashUpgrade,
        },
        {
            title: 'Award Upgrade',
            value: ProductType.AwardUpgrade,
        },
        {
            title: 'Voucher',
            value: ProductType.Voucher,
        },
        {
            title: 'Other',
            value: ProductType.Other,
        },

    ]

    public adapters = {
        forSelect: (record: Item, options?: ForSelectMapperOptions): SelectOption => {
            return {
                title: record.title,
                value: record.value,
            }
        },
    }

    public get mapRecordsForFilter() {
        return this.adapter.mapRecords(this.records.filter((record) => {
            return ![ProductType.TicketRefund, ProductType.CommissionRefund].includes(record.value)
        }))
    }

    public find(value: ProductType) {
        return super.findIndexed('value', value)
    }

    public get mapRecordsForFilterHasUpgrade() {
        return this.adapter.mapRecords(this.records.filter(record => {
            return [ProductType.CashUpgrade, ProductType.AwardUpgrade].includes(record.value)
        }))
    }
}
