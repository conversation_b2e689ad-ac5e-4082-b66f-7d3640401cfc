import type { Definition } from '~types/lib/Model'

const resources = {
    'ProjectList': defineDictionaryResource(),
    'CountryList': defineDictionaryResource(),
    'RegionList': defineDictionaryResource(),
    'AirlineList': defineDictionaryResource(),
    'CityList': defineDictionaryResource(),
    'ConsolidatorList': defineDictionaryResource(),
    'ConsolidatorAreaList': defineDictionaryResource(),
    'PositionList': defineDictionaryResource(),
    'DepartmentList': defineDictionaryResource(),
    'AgentList': defineDictionaryResource(),
    'OfficeList': defineDictionaryResource(),

    'DepartmentSkillList': defineResource<{
        list: PrimaryKey[]
    }>(),

    'AgentAgentSkillList': defineResource(),

    'TeamList': defineDictionaryResource(),
    'TeamAgentList': defineResource(),

    // Ticket
    'TicketExtraPreferenceList': defineDictionaryResource(),

    // Chat
    'ChatRecentList': defineResource({
        pk: ['gateway_pk', 'auth_pk'],
        cacheTTLms: Timespan.minutes(6).inMilliseconds,
        observable: true,
    }),
    'ChatChatMessageList': defineResource({
        pk: ['id', 'auth_pk'],
        cacheTTLms: Timespan.minutes(6).inMilliseconds,
        observable: true,
        chunkBy: 30,
    }),
    'ChatGatewayList': defineDictionaryResource(),
    'ChatChatMessagePinnedList': defineResource<{
        list: PrimaryKey[]
    }>(),

    // Call
    'ClientPhoneCallContactAttemptList': defineResource(),

    // Email
    'EmailEmailContactAttemptConversationList': defineResource(),
    'EmailContactAttemptConversationEmailContactAttemptList': defineResource<{
        list: PrimaryKey[]
    }>(),

    // Client
    'ClientStatusList': defineDictionaryResource(),
    'ClientClientPassportList': defineResource(),
    'ClientTravelerList': defineResource(),
    'ClientClientPhoneDataList': defineResource(),
    'ClientClientEmailDataList': defineResource(),
    'ClientLeadList': defineResource(),

    // Bookkeeping
    'BookkeepingTransactionBookkeepingTransactionAssignmentList': defineResource(),
    'BookkeepingInvoiceBookkeepingTransactionAssignmentList': defineResource(),
    'BookkeepingInvoiceInhouseBookkeepingTransactionAssignmentList': defineResource(),
    'BookkeepingTransactionInhouseBookkeepingTransactionAssignmentList': defineResource(),
    'BookkeepingTransactionChildrenList': defineResource(),
    'BookkeepingInvoiceBookkeepingInvoiceDocumentList': defineResource(),
    'BookkeepingPaymentGatewayList': defineDictionaryResource(),
    'PaymentGatewayList': defineDictionaryResource(),

    // Sale
    'SaleProjectCardList': defineResource(),
    'SaleVersionSaleVersionCardList': defineResource(),
    'SaleCompanyCardList': defineResource(),
    'SaleBookkeepingTransactionAssignmentList': defineResource(),
    'SaleInhouseBookkeepingTransactionAssignmentList': defineResource(),
    'SaleOtherBookkeepingTransactionAssignmentList': defineResource(),
    'SaleBookkeepingInvoiceList': defineResource(),
    'SaleInhouseBookkeepingInvoiceList': defineResource(),
    'SaleOtherBookkeepingInvoiceList': defineResource(),
    'SaleSaleVersionList': defineResource(),
    'SaleVersionSaleVersionPassengerList': defineResource(),
    'SaleVersionSaleVersionPnrList': defineResource(),
    'SaleVersionPnrSaleVersionPnrInfoList': defineResource(),
    'SaleVersionSaleClientRequestList': defineResource(),
    'SaleVersionSignedDocumentList': defineResource(),
    'SaleTaskGroupList': defineResource(),
    'SaleSaleMemberList': defineResource(),
    'SaleDraftList': defineDictionaryResource(),
    'SaleVersionProductList': defineResource(),
    'SaleVoucherList': defineResource(),
    'SaleSaleTransactionList': defineResource(),

    // Voucher
    'VoucherVoucherPnrAdditionalList': defineResource(),
    // Lead
    'LeadSaleList': defineResource(),
    'LeadPriceQuoteList': defineResource({
        isArchivable: ['Lead'],
    }),
    'LeadTaskGroupList': defineResource(),
    'LeadStatusList': defineDictionaryResource(),
    'LeadLeadDuplicateList': defineResource(),
    'PinnedAgentLeadsList': defineResource({
        pk: ['agent_pk'],
    }),
    'LeadIssueList': defineResource({
        pk: ['lead_pk'],
    }),
    'LeadLeadOfferList': defineResource({
        isArchivable: ['Lead'],
    }),
    // Award
    'MilePriceProgramList': defineDictionaryResource(),
    'AwardAccountAwardAccountIncomingRequestList': defineResource(),
    'AwardAccountAwardAccountOutgoingRequestList': defineResource(),
    'AwardAccountAwardAccountHolderList': defineResource(),
    // 'AwardAccountAwardAccountEmailList': defineResource(),

    // Tasks
    'TaskGroupTaskList': defineResource(),

    // Airline Report
    'AirlineReportAirlineReportVersionList': defineResource(),

    // Airline Case
    'AirlineCaseExpectedAmountList': defineResource(),

    // Agent report
    'AgentReportAgentReportInvoiceList': defineResource(),
    'AgentReportInvoiceAgentReportSaleList': defineResource(),
    'AgentReportInvoiceAgentReportAdditionalList': defineResource(),
    'AgentReportInvoiceAgentReportTicketingList': defineResource(),
    'AgentReportInvoiceAgentReportInternalProfitList': defineResource(),

    // Contact Store
    'CompanyContactAirlineCompanyContactList': defineResource(),

    // Request
    'IssueAirlineCaseList': defineResource(),
    'IssueExpectedAmountList': defineResource(),
    'IssueGroupList': defineDictionaryResource(),
    'IssueTaskGroupList': defineResource(),

    // Release Post
    'ReleasePostReleasePostItemList': defineResource(),
    'ReleasePostItemReleasePostItemFeedbackList': defineResource(),
    'ReleasePostItemFeedbackReleasePostItemFeedbackReplyList': defineResource(),

    // PriceDrop
    'PriceDropCheckPriceDropOfferList': defineResource(),

    // External user agent
    'ExternalUserAgentMilePriceProgramList': defineResource(),

    // Gambling Lot
    'GamblingLotGamblingLotOptionList': defineResource(),
    'GamblingLotGamblingLotBetList': defineResource(),

    // Terminal
    'TerminalHotkeyList': defineResource({
        pk: ['auth_pk'],
    }),

} satisfies { [resource in ResourceName]?: Definition.Resource }

export default resources
