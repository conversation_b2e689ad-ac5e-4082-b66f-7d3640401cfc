import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        AwardAccountOutgoingRequest: AwardAccountOutgoingRequest
    }
}

export default class AwardAccountOutgoingRequest implements Definition.Model {
    public readonly name = 'AwardAccountOutgoingRequest'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        miles_amount: z.number(),
        price: z.number(),
        remark: z.string(),
        created_at: z.timestamp(),
        is_blocked: z.boolean(),
        is_refund: z.boolean(),
        refunded_pk: z.pk().nullable(),

        award_account_pk: z.pk(),
        product_pk: z.pk().nullable(),
        parent_pk: z.pk().nullable(),
    })

    public relations = defineRelations({
        product: belongsTo('Product', 'product_pk'),
    })

    public actions = defineActions({
        //
        editRemark: {
            params: z.object({
                pk: z.pk(),
                remark: z.string().nullable(),
            }),
            response: z.null(),
        },
        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
