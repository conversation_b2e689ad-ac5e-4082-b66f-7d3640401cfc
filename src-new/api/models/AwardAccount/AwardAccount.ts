import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        AwardAccount: AwardAccount
    }
}

export enum AwardAccountStatus {
    Active = 'is-active', // dont have outgoing requests
    InUse = 'is-use', //  have outgoing requests, but their sales are adjusted
    Pending = 'is-pending', // have outgoing requests with not adjusted sales
    Blocked = 'is-blocked', // restricted to use
    Lost = 'is-lost', //
    Leftover = 'is-leftover', // is set manually
    Used = 'is-used', // is set manually
    Sold = 'is-sold', // is set manually
}

export enum AwardAccountType {
    Inhouse = 'in-house',
    Consolidator = 'consolidator',
    Pax = 'pax',
    Other = 'other',
}

export enum AwardAccountWarningFlag {
    DoNotUse = 'do-not-use',
    UnderRisk = 'under-risk',
    Chargeback = 'chargeback',
    CantUse = 'cant-use',
}

export default class AwardAccount implements Definition.Model {
    public readonly name = 'AwardAccount'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),
        status: z.nativeEnum(AwardAccountStatus),
        type: z.nativeEnum(AwardAccountType),
        is_full_editable: z.boolean(),

        account_number: z.string(),

        vpn: z.string(),
        external_user_agent_pk: z.pk().nullable(),
        // award_account_email_pk: z.pk(),

        remark: z.string().nullable(),

        balance: z.number(),
        cpm: z.number(),

        holder_pk: z.string(),
        mile_price_program_pk: z.pk(),
        consolidator_pk: z.pk().nullable(),
        consolidator_area_pk: z.pk().nullable(),

        parent_award_account_pk: z.pk().nullable(),
        parent_award_account_relation: z.string().nullable(),

        //

        in_use_till_date: z.timestamp().nullable(),
        last_sale_pk: z.pk().nullable(),

        active_sale_count: z.int(),
        refund_sale_count: z.int(),
        exchange_sale_count: z.int(),
        total_sale_count: z.int(),
        top_up_count: z.int(),

        //

        is_booking_type_online: z.boolean(),
        is_booking_type_phone: z.boolean(),
        is_booking_type_special: z.boolean(),

        //

        warning_flag: z.nativeEnum(AwardAccountWarningFlag).nullable(),

        //

        is_reserved: z.boolean(),
        group_pk: z.pk().nullable(),

        // Other
        project_pk: z.pk(),
    })

    public searchFields = z.object({
        id: z.number(),
        vpn: z.string(),
        keywords: z.string(),
        balance: z.number(),
        is_deleted: z.boolean(),
        mile_price_program_pk: z.pk(),
        consolidator_area_pk: z.pk(),
        holder_name: z.string(),
        type: z.nativeEnum(AwardAccountType),
        consolidator_pk: z.pk(),
        status: z.nativeEnum(AwardAccountStatus),
        account_number: z.string(),
        is_booking_type_online: z.boolean(),
        is_booking_type_phone: z.boolean(),
        is_booking_type_special: z.boolean(),
        is_reserved: z.boolean(),
        booking_type: z.null(),
        group_account_number: z.string(),
        alt_vpn: z.boolean(),
    })

    public relations = defineRelations({
        parent: belongsTo('AwardAccount', 'parent_award_account_pk'),
        credentials: belongsTo('AwardAccountAdditionalCredentials', 'id'),
        incoming: hasManyThrough('AwardAccountIncomingRequest', 'AwardAccountAwardAccountIncomingRequestList', 'award_account_pk'),
        outgoing: hasManyThrough('AwardAccountOutgoingRequest', 'AwardAccountAwardAccountOutgoingRequestList', 'award_account_pk'),

        holder: belongsTo('AwardAccountHolder', 'holder_pk'),
        holders: hasManyThrough('AwardAccountHolder', 'AwardAccountAwardAccountHolderList', 'award_account_pk'),

        groupAccount: belongsTo('AwardAccount', 'group_pk'),
        // email: belongsTo('AwardAccountEmail', 'award_account_email_pk'),
        // emails: hasManyThrough('AwardAccountEmail', 'AwardAccountAwardAccountEmailList', 'award_account_pk'),
        externalUserAgent: belongsTo('ExternalUserAgent', 'external_user_agent_pk'),
    })

    public actions = defineActions({
        changeStatus: {
            params: z.object({
                pk: z.pk(),
                status: z.nativeEnum(AwardAccountStatus),
            }),
            response: z.null(),
        },

        rejectSale: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        addRemark: {
            params: z.object({
                pk: z.pk(),
                remark: z.string(),
            }),
            response: z.null(),
        },

        create: {
            params: z.object({
                data: AwardAccountCrudFields,
                holders: z.array(AwardAccountHolderCrudFields),
            }),
            response: z.null(),
        },

        update: {
            params: z.object({
                pk: z.pk(),
                data: AwardAccountCrudFields,
                holders: z.array(AwardAccountHolderCrudFields),
            }),
            response: z.null(),
        },

        addMiles: {
            params: z.object({
                pk: z.pk(),
                data: z.object({
                    consolidator_pk: z.pk().nullable(),
                    consolidator_area_pk: z.pk().nullable(),
                    amount: z.number(),
                    cpm: z.number(),
                    remark: z.string(),
                }),
            }),
            response: z.null(),
        },

        moveMiles: {
            params: z.object({
                from_pk: z.pk(),
                to_pk: z.pk(),
                miles_amount: z.number(),
                fee_amount: z.number(),
                vcc_card_pk: z.pk().nullable(),
                airline_pk: z.pk().nullable(),
            }),
            response: z.null(),
        },

        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },

        pay: {
            params: z.object({
                pk: z.pk(),
                tickets: z.array(z.pk()),
            }),
            response: z.null(),
        },

        clearOutgoingRequests: {
            params: z.object({
                pk: z.pk(),
                tickets: z.array(z.pk()),
            }),
            response: z.null(),
        },

        accountsMatchForSale: {
            params: z.object({
                sale_pk: z.pk(),
                ticket_pk: z.pk(),
            }),
            response: z.array(z.object({
                pk: z.pk(),
                holder: z.boolean(),
                client: z.boolean(),
            })),
        },

        setInitialBookingState: {
            params: z.object({
                pk: z.pk(),
                in_use_till_date: z.timestamp().nullable(),

                active_sale_count: z.int(),
                refund_sale_count: z.int(),
                exchange_sale_count: z.int(),
                top_up_count: z.int(),
                total_sale_count: z.int(),
            }),
            response: z.null(),
        },

        refundTicket: {
            params: z.object({
                pk: z.pk(),
                ticket_pk: z.pk(),
                miles_count: z.int().nullable(),
                remark: z.string(),
                is_full_refund: z.boolean(),
            }),
            response: z.null(),
        },

        getMilesMaxValue: {
            params: z.object({}),
            response: z.int(),
        },

        getSummary: {
            params: z.object({
                search_params: z.any(),
            }),
            response: z.object({
                points: z.number(),
                account_count: z.int(),
            }),
        },
    })
}

const AwardAccountCrudFields = z.object({
    type: z.nativeEnum(AwardAccountType),
    //
    account_number: z.string(),
    //
    vpn: z.string(),
    remark: z.string().nullable(),
    //
    mile_price_program_pk: z.pk(),
    consolidator_pk: z.pk().nullable(),
    //
    parent_award_account_pk: z.pk().nullable(),
    parent_award_account_relation: z.string().nullable(),
    //
    is_booking_type_online: z.boolean(),
    is_booking_type_phone: z.boolean(),
    is_booking_type_special: z.boolean(),
    //
    warning_flag: z.nativeEnum(AwardAccountWarningFlag).nullable(),
    //
    is_reserved: z.boolean(),

    // Credentials
    password: z.string(),

    phone: z.string().nullable(),
    is_our_phone: z.boolean(),

    email: z.string(),
    is_our_email: z.boolean(),

    street: z.string().nullable(),
    city: z.string().nullable(),
    state: z.string().nullable(),
    zip: z.string().nullable(),
    country: z.string().nullable(),
    external_user_agent_pk: z.pk().nullable(),
})

export const AwardAccountHolderCrudFields = z.object({
    first_name: z.string(),
    last_name: z.string(),
    birth_date: z.timestamp(),
    relation: z.string(),
    blocked_till_date: z.timestamp().nullable(),
}).describe('AwardAccountHolderCrudFields')
