import { ClientStatusName } from '~/api/models/Client/ClientStatus'
import { IssueActionData, IssueResult } from '~/api/models/Issue/Issue'
import type { Definition } from '~types/lib/Model'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'

declare global {
    export interface Models {
        Sale: Sale
    }
}

export enum SaleType {
    Sale = 'sale',
    Exchange = 'exchange',
    InvoiceExchange = 'inv_exchange',
    Refund = 'refund',
    InvoiceRefund = 'inv_refund',
}

export enum SaleCategoryType {
    Revenue = '0',
    Award = '1',
    Mix = '2'
}

export enum ExpertType {
    LeadExpert = 'expert',
    PqExpert = 'pqExpert',
    Executor = 'executor'
}

export enum SaleVersionPayType {
    CC = 'CC',
    WireTransfer = 'Wire Transfer'
}

export const ShareExpertGpMemberActionData = z.object({
    agent_pk: z.pk(),
    ticket_profit: z.number(),
    expert_type: z.nativeEnum(ExpertType),
}).describe('ShareExpertGpMemberActionData')

// expert manager - change, experts - view

export const FetchExpertMembers = z.object({
    pk: z.pk(),
    sale_pk: z.pk(),
    agent_pk: z.pk(),
    category: z.nativeEnum(ExpertType).nullable(),
    ticket_profit_ps: z.number(),
}).describe('FetchExpertMembers')

export const FetchSaleSummaryMember = z.object({
    agent_pk: z.pk(),
    percent: z.number(),
}).describe('FetchSaleSummaryMember')

export default class Sale implements Definition.Model {
    public readonly name = 'Sale'
    public readonly pk = 'id'
    public readonly chunkBy = 30
    public readonly defaultOrderBy = { id: 'desc' } as const

    public fields = z.object({
        id: z.int(),
        client_pk: z.pk(),
        executor_pk: z.pk(),
        old_bo_id: z.int(),
        sale_at: z.timestamp(),
        sale_version_pk: z.pk(),
        lead_pk: z.pk(),
        project_pk: z.pk(),
        chat_pk: z.pk().nullable(),
        type: z.nativeEnum(SaleType),
        is_adjusted: z.boolean(), // !?
        is_split: z.boolean(),
        is_hidden: z.boolean(),
        is_paid_fully: z.boolean(),
        is_ticket_sent: z.boolean(),
        is_paid_bsb: z.boolean(),
        is_sale_closed: z.boolean(),
        is_sale_adjusted: z.boolean(), // !?
        is_product_description: z.boolean(),
        is_pnr_provided: z.boolean(),
        is_customer_verified: z.boolean(),
        is_sale_approved: z.boolean(),
        is_sale_issued: z.boolean(),
        is_sale_charged: z.boolean(),
        is_finances_verified: z.boolean(),
        is_pending_approval: z.boolean(),
        is_connex_pay_enabled: z.boolean(),
        is_enable_automatic_transactions: z.boolean(),
        closed_by_pk: z.pk().nullable(),
        client_session_activity_pk: z.pk().nullable(),
        external_resource: z.softEnum(ExternalResource),
        is_test: z.boolean(),
    })

    public searchFields = z.object({
        id: z.int(),
        lead_pk: z.pk(),
        old_bo_id: z.int(),
        sale_at: z.timestamp(),
        executor_pk: z.pk(),
        project_pk: z.pk(),
        summary_pnr: z.string(),
        summary_vc: z.string(),
        client_name: z.string(),
        passenger_name: z.string(),
        departure_at: z.timestamp(),
        from_iata_code: z.string(),
        to_iata_code: z.string(),
        summary_net_price: z.number(),
        summary_sell_price: z.number(),
        summary_fee: z.number(),
        summary_ticket_profit: z.number(),
        summary_insurance_profit: z.number(),
        summary_baggage_profit: z.number(),
        summary_tips_profit: z.number(),
        summary_profit: z.number(),
        sale_category: z.nativeEnum(SaleCategoryType),
        sale_type: z.nativeEnum(SaleType),
        debt_amount: z.number(),
        is_paid_fully: z.boolean(),
        is_paid_bsb: z.boolean(),
        is_product_description: z.boolean(),
        is_pnr_provided: z.boolean(),
        is_customer_verified: z.boolean(),
        is_sale_approved: z.boolean(),
        is_sale_issued: z.boolean(),
        is_sale_charged: z.boolean(),
        is_sale_adjusted: z.boolean(),
        is_ticket_sent: z.boolean(),
        is_finances_verified: z.boolean(),
        is_sale_closed: z.boolean(),

        keywords: z.searchKeywords(),
        client_pk: z.pk(),
        executor_name: z.string(),
        issued_at: z.timestamp(),
        can_be_added_to_agent_invoice_pk: z.pk(),
        is_split: z.boolean(),
        search_ticket: z.number(),
        lead_created_at: z.timestamp(),
        created_by_name: z.string(),
        version_pay_type: z.nativeEnum(SaleVersionPayType),
        is_hidden: z.boolean(),
        is_pending_approval: z.boolean(),
        client_phone: z.string(),
        client_email: z.string(),
        created_at: z.timestamp(),
        return_at: z.timestamp(),
        lead_email: z.string(),
        lead_phone: z.string(),
        is_has_debt: z.boolean(),
        utm_source: z.string(),
        utm_campaign: z.string(),
        utm_ga: z.string(),
        utm_medium: z.string(),
        utm_term: z.string(),
        external_resource: z.softEnum(ExternalResource),
        sale_executor_client_status_pk: z.softEnum(ClientStatusName),
        lastChatMessageTime: z.timestamp(),
        chatMessageText: z.string(),
        last_pinned_message: z.string(),
        passenger_name_or_client_name: z.string(),
        consolidator_pk: z.pk(),
        has_upgrade: z.string(),
        has_segment_type: z.string(),
    })

    public relations = defineRelations({
        saleVersions: hasManyThrough('SaleVersion', 'SaleSaleVersionList', 'sale_pk'),
        saleVersion: belongsTo('SaleVersion', 'sale_version_pk'),
        info: belongsTo('SaleAdditionalInfo', 'id'),
        chat: belongsTo('Chat', 'chat_pk'),
        client: belongsTo('Client', 'client_pk'),
        clientPreview: belongsTo('ClientPreview', 'client_pk'),
        lead: belongsTo('Lead', 'lead_pk'),
        executor: belongsTo('Agent', 'executor_pk'),
        profits: belongsTo('SaleAdditionalProfits', 'id'),
        saleProjectCards: hasManyThrough('ProjectCard', 'SaleProjectCardList', 'sale_pk'),
        saleCompanyCards: hasManyThrough('ProjectCard', 'SaleCompanyCardList', 'sale_pk'),
        inhouseTransactionAssignments: hasManyThrough('BookkeepingTransactionAssignment', 'SaleInhouseBookkeepingTransactionAssignmentList', 'sale_pk'),
        otherTransactionAssignments: hasManyThrough('BookkeepingTransactionAssignment', 'SaleOtherBookkeepingTransactionAssignmentList', 'sale_pk'),
        inhouseInvoices: hasManyThrough('BookkeepingInvoice', 'SaleInhouseBookkeepingInvoiceList', 'sale_pk'),
        otherInvoices: hasManyThrough('BookkeepingInvoice', 'SaleOtherBookkeepingInvoiceList', 'sale_pk'),
        taskGroups: hasManyThrough('TaskGroup', 'SaleTaskGroupList', morphKey),
        members: hasManyThrough('SaleMember', 'SaleSaleMemberList', 'sale_pk'),
        vouchers: hasManyThrough('Voucher', 'SaleVoucherList', 'sale_pk'),
        saleTransactions: hasManyThrough('SaleTransaction', 'SaleSaleTransactionList', 'sale_pk'),
    })

    public actions = defineActions({
        createRefundFromClient: {
            params: z.object({
                client_pk: z.pk(),
                pax_count: z.int(),
            }),
            response: z.object({
                sale_pk: z.pk(),
            }),
        },
        clone: {
            params: z.object({
                sale_pk: z.pk(),
                type: z.nativeEnum(SaleType),
            }),
            response: z.object({
                sale_pk: z.pk(),
            }),
        },
        addDisclaimer: {
            params: z.object({
                task_pk: z.pk(),
                remark: z.string().nullable(),
                file_pks: z.array(z.pk()),
            }),
            response: z.null(),
        },
        shareSale: {
            params: z.object({
                sale_pk: z.pk(),
                lead_pk: z.pk(),
                agent_pk: z.pk(),
            }),
            response: z.null(),
        },
        updateShareSale: {
            params: z.object({
                sale_pk: z.pk(),
                result: z.namedUnion(IssueResult),
                issue_data: IssueActionData,
            }),
            response: z.null(),
        },
        shareExpertGp: {
            params: z.object({
                sale_pk: z.pk(),
                experts_data: z.array(ShareExpertGpMemberActionData),
            }),
            response: z.null(),
        },
        fetchSaleExpertMembers: {
            params: z.object({
                sale_pk: z.pk(),
            }),
            response: z.object({
                expertMembers: z.array(FetchExpertMembers),
            }),
        },
        fetchSaleActivityMembers: {
            params: z.object({
                sale_pk: z.pk(),
            }),
            response: z.object({
                sale_activity_members_pks: z.array(z.pk()),
            }),
        },

        fetchSaleSummaryMembers: {
            params: z.object({
                from_date: z.timestamp(),
                to_date: z.timestamp(),
            }),
            response: z.object({
                sale_summary_members: z.array(FetchSaleSummaryMember),
            }),
        },

        updateSaleWithType: {
            params: z.object({
                sale_pk: z.pk(),
                task_pk: z.pk().nullable(),
                type: z.nativeEnum(SaleType),
                sales: z.array(z.object({
                    sale_pk: z.pk().nullable(),
                    passengers_pks: z.array(z.pk()).nullable(),
                })).nullable(),
            }),
            response: z.null(),
        },

        createNewSaleWithType: {
            params: z.object({
                sale_pk: z.pk(),
                type: z.nativeEnum(SaleType),
                passengers_pks: z.array(z.pk()).nullable(),
                issue_pk: z.pk().nullable(),
                executor_pk: z.pk(),
                with_payments: z.boolean().nullable(),
            }),
            response: z.object({
                sale_pk: z.pk(),
            }),
        },

        createNewSaleForClient: {
            params: z.object({
                client_pk: z.pk(),
                type: z.nativeEnum(SaleType),
                sale_pk: z.pk().nullable(),
                passengers_pks: z.array(z.pk()).nullable(),
                executor_pk: z.pk(),
                with_payments: z.boolean().nullable(),
            }),
            response: z.object({
                sale_pk: z.pk(),
            }),
        },

        fetchSaleListExcel: {
            params: z.object({
                search_params: z.any(),
            }),
            response: z.object({
                result: z.string(),
            }),
        },

        // Markups
        mixerMarkupRequest: {
            params: z.object({
                url: z.string(),
                options: z.any(),
            }),
            response: z.custom<AnyObject>(),
        },

        sendPriceDropStagingTest: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}

