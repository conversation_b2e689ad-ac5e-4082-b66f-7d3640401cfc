import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        TerminalHotkey: TerminalHotkey
    }
}

export default class TerminalHotkey implements Definition.Model {
    public readonly name = 'TerminalHotkey'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        title: z.string(),
        description: z.string().nullable(),
        command: z.string(),
        autorun: z.boolean(),
        keys: z.array(z.string()),

        consolidator_system_name: z.string(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        create: {
            params: z.object({
                title: z.string(),
                description: z.string().nullable(),
                command: z.string(),
                autorun: z.boolean(),
                keys: z.array(z.string()),
                consolidator_system_name: z.string(),
            }),
            response: z.object({
                pk: z.pk(),
            }),
        },

        update: {
            params: z.object({
                pk: z.pk(),
                title: z.string(),
                description: z.string().nullable(),
                command: z.string(),
                autorun: z.boolean(),
                keys: z.array(z.string()),
            }),
            response: z.null(),
        },

        delete: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
    })
}
