import type { Definition } from '~types/lib/Model'

declare global {
    export interface Models {
        ExternalUserAgentProxy: ExternalUserAgentProxy
    }
}

export enum ExternalUserAgentProxyType {
    Multilogin = 'multilogin',
    NodeMaven = 'node_maven'
}

export default class ExternalUserAgentProxy implements Definition.Model {
    public readonly name = 'ExternalUserAgentProxy'
    public readonly pk = 'id'

    public fields = z.object({
        id: z.int(),

        type: z.nativeEnum(ExternalUserAgentProxyType),

        location: z.object({
            country: z.string(),
            city: z.string().nullable(),
            region: z.string().nullable(),
        }),

        credential: z.object({
            protocol: z.enum(['http', 'socks5']),
            username: z.string(),
            password: z.string(),
            host: z.string(),
            port: z.int(),
        }).nullable(),
    })

    public relations = defineRelations({
        //
    })

    public actions = defineActions({
        generateProxy: {
            params: z.object({
                type: z.nativeEnum(ExternalUserAgentProxyType),
                protocol: z.enum(['http', 'socks5']).nullable(),
                location: z.object({
                    country: z.string().nullable(),
                    city: z.string().nullable(),
                    region: z.string().nullable(),
                }).nullable(),
            }),
            response: z.object({
                protocol: z.enum(['http', 'socks5']),
                host: z.string(),
                port: z.int(),
                username: z.string(),
                password: z.string(),
            }),
        },

        getProxyLocationOptions: {
            params: z.object({}),
            response: z.object({
                multilogin_countries: z.array(z.object({
                    title: z.string(),
                    value: z.string(),
                    regions: z.array(z.object({
                        title: z.string(),
                        value: z.string(),
                    })),
                    cities: z.array(z.object({
                        title: z.string(),
                        value: z.string(),
                        region: z.string(),
                    })),
                })),
                node_maven_countries: z.array(z.object({
                    title: z.string(),
                    value: z.string(),
                    regions: z.array(z.object({
                        title: z.string(),
                        value: z.string(),
                    })),
                    cities: z.array(z.object({
                        title: z.string(),
                        value: z.string(),
                        region: z.string(),
                    })),
                })),
            }),
        },
    })
}
