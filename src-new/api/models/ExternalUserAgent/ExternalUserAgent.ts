import type { Definition } from '~types/lib/Model'
import { ExternalUserAgentProxyType } from '~/api/models/ExternalUserAgent/ExternalUserAgentProxy'

declare global {
    export interface Models {
        ExternalUserAgent: ExternalUserAgent
    }
}

export enum ExternalUserAgentStatus {
    Pending = 'pending',
    Approved = 'approved',
    Rejected = 'rejected',
    Deleted = 'deleted',
}

export const ExternalUserAgentAdvancedSettings = z.object({
    screen_masking: z.enum(['mask', 'custom', 'natural']),
    screen_resolution_custom: z.string().nullable(),

    media_devices_masking: z.enum(['mask', 'custom', 'natural']),
    video_input_count: z.number().nullable(),
    audio_input_count: z.number().nullable(),
    audio_output_count: z.number().nullable(),

    graphics_noise: z.enum(['mask', 'natural']),
    graphics_masking: z.enum(['mask', 'natural']),
    canvas_noise: z.enum(['mask', 'natural']),
    audio_masking: z.enum(['noise', 'natural']),

    navigator_masking: z.enum(['mask', 'custom', 'natural']),
    user_agent: z.string().nullable(),
    platform: z.string().nullable(),
    hardware_concurrency: z.number().nullable(),
    oscpu: z.string().nullable(),

    ports_masking: z.enum(['mask', 'custom', 'natural']),
    whitelisted_ports: z.string().nullable(),

    fonts_masking: z.enum(['mask', 'natural']),

    browser_type: z.enum(['mimic', 'stealthfox']),
}).describe('ExternalUserAgentAdvancedSettings')

export default class ExternalUserAgent implements Definition.Model {
    public readonly name = 'ExternalUserAgent'
    public readonly pk = 'id'
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        status: z.nativeEnum(ExternalUserAgentStatus),
        name: z.string(),
        note: z.string().nullable(),

        profile_id: z.string(),
        folder_id: z.string(),

        created_by_pk: z.pk(),
        created_at: z.timestamp(),
        last_opened_at: z.timestamp().nullable(),
        last_opened_by_pk: z.pk().nullable(),

        advanced_settings: ExternalUserAgentAdvancedSettings,
        external_user_agent_proxy_pk: z.pk().nullable(),
    })

    public searchFields = z.object({
        status: z.nativeEnum(ExternalUserAgentStatus),
    })

    public relations = defineRelations({
        createdBy: belongsTo('Agent', 'created_by_pk'),
        lastOpenedBy: belongsTo('Agent', 'last_opened_by_pk'),
        milePricePrograms: hasManyThrough('MilePriceProgram', 'ExternalUserAgentMilePriceProgramList', 'id'),
        proxy: belongsTo('ExternalUserAgentProxy', 'external_user_agent_proxy_pk'),
    })

    public actions = defineActions({
        create: {
            params: z.object({
                award_account_pk: z.pk().nullable(),
                name: z.string(),
                note: z.string().nullable(),
                advanced_settings: ExternalUserAgentAdvancedSettings,
                proxy: z.object({
                    type: z.nativeEnum(ExternalUserAgentProxyType),

                    location: z.object({
                        country: z.string(),
                        city: z.string().nullable(),
                        region: z.string().nullable(),
                    }).nullable(),

                    credential: z.object({
                        protocol: z.enum(['http', 'socks5']),
                        username: z.string(),
                        password: z.string(),
                        host: z.string(),
                        port: z.int(),
                    }).nullable(),
                }).nullable(),
            }),
            response: z.object({
                pk: z.pk(),
            }),
        },

        remove: {
            params: z.object({
                pk: z.pk(),
                award_account_pk: z.pk(),
            }),
            response: z.null(),
        },

        save: {
            params: z.object({
                pk: z.pk(),
                name: z.string(),
                note: z.string().nullable(),
                advanced_settings: ExternalUserAgentAdvancedSettings,
                proxy: z.object({
                    type: z.nativeEnum(ExternalUserAgentProxyType),

                    location: z.object({
                        country: z.string(),
                        city: z.string().nullable(),
                        region: z.string().nullable(),
                    }).nullable(),

                    credential: z.object({
                        protocol: z.enum(['http', 'socks5']),
                        username: z.string(),
                        password: z.string(),
                        host: z.string(),
                        port: z.int(),
                    }).nullable(),
                }).nullable(),
            }),
            response: z.null(),
        },

        linkWithAwardAccount: {
            params: z.object({
                award_account_pk: z.pk(),
                external_user_agent_pk: z.pk(),
            }),
            response: z.null(),
        },

        createFromUrl: {
            params: z.object({
                url: z.string(),
            }),
            response: z.object({
                pk: z.pk(),
            }),
        },

        getMultiLoginToken: {
            params: z.object({}),
            response: z.object({
                token: z.string(),
            }),
        },

        getNotUsedExternalUserAgentPks: {
            params: z.object({
                mile_price_program_pk: z.pk(),
            }),
            response: z.object({
                pks: z.array(z.pk()),
            }),
        },

        getMultiLoginFormOptions: {
            params: z.object({}),
            response: z.object({
                screen_resolutions: z.array(z.object({
                    title: z.string(),
                    value: z.string(),
                })),
                hardware_concurrencies: z.array(z.object({
                    title: z.string(),
                    value: z.string(),
                })),
                browsers: z.array(z.object({
                    title: z.string(),
                    value: z.string(),
                })),
            }),
        },

        getMultiLoginQuickProfileFormOptions: {
            params: z.object({}),
            response: z.object({
                os_types: z.array(z.object({ title: z.string(), value: z.string() })),
                browser_types: z.array(z.object({ title: z.string(), value: z.string() })),
            }),
        },

        updateIsApproval: {
            params: z.object({
                pk: z.pk(),
                state: z.boolean(),
            }),
            response: z.null(),
        },
    })
}
