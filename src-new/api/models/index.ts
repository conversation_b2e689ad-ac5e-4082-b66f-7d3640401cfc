// This file is auto generatedby vite-plugin-import-models
// Do not edit this file manually

import ActiveCSIssueListWithChats from '#/src-new/api/models/Issue/List/ActiveCSIssueListWithChats'
import ActiveIssueListWithChats from '#/src-new/api/models/Issue/List/ActiveIssueListWithChats'
import ActiveSaleListWithChats from '#/src-new/api/models/Sale/List/ActiveSaleListWithChats'
import ActivityLog from '#/src-new/api/models/Log/ActivityLog'
import ActivityLogDetailed from '#/src-new/api/models/Log/ActivityLogDetailed'
import Agent from '#/src-new/api/models/Agent/Agent'
import AgentAdditionalBonusInfo from '#/src-new/api/models/Agent/AgentAdditionalBonusInfo'
import AgentAdditionalGDSInfo from '#/src-new/api/models/Agent/AgentAdditionalGDSInfo'
import AgentAdditionalPersonalInfo from '#/src-new/api/models/Agent/AgentAdditionalPersonalInfo'
import AgentAdditionalSalaryInfo from '#/src-new/api/models/Agent/AgentAdditionalSalaryInfo'
import AgentAdditionalState from '#/src-new/api/models/Agent/AgentAdditionalState'
import AgentAdditionalTransferClientsInfo from '#/src-new/api/models/Agent/AgentAdditionalTransferClientsInfo'
import AgentAppSettings from '#/src-new/api/models/Agent/AgentAppSettings'
import AgentClientTransfer from '#/src-new/api/models/Agent/AgentClientTransfer'
import AgentLeadGiveRule from '#/src-new/api/models/Agent/AgentLeadGiveRule'
import AgentProjectSettings from '#/src-new/api/models/Agent/AgentProjectSettings'
import AgentReport from '#/src-new/api/models/AgentReport/AgentReport'
import AgentReportAdditional from '#/src-new/api/models/AgentReport/AgentReportAdditional'
import AgentReportInternalProfit from '#/src-new/api/models/AgentReport/AgentReportInternalProfit'
import AgentReportInvoice from '#/src-new/api/models/AgentReport/AgentReportInvoice'
import AgentReportSale from '#/src-new/api/models/AgentReport/AgentReportSale'
import AgentReportTicketing from '#/src-new/api/models/AgentReport/AgentReportTicketing'
import AgentShift from '#/src-new/api/models/Agent/AgentShift'
import AgentSkill from '#/src-new/api/models/Agent/AgentSkill'
import AgentTarget from '#/src-new/api/models/Agent/AgentTarget'
import Airline from '#/src-new/api/models/Airline/Airline'
import AirlineCase from '#/src-new/api/models/Airline/AirlineCase'
import AirlineCompanyContact from '#/src-new/api/models/CompanyContact/AirlineCompanyContact'
import AirlineReport from '#/src-new/api/models/AirlineReport/AirlineReport'
import AirlineReportVersion from '#/src-new/api/models/AirlineReport/AirlineReportVersion'
import AssignLog from '#/src-new/api/models/AssignLog/AssignLog'
import AudioTranscription from '#/src-new/api/models/AudioTranscription/AudioTranscription'
import AwardAccount from '#/src-new/api/models/AwardAccount/AwardAccount'
import AwardAccountAdditionalCredentials from '#/src-new/api/models/AwardAccount/AwardAccountAdditionalCredentials'
import AwardAccountHolder from '#/src-new/api/models/AwardAccount/AwardAccountHolder'
import AwardAccountIncomingRequest from '#/src-new/api/models/AwardAccount/AwardAccountIncomingRequest'
import AwardAccountOutgoingRequest from '#/src-new/api/models/AwardAccount/AwardAccountOutgoingRequest'
import BonusLead from '#/src-new/api/models/Lead/BonusLead'
import BookkeepingInvoice from '#/src-new/api/models/Bookkeeping/BookkeepingInvoice'
import BookkeepingInvoiceDocument from '#/src-new/api/models/Bookkeeping/BookkeepingInvoiceDocument'
import BookkeepingPaymentGateway from '#/src-new/api/models/Bookkeeping/BookkeepingPaymentGateway'
import BookkeepingTransaction from '#/src-new/api/models/Bookkeeping/BookkeepingTransaction'
import BookkeepingTransactionAdditionalInfo from '#/src-new/api/models/Bookkeeping/BookkeepingTransactionAdditionalInfo'
import BookkeepingTransactionAdditionalPayment from '#/src-new/api/models/Bookkeeping/BookkeepingTransactionAdditionalPayment'
import BookkeepingTransactionAssignment from '#/src-new/api/models/Bookkeeping/BookkeepingTransactionAssignment'
import CallContactAttempt from '#/src-new/api/models/Call/CallContactAttempt'
import Chat from '#/src-new/api/models/Chat/Chat'
import ChatAdditionalInfo from '#/src-new/api/models/Chat/ChatAdditionalInfo'
import ChatGateway from '#/src-new/api/models/Chat/ChatGateway'
import ChatGroup from '#/src-new/api/models/Chat/ChatGroup'
import ChatMessage from '#/src-new/api/models/Chat/ChatMessage'
import CheckInReminder from '#/src-new/api/models/CheckInReminder/CheckInReminder'
import City from '#/src-new/api/models/City/City'
import Client from '#/src-new/api/models/Client/Client'
import ClientAdditionalEnrichmentInfo from '#/src-new/api/models/Client/ClientAdditionalEnrichmentInfo'
import ClientAdditionalInfo from '#/src-new/api/models/Client/ClientAdditionalInfo'
import ClientEmailData from '#/src-new/api/models/Client/ClientEmailData'
import ClientPassport from '#/src-new/api/models/Client/ClientPassport'
import ClientPhoneData from '#/src-new/api/models/Client/ClientPhoneData'
import ClientPreview from '#/src-new/api/models/Client/ClientPreview'
import ClientSessionActivity from '#/src-new/api/models/Client/ClientSessionActivity'
import ClientStatus from '#/src-new/api/models/Client/ClientStatus'
import CompanyContact from '#/src-new/api/models/CompanyContact/CompanyContact'
import Consolidator from '#/src-new/api/models/Consolidator/Consolidator'
import ConsolidatorAdditionalParserSettings from '#/src-new/api/models/Consolidator/ConsolidatorAdditionalParserSettings'
import ConsolidatorArea from '#/src-new/api/models/Consolidator/ConsolidatorArea'
import ContactRule from '#/src-new/api/models/ContactRule/ContactRule'
import Counter from '#/src-new/api/models/Counter/Counter'
import Country from '#/src-new/api/models/Country/Country'
import Currency from '#/src-new/api/models/Currency/Currency'
import Department from '#/src-new/api/models/Department/Department'
import ElrFrtCheck from '#/src-new/api/models/ElrFrtCheck/ElrFrtCheck'
import Email from '#/src-new/api/models/Email/Email'
import EmailContactAttempt from '#/src-new/api/models/ContactAttempt/EmailContactAttempt'
import EmailContactAttemptConversation from '#/src-new/api/models/ContactAttempt/EmailContactAttemptConversation'
import EmailEmailContactAttemptConversationList from '#/src-new/api/models/ContactAttempt/EmailEmailContactAttemptConversationList'
import EmailTemplate from '#/src-new/api/models/Email/EmailTemplate'
import ExpectedAmount from '#/src-new/api/models/ExpectedAmount/ExpectedAmount'
import ExternalUserAgent from '#/src-new/api/models/ExternalUserAgent/ExternalUserAgent'
import ExternalUserAgentProxy from '#/src-new/api/models/ExternalUserAgent/ExternalUserAgentProxy'
import ExternalUserAgentQuickProfileLog from '#/src-new/api/models/Agent/ExternalUserAgentQuickProfileLog'
import File from '#/src-new/api/models/File/File'
import GamblingLot from '#/src-new/api/models/GamblingLot/GamblingLot'
import GamblingLotBet from '#/src-new/api/models/GamblingLot/GamblingLotBet'
import GamblingLotOption from '#/src-new/api/models/GamblingLot/GamblingLotOption'
import Iata from '#/src-new/api/models/Iata/Iata'
import Issue from '#/src-new/api/models/Issue/Issue'
import IssueAdditionalAgentInfo from '#/src-new/api/models/Issue/IssueAdditionalAgentInfo'
import IssueGroup from '#/src-new/api/models/Issue/IssueGroup'
import Lead from '#/src-new/api/models/Lead/Lead'
import LeadAdditionalAgentInfo from '#/src-new/api/models/Lead/LeadAdditionalAgentInfo'
import LeadAdditionalCallCounterInfo from '#/src-new/api/models/Lead/LeadAdditionalCallCounterInfo'
import LeadAdditionalExpertInformation from '#/src-new/api/models/Lead/LeadAdditionalExpertInformation'
import LeadAdditionalExpertQueueInformation from '#/src-new/api/models/Lead/LeadAdditionalExpertQueueInformation'
import LeadAdditionalFollowUp from '#/src-new/api/models/Lead/LeadAdditionalFollowUp'
import LeadAdditionalListInformation from '#/src-new/api/models/Lead/LeadAdditionalListInformation'
import LeadAdditionalMailCounterInfo from '#/src-new/api/models/Lead/LeadAdditionalMailCounterInfo'
import LeadAdditionalManagementProcessedRemark from '#/src-new/api/models/Lead/LeadAdditionalManagementProcessedRemark'
import LeadAdditionalManagementStatus from '#/src-new/api/models/Lead/LeadAdditionalManagementStatus'
import LeadAdditionalProfits from '#/src-new/api/models/Lead/LeadAdditionalProfits'
import LeadAdditionalUTM from '#/src-new/api/models/Lead/LeadAdditionalUTM'
import LeadDuplicate from '#/src-new/api/models/Lead/LeadDuplicate'
import LeadExpertProfits from '#/src-new/api/models/Lead/LeadExpertProfits'
import LeadOffer from '#/src-new/api/models/Lead/LeadOffer'
import LeadPreview from '#/src-new/api/models/Lead/LeadPreview'
import LeadStatus from '#/src-new/api/models/Lead/LeadStatus'
import MilePriceProgram from '#/src-new/api/models/MilePriceProgram/MilePriceProgram'
import NotifiableCSIssueListWithChats from '#/src-new/api/models/Issue/List/NotifiableCSIssueListWithChats'
import NotifiableIssueListWithChats from '#/src-new/api/models/Issue/List/NotifiableIssueListWithChats'
import NotifiableSaleListWithChats from '#/src-new/api/models/Sale/List/NotifiableSaleListWithChats'
import Office from '#/src-new/api/models/Office/Office'
import PaymentGateway from '#/src-new/api/models/PaymentGateway/PaymentGateway'
import PaymentGatewayBalanceAdditional from '#/src-new/api/models/PaymentGateway/PaymentGatewayBalanceAdditional'
import PerformanceFeedback from '#/src-new/api/models/PerformanceFeedback/PerformanceFeedback'
import PerformanceFeedbackEvent from '#/src-new/api/models/PerformanceFeedback/PerformanceFeedbackEvent'
import Phone from '#/src-new/api/models/Phone/Phone'
import PnrSchedule from '#/src-new/api/models/PnrSchedule/PnrSchedule'
import Poll from '#/src-new/api/models/Poll/Poll'
import Position from '#/src-new/api/models/Position/Position'
import PriceDropCheck from '#/src-new/api/models/PriceDrop/PriceDropCheck'
import PriceDropOffer from '#/src-new/api/models/PriceDrop/PriceDropOffer'
import PriceQuote from '#/src-new/api/models/PriceQuote/PriceQuote'
import PriceQuoteAdditionalSegments from '#/src-new/api/models/PriceQuote/PriceQuoteAdditionalSegments'
import PriceQuoteAdditionalSummary from '#/src-new/api/models/PriceQuote/PriceQuoteAdditionalSummary'
import PriceQuoteAdditionalTrackingInfo from '#/src-new/api/models/PriceQuote/PriceQuoteAdditionalTrackingInfo'
import Product from '#/src-new/api/models/Product/Product'
import ProductAdditionalFieldAmount from '#/src-new/api/models/Product/ProductAdditionalFieldAmount'
import ProductAdditionalInfo from '#/src-new/api/models/Product/ProductAdditionalInfo'
import ProductClientApprove from '#/src-new/api/models/Product/ProductClientApprove'
import Project from '#/src-new/api/models/Project/Project'
import ProjectCard from '#/src-new/api/models/Card/ProjectCard'
import ProjectCardAdditionalVcc from '#/src-new/api/models/Card/ProjectCardAdditionalVcc'
import ProjectCardTransaction from '#/src-new/api/models/Card/ProjectCardTransaction'
import Region from '#/src-new/api/models/Region/Region'
import ReleasePost from '#/src-new/api/models/ReleasePost/ReleasePost'
import ReleasePostItem from '#/src-new/api/models/ReleasePost/ReleasePostItem'
import ReleasePostItemAdditionalInfo from '#/src-new/api/models/ReleasePost/ReleasePostItemAdditionalInfo'
import ReleasePostItemFeedback from '#/src-new/api/models/ReleasePost/ReleasePostItemFeedback'
import ReleasePostItemFeedbackReply from '#/src-new/api/models/ReleasePost/ReleasePostItemFeedbackReply'
import ReleasesInfo from '#/src-new/api/models/ReleasesInfo/ReleasesInfo'
import Sale from '#/src-new/api/models/Sale/Sale'
import SaleAdditionalInfo from '#/src-new/api/models/Sale/SaleAdditionalInfo'
import SaleAdditionalProfits from '#/src-new/api/models/Sale/SaleAdditionalProfits'
import SaleCardAccessRequest from '#/src-new/api/models/Sale/SaleCardAccessRequest'
import SaleClientRequest from '#/src-new/api/models/Client/SaleClientRequest'
import SaleDraft from '#/src-new/api/models/Sale/SaleDraft'
import SaleExtraProfit from '#/src-new/api/models/Sale/SaleExtraProfit'
import SaleMember from '#/src-new/api/models/Sale/SaleMember'
import SalePreview from '#/src-new/api/models/Sale/SalePreview'
import SaleTransaction from '#/src-new/api/models/Sale/SaleTransaction'
import SaleVersion from '#/src-new/api/models/Sale/SaleVersion'
import SaleVersionCard from '#/src-new/api/models/Sale/SaleVersionCard'
import SaleVersionPassenger from '#/src-new/api/models/Sale/SaleVersionPassenger'
import SaleVersionPassengerSegmentInfo from '#/src-new/api/models/Sale/SaleVersionPassengerSegmentInfo'
import SaleVersionPnr from '#/src-new/api/models/Sale/SaleVersionPnr'
import SaleVersionPnrInfo from '#/src-new/api/models/Sale/SaleVersionPnrInfo'
import SaleVoucher from '#/src-new/api/models/Sale/SaleVoucher'
import Shift from '#/src-new/api/models/Shift/Shift'
import SignedDocument from '#/src-new/api/models/SignedDocument/SignedDocument'
import Skill from '#/src-new/api/models/Skill/Skill'
import SubscribedCounter from '#/src-new/api/models/Counter/SubscribedCounter'
import Task from '#/src-new/api/models/Task/Task'
import TaskGroup from '#/src-new/api/models/Task/TaskGroup'
import Team from '#/src-new/api/models/Team/Team'
import TerminalHotkey from '#/src-new/api/models/TerminalHotkey/TerminalHotkey'
import Ticket from '#/src-new/api/models/Ticket/Ticket'
import TicketExtraPreference from '#/src-new/api/models/TicketExtraPreference/TicketExtraPreference'
import Traveler from '#/src-new/api/models/Traveler/Traveler'
import UnsubscribedCounter from '#/src-new/api/models/Counter/UnsubscribedCounter'
import VccProjectCardSummary from '#/src-new/api/models/Card/VccProjectCardSummary'
import Voucher from '#/src-new/api/models/Voucher/Voucher'
import VoucherPnrAdditional from '#/src-new/api/models/Voucher/VoucherPnrAdditional'

export default {
    ActiveCSIssueListWithChats: new ActiveCSIssueListWithChats(),
    ActiveIssueListWithChats: new ActiveIssueListWithChats(),
    ActiveSaleListWithChats: new ActiveSaleListWithChats(),
    ActivityLog: new ActivityLog(),
    ActivityLogDetailed: new ActivityLogDetailed(),
    Agent: new Agent(),
    AgentAdditionalBonusInfo: new AgentAdditionalBonusInfo(),
    AgentAdditionalGDSInfo: new AgentAdditionalGDSInfo(),
    AgentAdditionalPersonalInfo: new AgentAdditionalPersonalInfo(),
    AgentAdditionalSalaryInfo: new AgentAdditionalSalaryInfo(),
    AgentAdditionalState: new AgentAdditionalState(),
    AgentAdditionalTransferClientsInfo: new AgentAdditionalTransferClientsInfo(),
    AgentAppSettings: new AgentAppSettings(),
    AgentClientTransfer: new AgentClientTransfer(),
    AgentLeadGiveRule: new AgentLeadGiveRule(),
    AgentProjectSettings: new AgentProjectSettings(),
    AgentReport: new AgentReport(),
    AgentReportAdditional: new AgentReportAdditional(),
    AgentReportInternalProfit: new AgentReportInternalProfit(),
    AgentReportInvoice: new AgentReportInvoice(),
    AgentReportSale: new AgentReportSale(),
    AgentReportTicketing: new AgentReportTicketing(),
    AgentShift: new AgentShift(),
    AgentSkill: new AgentSkill(),
    AgentTarget: new AgentTarget(),
    Airline: new Airline(),
    AirlineCase: new AirlineCase(),
    AirlineCompanyContact: new AirlineCompanyContact(),
    AirlineReport: new AirlineReport(),
    AirlineReportVersion: new AirlineReportVersion(),
    AssignLog: new AssignLog(),
    AudioTranscription: new AudioTranscription(),
    AwardAccount: new AwardAccount(),
    AwardAccountAdditionalCredentials: new AwardAccountAdditionalCredentials(),
    AwardAccountHolder: new AwardAccountHolder(),
    AwardAccountIncomingRequest: new AwardAccountIncomingRequest(),
    AwardAccountOutgoingRequest: new AwardAccountOutgoingRequest(),
    BonusLead: new BonusLead(),
    BookkeepingInvoice: new BookkeepingInvoice(),
    BookkeepingInvoiceDocument: new BookkeepingInvoiceDocument(),
    BookkeepingPaymentGateway: new BookkeepingPaymentGateway(),
    BookkeepingTransaction: new BookkeepingTransaction(),
    BookkeepingTransactionAdditionalInfo: new BookkeepingTransactionAdditionalInfo(),
    BookkeepingTransactionAdditionalPayment: new BookkeepingTransactionAdditionalPayment(),
    BookkeepingTransactionAssignment: new BookkeepingTransactionAssignment(),
    CallContactAttempt: new CallContactAttempt(),
    Chat: new Chat(),
    ChatAdditionalInfo: new ChatAdditionalInfo(),
    ChatGateway: new ChatGateway(),
    ChatGroup: new ChatGroup(),
    ChatMessage: new ChatMessage(),
    CheckInReminder: new CheckInReminder(),
    City: new City(),
    Client: new Client(),
    ClientAdditionalEnrichmentInfo: new ClientAdditionalEnrichmentInfo(),
    ClientAdditionalInfo: new ClientAdditionalInfo(),
    ClientEmailData: new ClientEmailData(),
    ClientPassport: new ClientPassport(),
    ClientPhoneData: new ClientPhoneData(),
    ClientPreview: new ClientPreview(),
    ClientSessionActivity: new ClientSessionActivity(),
    ClientStatus: new ClientStatus(),
    CompanyContact: new CompanyContact(),
    Consolidator: new Consolidator(),
    ConsolidatorAdditionalParserSettings: new ConsolidatorAdditionalParserSettings(),
    ConsolidatorArea: new ConsolidatorArea(),
    ContactRule: new ContactRule(),
    Counter: new Counter(),
    Country: new Country(),
    Currency: new Currency(),
    Department: new Department(),
    ElrFrtCheck: new ElrFrtCheck(),
    Email: new Email(),
    EmailContactAttempt: new EmailContactAttempt(),
    EmailContactAttemptConversation: new EmailContactAttemptConversation(),
    EmailEmailContactAttemptConversationList: new EmailEmailContactAttemptConversationList(),
    EmailTemplate: new EmailTemplate(),
    ExpectedAmount: new ExpectedAmount(),
    ExternalUserAgent: new ExternalUserAgent(),
    ExternalUserAgentProxy: new ExternalUserAgentProxy(),
    ExternalUserAgentQuickProfileLog: new ExternalUserAgentQuickProfileLog(),
    File: new File(),
    GamblingLot: new GamblingLot(),
    GamblingLotBet: new GamblingLotBet(),
    GamblingLotOption: new GamblingLotOption(),
    Iata: new Iata(),
    Issue: new Issue(),
    IssueAdditionalAgentInfo: new IssueAdditionalAgentInfo(),
    IssueGroup: new IssueGroup(),
    Lead: new Lead(),
    LeadAdditionalAgentInfo: new LeadAdditionalAgentInfo(),
    LeadAdditionalCallCounterInfo: new LeadAdditionalCallCounterInfo(),
    LeadAdditionalExpertInformation: new LeadAdditionalExpertInformation(),
    LeadAdditionalExpertQueueInformation: new LeadAdditionalExpertQueueInformation(),
    LeadAdditionalFollowUp: new LeadAdditionalFollowUp(),
    LeadAdditionalListInformation: new LeadAdditionalListInformation(),
    LeadAdditionalMailCounterInfo: new LeadAdditionalMailCounterInfo(),
    LeadAdditionalManagementProcessedRemark: new LeadAdditionalManagementProcessedRemark(),
    LeadAdditionalManagementStatus: new LeadAdditionalManagementStatus(),
    LeadAdditionalProfits: new LeadAdditionalProfits(),
    LeadAdditionalUTM: new LeadAdditionalUTM(),
    LeadDuplicate: new LeadDuplicate(),
    LeadExpertProfits: new LeadExpertProfits(),
    LeadOffer: new LeadOffer(),
    LeadPreview: new LeadPreview(),
    LeadStatus: new LeadStatus(),
    MilePriceProgram: new MilePriceProgram(),
    NotifiableCSIssueListWithChats: new NotifiableCSIssueListWithChats(),
    NotifiableIssueListWithChats: new NotifiableIssueListWithChats(),
    NotifiableSaleListWithChats: new NotifiableSaleListWithChats(),
    Office: new Office(),
    PaymentGateway: new PaymentGateway(),
    PaymentGatewayBalanceAdditional: new PaymentGatewayBalanceAdditional(),
    PerformanceFeedback: new PerformanceFeedback(),
    PerformanceFeedbackEvent: new PerformanceFeedbackEvent(),
    Phone: new Phone(),
    PnrSchedule: new PnrSchedule(),
    Poll: new Poll(),
    Position: new Position(),
    PriceDropCheck: new PriceDropCheck(),
    PriceDropOffer: new PriceDropOffer(),
    PriceQuote: new PriceQuote(),
    PriceQuoteAdditionalSegments: new PriceQuoteAdditionalSegments(),
    PriceQuoteAdditionalSummary: new PriceQuoteAdditionalSummary(),
    PriceQuoteAdditionalTrackingInfo: new PriceQuoteAdditionalTrackingInfo(),
    Product: new Product(),
    ProductAdditionalFieldAmount: new ProductAdditionalFieldAmount(),
    ProductAdditionalInfo: new ProductAdditionalInfo(),
    ProductClientApprove: new ProductClientApprove(),
    Project: new Project(),
    ProjectCard: new ProjectCard(),
    ProjectCardAdditionalVcc: new ProjectCardAdditionalVcc(),
    ProjectCardTransaction: new ProjectCardTransaction(),
    Region: new Region(),
    ReleasePost: new ReleasePost(),
    ReleasePostItem: new ReleasePostItem(),
    ReleasePostItemAdditionalInfo: new ReleasePostItemAdditionalInfo(),
    ReleasePostItemFeedback: new ReleasePostItemFeedback(),
    ReleasePostItemFeedbackReply: new ReleasePostItemFeedbackReply(),
    ReleasesInfo: new ReleasesInfo(),
    Sale: new Sale(),
    SaleAdditionalInfo: new SaleAdditionalInfo(),
    SaleAdditionalProfits: new SaleAdditionalProfits(),
    SaleCardAccessRequest: new SaleCardAccessRequest(),
    SaleClientRequest: new SaleClientRequest(),
    SaleDraft: new SaleDraft(),
    SaleExtraProfit: new SaleExtraProfit(),
    SaleMember: new SaleMember(),
    SalePreview: new SalePreview(),
    SaleTransaction: new SaleTransaction(),
    SaleVersion: new SaleVersion(),
    SaleVersionCard: new SaleVersionCard(),
    SaleVersionPassenger: new SaleVersionPassenger(),
    SaleVersionPassengerSegmentInfo: new SaleVersionPassengerSegmentInfo(),
    SaleVersionPnr: new SaleVersionPnr(),
    SaleVersionPnrInfo: new SaleVersionPnrInfo(),
    SaleVoucher: new SaleVoucher(),
    Shift: new Shift(),
    SignedDocument: new SignedDocument(),
    Skill: new Skill(),
    SubscribedCounter: new SubscribedCounter(),
    Task: new Task(),
    TaskGroup: new TaskGroup(),
    Team: new Team(),
    TerminalHotkey: new TerminalHotkey(),
    Ticket: new Ticket(),
    TicketExtraPreference: new TicketExtraPreference(),
    Traveler: new Traveler(),
    UnsubscribedCounter: new UnsubscribedCounter(),
    VccProjectCardSummary: new VccProjectCardSummary(),
    Voucher: new Voucher(),
    VoucherPnrAdditional: new VoucherPnrAdditional(),
}