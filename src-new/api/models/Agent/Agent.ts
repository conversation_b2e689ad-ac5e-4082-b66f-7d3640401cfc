import type { Definition } from '~types/lib/Model'
import { Gender } from '~types/enums/Gender'
import { SuspiciousAction } from '~/sections/Lead/composable/useSuspiciousActivityTracker'
import { ShiftType } from '~/api/models/Shift/Shift'
import { HiringSource } from '~/api/dictionaries/Static/Agent/HiringSourceDictionary'

declare global {
    export interface Models {
        Agent: Agent
    }
}

export enum DepartmentCategory {
    Business = 'business',
    Service = 'service'
}

export const ExpertStatistic = z.object({
    agent_pk: z.pk(),
    takenLeadsMonthCount: z.int(),
    takenLeadsDayCount: z.int(),
    soldLeadsCount: z.int(),
    soldSalesCount: z.int(),
    averageLeadsDay: z.int(),
    total_gp: z.number(),
    targetAchievedPs: z.number(),
    target: z.number(),
    lead_gp: z.number(),
    pq_gp: z.number(),
    takenLeadsFromQueueMonthCount: z.int(),
    takenLeadsManuallyMonthCount: z.int(),
    takenLeadsFromQueueDayCount: z.int(),
    takenLeadsManuallyDayCount: z.int(),
}).describe('ExpertStatistic')

const SaleStatisticData = z.object({
    bonus_leads_count: z.int(),
    new_leads_count: z.int(),
    leads_moved_to_nq: z.int(),
    leads_moved_to_bq: z.int(),
    leads_returns_or_referrals_count: z.int(),
    bonus_clients_count: z.int(),
    new_clients_count: z.int(),
    clients_moved_to_nq: z.int(),
    clients_moved_to_bq: z.int(),
    clients_returns_or_referrals_count: z.int(),
    non_tbc_clients: z.int(),
    gp_exchange_refunds: z.number(),
    gp_from_leads_returns_or_referrals: z.number(),
    gp_from_new_clients: z.number(),
    sales_number: z.int(),
    tp: z.number(),
    tips: z.number(),
    total_gp: z.number(),
})
export const SaleStatistic = z.object({
    agent_pk: z.pk(),
    target: z.number(),
    is_closed: SaleStatisticData,
    is_adjusted: SaleStatisticData,

}).describe('SaleStatistic')

export const LeadManagementStatistic = z.object({
    agent_pk: z.pk(),
    // month
    total_month_closing_count: z.int(),
    approved_month_closing_count: z.int(),
    declined_month_closing_count: z.int(),
    //today
    total_today_closing_count: z.int(),
    approved_today_closing_count: z.int(),
    declined_today_closing_count: z.int(),

    //month
    total_month_keep_client_count: z.int(),
    approved_month_keep_client_count: z.int(),
    declined_month_keep_client_count: z.int(),
    //today
    total_today_keep_client_count: z.int(),
    approved_today_keep_client_count: z.int(),
    declined_today_keep_client_count: z.int(),

    // month
    total_month_follow_up_count: z.int(),
    processed_well_month_follow_up_count: z.int(),
    need_attention_month_follow_up_count: z.int(),
    //today
    total_today_follow_up_count: z.int(),
    processed_well_today_follow_up_count: z.int(),
    need_attention_today_follow_up_count: z.int(),

    // month
    total_month_created_count: z.int(),
    created_month_jivo_count: z.int(),
    created_month_general_line_count: z.int(),
    //today
    total_today_created_count: z.int(),
    created_today_jivo_count: z.int(),
    created_today_general_line_count: z.int(),

    gp: z.number(),

}).describe('LeadManagementStatistic')

export const AgentUpdateInfoFormData = z.object({
    first_name: z.string(),
    last_name: z.string(),
    agentProjectInfo: z.array(z.object({
        project_pk: z.pk(),
        email: z.string(),
        phone: z.string(),
        voip_ext: z.string().nullable(),
    })),
    new_password: z.string().nullable(),
    department_pk: z.pk(),
    position_pk: z.pk(),
    team_pk: z.pk().nullable(),
    curator_pk: z.pk().nullable(),
    office_pk: z.pk().nullable(),
    replaced_by_pk: z.pk().nullable(),
    replaced_till_date: z.timestamp().nullable(),
    personal_first_name: z.string(),
    personal_last_name: z.string(),
    birthday_at: z.timestamp(),
    gender: z.nativeEnum(Gender),
    personal_phone: z.string(),
    personal_email: z.string(),
    address: z.string(),
    serial_number: z.string(),
    idnp: z.string(),
    sabre_id: z.string().nullable(),
    sabre_lniata: z.string().nullable(),
    sabre_initials: z.string().nullable(),
    req_hired_at: z.timestamp().nullable(),
    avatar_file_pk: z.pk().nullable(),
    hiring_source: z.nativeEnum(HiringSource).nullable(),
    hiring_source_remark: z.string().nullable(),
}).describe('AgentUpdateInfoFormData')

export default class Agent implements Definition.Model {
    public readonly name = 'Agent'
    public readonly pk = 'id'
    public readonly cacheTTLms = Timespan.hours(8).inMilliseconds
    public readonly defaultOrderBy = { id: 'asc' } as const

    public fields = z.object({
        id: z.int(),
        email: z.string(),
        sex: z.nativeEnum(Gender),
        birthday_at: z.timestamp(),
        phone: z.string(),
        voip_extension: z.string().nullable(),

        first_name: z.string(),
        last_name: z.string().nullable(),
        personal_fullname: z.string(),
        avatar: z.string().nullable(),

        is_enabled: z.boolean(),
        created_at: z.timestamp(),
        requested_hired_at: z.timestamp().nullable(),
        hired_at: z.timestamp().nullable(),
        requested_dismissed_at: z.timestamp().nullable(),
        dismissed_at: z.timestamp().nullable(),
        is_beginner: z.boolean(),
        is_bot: z.boolean(),

        replaced_till_date: z.timestamp().nullable(),

        hiring_source: z.nativeEnum(HiringSource).nullable(),
        hiring_source_remark: z.string().nullable(),

        // Relation fields
        team_pk: z.pk().nullable(),
        ex_team_pk: z.pk().nullable(),
        position_pk: z.pk(),
        department_pk: z.pk(),
        project_pk: z.pk(),
        department_category: z.array(z.nativeEnum(DepartmentCategory)),
        curator_pk: z.pk().nullable(),
        replaced_by_pk: z.pk().nullable(),
        ringcentral_id: z.int().nullable(),
        shift_type: z.softEnum(ShiftType).nullable(),
        office_pk: z.pk().nullable(),
    })

    public searchFields = z.object({
        keywords: z.searchKeywords(),

        id: z.int(),
        is_online: z.boolean(),
        email: z.string(),
        sex: z.nativeEnum(Gender),
        birthday_at: z.timestamp(),
        fullname: z.string(),
        personal_fullname: z.string(),
        phone: z.string(),
        department: z.string(),
        position: z.string(),
        team: z.string(),
        office_pk: z.pk(),
        status: z.boolean(),
        voip_extension: z.boolean(),
        created_at: z.timestamp(),
        last_login_at: z.timestamp(),

        sabre_id: z.string(),
        sabre_initials: z.string(),

        requested_hired_at: z.timestamp(),
        requested_dismissed_at: z.timestamp(),

        project_pk: z.pk(),

        can_ba_added_to_agent_report: z.pk(),
        incoming_birthday_at: z.timestamp(),

        department_category: z.nativeEnum(DepartmentCategory),

        hiring_source: z.nativeEnum(HiringSource),

        // Release tool search fields
        agent_pk: z.pk(),
        department_pk: z.pk(),
        position_pk: z.pk(),
        manage_all: z.boolean(),
        is_it_manager: z.boolean(),
    })

    public relations = defineRelations({
        team: belongsTo('Team', 'team_pk'),
        exTeam: belongsTo('Team', 'ex_team_pk'),
        position: belongsTo('Position', 'position_pk'),
        department: belongsTo('Department', 'department_pk'),
        office: belongsTo('Office', 'office_pk'),

        personalInfo: belongsTo('AgentAdditionalPersonalInfo', 'id'),
        state: belongsTo('AgentAdditionalState', 'id'),
        gds: belongsTo('AgentAdditionalGDSInfo', 'id'),
        transferClientsInfo: belongsTo('AgentAdditionalTransferClientsInfo', 'id'),
        bonus: belongsTo('AgentAdditionalBonusInfo', 'id'),
        salary: belongsTo('AgentAdditionalSalaryInfo', 'id'),

        targets: hasMany('AgentTarget', 'id'),
    })

    public actions = defineActions({
        changeTeam: {
            params: z.object({
                pk: z.pk(),
                team_pk: z.pk().nullable(),
            }),
            response: z.model('Agent').nullable(),
        },
        disable: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        requestedDisable: {
            params: z.object({
                pk: z.pk(),
                dismissal_date: z.timestamp(),
                outstanding_salary: z.number(),
                reason: z.string(),
            }),
            response: z.null(),
        },
        confirmHire: {
            params: z.object({
                pk: z.pk(),
                date: z.timestamp(),
            }),
            response: z.null(),
        },
        confirmDismissal: {
            params: z.object({
                pk: z.pk(),
                date: z.timestamp(),
            }),
            response: z.null(),
        },
        enable: {
            params: z.object({
                pk: z.pk(),
            }),
            response: z.null(),
        },
        suspiciousActivityMonitoring: {
            params: z.object({
                actionType: z.nativeEnum(SuspiciousAction),
                actionCounter: z.int(),
            }),
            response: z.null(),
        },
        getExpertStatistics: {
            params: z.object({
                period: z.object({
                    month: z.int(),
                    year: z.int(),
                }),
                project_pk: z.pk(),
                forced_refresh: z.boolean().nullable(),
            }),
            response: z.array(ExpertStatistic),
        },
        getExpertStatisticDetails: {
            params: z.object({
                period: z.object({
                    month: z.int(),
                    year: z.int(),
                }),
                agent_pk: z.pk(),
                field: z.string(),
            }),
            response: z.object({
                lead_pks: z.array(z.pk()),
            }),
        },
        getSaleStatistics: {
            params: z.object({
                period: z.object({
                    month: z.int(),
                    year: z.int(),
                }),
                project_pk: z.pk(),
                forced_refresh: z.boolean().nullable(),
            }),
            response: z.array(SaleStatistic),
        },
        getSaleStatisticDetails: {
            params: z.object({
                period: z.object({
                    month: z.int(),
                    year: z.int(),
                }),
                agent_pk: z.pk(),
                field: z.string(),
            }),
            response: z.object({
                lead_pks: z.array(z.pk()),
            }),
        },
        getSalesFromStatisticDetails: {
            params: z.object({
                period: z.object({
                    month: z.int(),
                    year: z.int(),
                }),
                agent_pk: z.pk(),
                field: z.string(),
                saleStage: z.string(),
            }),
            response: z.object({
                sale_pks: z.array(z.pk()),
            }),
        },
        getLeadManagementStatistics: {
            params: z.object({
                period: z.object({
                    month: z.int(),
                    year: z.int(),
                }),
                project_pk: z.pk(),
                forced_refresh: z.boolean().nullable(),
            }),
            response: z.array(LeadManagementStatistic),
        },
        getLeadManagementStatisticsDetails: {
            params: z.object({
                period: z.object({
                    month: z.int(),
                    year: z.int(),
                }),
                agent_pk: z.pk(),
                field: z.string(),
            }),
            response: z.object({
                lead_pks: z.array(z.pk()),
            }),
        },
        unlinkFromRingcentral: {
            params: z.object({
                agent_pk: z.pk(),
            }),
            response: z.null(),
        },
        setAvatar: {
          params: z.object({
              agent_pk: z.pk(),
              avatar_file_pk: z.pk().nullable(),
          }),
          response: z.null(),
        },
        updateAgentInfo: {
            params: z.object({
                agent_pk: z.pk(),
                data: AgentUpdateInfoFormData,
            }),
            response: z.null(),
        },
        createAgent: {
            params: z.object({
                data: AgentUpdateInfoFormData,
            }),
            response: z.null(),
        },
        setIsBeginner: {
            params: z.object({
                pk: z.pk(),
                is_beginner: z.boolean(),
            }),
            response: z.null(),
        },
        updateBonus: {
            params: z.object({
                pk: z.pk(),
                rate: z.number(),
            }),
            response: z.null(),
        },
        updateShiftType: {
            params: z.object({
                agent_pk: z.pk(),
                shift_type: z.softEnum(ShiftType).nullable(),
            }),
            response: z.null(),
        },
        loginInNotificationBar: {
            params: z.object({
                identity_key: z.string(),
            }),
            response: z.object({
                state: z.boolean(),
            }),
        },
        parseAgentInfoFromDocument: {
            params: z.object({
                file_pks: z.array(z.pk()),
            }),
            response: z.object({
                first_name: z.string().nullable(),
                last_name: z.string().nullable(),
                birth_date: z.timestamp().nullable(),
                gender: z.nativeEnum(Gender).nullable(),
                address: z.string().nullable(),
                serial_no: z.string().nullable(),
                idnp: z.string().nullable(),
            }),
        },
        linkToRingcentral: {
            params: z.object({
                ringcentral_id: z.int(),
            }),
            response: z.null(),
        },
        agentAvatarCompress: {
            params: z.object({
                agent_pk: z.pk(),
                file_pk: z.pk().nullable(),
            }),
            response: z.object({
                avatar: z.string().nullable(),
            }),
        },
        getAbilities: {
            params: z.object({
               agent_pk: z.pk(),
            }),
            response: z.array(z.object({
                pk: z.pk(),
                name: z.string(),
                description: z.string(),
                access_description: z.array(z.string()),
                is_enabled: z.boolean(),
            })),
        },
        setAbility: {
            params: z.object({
                agent_pk: z.pk(),
                ability_pk: z.pk(),
                is_enabled: z.boolean(),
            }),
            response: z.object({
                is_enabled: z.boolean(),
            }),
        },
    })
}
