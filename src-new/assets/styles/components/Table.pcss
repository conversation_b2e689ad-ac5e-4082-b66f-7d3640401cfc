@layer components {
    table {
        --tr-hightlight-zebra: theme('colors.secondary.50');
        --tr-hightlight-hover: theme('colors.primary.50');

        --tr-hightlight-primary: theme('colors.primary.100');
        --tr-hightlight-secondary: theme('colors.secondary.100');
        --tr-hightlight-success: theme('colors.success.50');
        --tr-hightlight-info: theme('colors.info.50');
        --tr-hightlight-warning: theme('colors.warning.50');
        --tr-hightlight-danger: theme('colors.danger.50');
        --tr-hightlight-pending: theme('colors.pending.50');
        --tr-hightlight-click: theme('colors.info.100');

        :where(.dark) & {
            --tr-hightlight-zebra: theme('colors.dark.3');
            --tr-hightlight-hover: theme('colors.dark.6');
        }
    }

    /**
     * Table Rows Highlightings
     */
    .table-tr--highlighted {
        --td-color: var(--tr-hightlight-zebra);
        --td-border-color: theme('colors.secondary.200');

        &-primary {
            --td-color: var(--tr-hightlight-primary);
            --td-border-color: theme('colors.primary.200');
        }

        &-secondary {
            --td-color: var(--tr-hightlight-secondary);
            --td-border-color: theme('colors.secondary.200');
        }

        &-success {
            --td-color: var(--tr-hightlight-success);
            --td-border-color: theme('colors.success.100');
        }

        &-info {
            --td-color: var(--tr-hightlight-info);
            --td-border-color: theme('colors.info.100');
        }

        &-warning {
            --td-color: var(--tr-hightlight-warning);
            --td-border-color: theme('colors.warning.100');
        }

        &-danger {
            --td-color: var(--tr-hightlight-danger);
            --td-border-color: theme('colors.danger.100');
        }

        &-pending {
            --td-color: var(--tr-hightlight-pending);
            --td-border-color: theme('colors.pending.100');
        }

        &-click {
            --td-color: var(--tr-hightlight-click);
            --td-border-color: theme('colors.info.200');
        }

        :where(.dark) & {
            &-primary {
                --td-color: theme('colors.success.950');
            }
            /* TODO: remove this one and use primary after it will be refactor to use primary color instead of success */
            &-real-primary {
                --td-color: theme('colors.primary.900');
            }
            &-secondary {
                --td-color: theme('colors.secondary.950');
            }
            &-success {
                --td-color: theme('colors.success.950');
            }
            &-info {
                --td-color: theme('colors.info.950');
            }
            &-warning {
                --td-color: theme('colors.warning.950');
            }
            &-danger {
                --td-color: theme('colors.danger.950');
            }
            &-pending {
                --td-color: theme('colors.pending.950');
            }
            &-click {
                --td-color: theme('colors.info.800');
                --td-border-color: theme('colors.info.900');
            }
        }
    }

    .table-tr--truncate {
        > td {
            @apply truncate;
        }
    }

    tr[class*="table-tr--highlighted"], tr[class*="table-tr--bordered"] {
        & + & {
            border-top: 1px solid var(--td-border-color);
        }
    }

    .table {
        &__td {
            &--link:not(#undefined) {
               padding: 0;

                a, .link {
                    display: flex;
                    width: 100%;
                    height: 100%;
                    align-items: center;
                    padding: var(--td-padding, 0.5rem);
                }
            }
        }
    }

    .list-table-v2 {
        width: 100%;
        text-align: left;

        @apply text-xs font-medium;

        thead {
            --td-color: theme('colors.secondary.50');

            :where(.dark) & {
                --td-color: theme('colors.dark.3');
            }

            @apply text-secondary-400 leading-normal;
            @apply dark:text-secondary-300;

            th {
                vertical-align: top;
                border-top-width: 1px;
                border-bottom-width: 1px;
            }
        }

        --td-padding: 0.5rem 0.675rem;
        --td-color: #fff;
        --td-border-color: theme('colors.secondary.100');

        &.\--compact {
            --td-padding: 0.25rem 0.375rem;
        }

        &.\--extra-compact {
            --td-padding: 0.25rem 0.15rem;
        }

        &.\--multiline-header {
            thead {
                th {
                    white-space: normal;
                    vertical-align: middle;
                }
            }
        }

        &.\--middle-header {
            thead {
                th {
                    vertical-align: middle;
                }
            }
        }

        &.\--rounded {
            &:first-child {
                thead {
                    tr:first-of-type {
                        td, th {
                            &:first-of-type {
                                border-top-left-radius: var(--card-border-radius);
                            }

                            &:last-of-type {
                                border-top-right-radius: var(--card-border-radius);
                            }
                        }
                    }
                }
            }

            &:last-child {
                tbody {
                    tr:last-of-type {
                        td, th {
                            &:first-of-type {
                                border-bottom-left-radius: var(--card-border-radius);
                            }

                            &:last-of-type {
                                border-bottom-right-radius: var(--card-border-radius);
                            }
                        }
                    }
                }
            }

            border-spacing: 0;
            border-radius: var(--card-border-radius);
            overflow: hidden;
        }

        &.\--bordered {
            @apply border;

            thead th {
                border-top-width: 0;
            }
        }

        &.\--fixed-header {
            thead tr th {
                position: sticky;
                top: 0;
                z-index: 2;
            }

            th {
                border-top-width: 0 !important;
            }
        }

        &.\--rounded&.\--bordered {
            border-collapse: separate;
        }

        &.\--rounded {
            &:first-child {
                thead {
                    tr:first-of-type {
                        td, th {
                            &:first-of-type {
                                border-top-left-radius: var(--card-border-radius);
                            }

                            &:last-of-type {
                                border-top-right-radius: var(--card-border-radius);
                            }
                        }
                    }
                }
            }

            &:last-child {
                tbody {
                    tr:last-of-type {
                        td, th {
                            &:first-of-type {
                                border-bottom-left-radius: var(--card-border-radius);
                            }

                            &:last-of-type {
                                border-bottom-right-radius: var(--card-border-radius);
                            }
                        }
                    }
                }
            }

            border-spacing: 0;
            border-radius: var(--card-border-radius);
            overflow: hidden;
        }

        &.\--bordered {
            @apply border;
        }

        &.\--fixed-header {
            thead tr th {
                position: sticky;
                top: 0;
                z-index: 2;
            }

            th {
                border-top-width: 0 !important;
            }
        }

        &.\--rounded&.\--bordered {
            border-collapse: separate;
        }

        :where(.dark) & {
            --td-color: theme('colors.dark.5');
            --td-border-color: theme('colors.secondary.600');
        }

        :where(tr) {
            border-color: var(--td-border-color);
        }

        :where(th, td) {
            padding: var(--td-padding);
            border-style: solid;
            border-color: var(--td-border-color);
            white-space: nowrap;
            background: var(--td-color);
            height: 1px;

            @apply font-medium;

            &:last-child {
                border-right-width: 0;
            }

            &.\--paddingless {
                padding: 0;
            }


            /* Full border style: */
            /*border-right-width: 1px;*/

            /* Partial border style: */
            position: relative;
            &:not(:last-child):after {
                content: '';
                display: block;
                position: absolute;
                top: 0.5rem;
                bottom: 0.5rem;
                right: 0;
                width: 1px;
                background: var(--td-border-color);
            }
        }

        tbody {
            tr {
                &:hover {
                    --td-color: var(--tr-hightlight-hover);
                    --td-border-color: theme('colors.primary.100');

                    :where(.dark) & {
                        --td-border-color: theme('colors.secondary.600');
                    }
                }
            }
        }

        &--zebra tr:nth-child(even):not(
            .table-tr--highlighted-primary,
            .table-tr--highlighted-secondary,
            .table-tr--highlighted-success,
            .table-tr--highlighted-info,
            .table-tr--highlighted-warning,
            .table-tr--highlighted-danger,
            .table-tr--highlighted-pending,
            .table-tr--highlighted-click
        ) {
            --td-color: var(--tr-hightlight-zebra);
        }

        &--zebra-dark-old {
            :where(.dark) & {
                :not(#undefined) {
                    td, th, tr {
                        border-color: var(--td-border-color);
                    }
                }

                --td-color: theme('colors.dark.3');
                --td-border-color: theme('colors.dark.5');
                --tr-hightlight-zebra: theme('colors.dark.1');
                --tr-hightlight-hover: theme('colors.dark.2');
            }
        }

        /**
         * Table Components
         * ========================================
         */
        &__group {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0.25rem;
            user-select: none;
            padding: var(--td-padding);

            &:not(:last-child) {
                padding-bottom: 0;
            }

            &__label {
                &.\--sort {
                    cursor: pointer;

                    &.\--active {
                        @apply text-primary-400 hover:text-primary-500 dark:hover:text-primary-300;
                    }

                    &:hover {
                        @apply text-secondary-500;
                        @apply dark:text-secondary-200;
                    }
                }
            }

            &__arrow {
                margin-left: auto;

                svg {
                    width: 0.75rem;
                    height: 0.75rem;
                    stroke-width: 2px;
                }

                &.\--active {
                    @apply text-primary-400 hover:text-primary-500 dark:hover:text-primary-300;
                }

                &.\--asc {
                    @apply rotate-180;
                }
            }
        }

        &__sort {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 0.5rem;
            user-select: none;
            cursor: pointer;
            padding: var(--td-padding);

            &:hover {
                @apply text-secondary-500;
                @apply dark:text-secondary-200;
            }

            &.\--active {
                @apply text-primary-400 hover:text-primary-500 dark:hover:text-primary-300;
            }

            &.\--asc {
                .list-table-v2__sort__arrow {
                    @apply rotate-180;
                }
            }

            &:not(:last-child) {
                padding-bottom: 0;
            }

            &__arrow {
                width: 0.75rem;
                height: 0.75rem;
                stroke-width: 2px;
            }
        }

        &__filter {
            :where(.\--paddingless) & {
                padding: var(--td-padding);
            }

            &:not(:first-child) {
                @apply pt-1;
            }

            input[placeholder] {
                &:placeholder-shown:not(:focus).\--search {
                    @apply pl-7;
                    background-image: url("data:image/svg+xml,%3Csvg data-v-4c034fe6='' xmlns='http://www.w3.org/2000/svg' width='24px' height='24px' viewBox='0 0 24 24' fill='none' stroke='%23607f96' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='icon-svg feather feather-search'%3E%3Ccircle data-v-4c034fe6='' cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline data-v-4c034fe6='' x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
                    background-repeat: no-repeat;
                    background-size: 14px;
                    background-position: 7px center;
                }

                &:placeholder-shown:not(:focus).\--calendar {
                    @apply pl-7;
                    background-image: url("data:image/svg+xml,%3Csvg data-v-4c034fe6='' xmlns='http://www.w3.org/2000/svg' width='24px' height='24px' viewBox='0 0 24 24' fill='none' stroke='%23607f96' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='icon-svg feather feather-calendar'%3E%3Crect data-v-4c034fe6='' x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline data-v-4c034fe6='' x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline data-v-4c034fe6='' x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline data-v-4c034fe6='' x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
                    background-repeat: no-repeat;
                    background-size: 14px;
                    background-position: 7px center;
                }

                &:focus {
                    &::placeholder {
                        color: transparent;
                    }
                }
            }
        }

        &__not-found {
            @apply text-center text-secondary-500 flex justify-center items-center;
        }
    }
}
