import { ResourceCacheStrategy, type ResourceContextOptions, useResourceFetch } from '~/composables/useResourceFetch'
import type {
ListResource,
Model,
ModelIdentification,
ModelRef,
ResourceListRef,
} from '~types/lib/Model'
import type { ComputedRef, MaybeRefOrGetter } from 'vue'
import type ArrayConditionAndOr from '~/lib/Helper/ArrayCondition/Conditions/ArrayConditionAndOr'
import ApiError from '~/lib/Error/Api/ApiError'
import type Pagination from '~/lib/Model/Pagination'
import ArrayConditionHelper from '~/lib/Helper/ArrayCondition/ArrayConditionHelper'
import type { ApiListResponse } from '~types/lib/Api'
import { useApiRoute } from '~/composables/_core/useApiRoute'
import type ModelController from '~/lib/Model/ModelController'
import { makeDestructurableClass, pluckObjectKeys } from '~/lib/Helper/ObjectHelper'
import { ensureArray, sortWithResolvers, unique } from '~/lib/Helper/ArrayHelper'
import type { SortDirection } from '~/lib/Search/SortController'
import SortController from '~/lib/Search/SortController'
import { FrontendPagination } from '~/lib/Model/FrontendPagination'
import { AnyWorkspace } from '~/service/WorkspaceService'
import { useProgress } from '@marcoschulte/vue3-progress'
import { BackendPagination } from '~/lib/Model/BackendPagination'
import { useCacheWithExpirationStorage } from '~/composables/storage/useCacheWithExpirationStorage'
import { isArchivable } from '~/composables/isArchived'
import type { Awaitable } from '@vueuse/shared'

export type ResourceListOptions<TModelName extends ModelName> =
    SimpleRequestOptions<Model.Relation.FieldRecursive<TModelName>, TModelName>
    | ResourceRequestOptions<Model.Relation.FieldRecursive<TModelName>>

export default class ResourceList<
    TModelName extends ModelName = any,
    TRequestOptions extends ResourceListOptions<TModelName> = any,
> {
    private state = reactive({
        records: [],
        listResource: undefined,
        loadedRelations: new Set(),
    }) as {
        records: any[],
        listResource: any,
        loadedRelations: Set<string>,
    }

    private preloadedRecords: { [key: string]: SimpleRequestResponse } = {}
    private requestListKey: string | undefined

    private declare readonly modelController: ModelController<TModelName>
    private declare readonly options: TRequestOptions

    private declare readonly modelName: TModelName
    private declare readonly resourceContextOptions: ResourceContextOptions
    private declare readonly refController: ReturnType<typeof useRef>

    private preloadOptions: PreloadNormalizedOptions
    private declare preloadInProgress: boolean
    private debouncePreloadList = useDebounceFn((options) => {
        this.preloadList(options)
    }, 5000)

    private recursiveRequestsCounter = 0

    // Relations

    private declare readonly relations: string[]
    private customRelationsStorage: Record<string, Record<PrimaryKey, unknown>> = {}
    private optionalRelationsStorage: Record<string, boolean> = {}

    //

    private declare readonly dependancies: MaybeRefOrGetter<Promise<any>>[]

    //

    public error = ref<ApiError>()
    public loading = ref<boolean>(false)
    public loaded = ref<boolean>(false)

    // For lazy loading
    public loadingMore = computed(() => this.loaded.value && this.loading.value)

    // Cache
    private searchCacheStorage = useCacheWithExpirationStorage('search')

    // @ts-ignore
    public get records(): ModelRef<
        TModelName,
        ExtractRelationsFromOptions<TRequestOptions>,
        ExtractCustomRelationsFromOptions<TRequestOptions>,
        ExtractOptionalRelationsFromOptions<TRequestOptions>
    >[] {
        if (this.isResourceList) {
            const pagination = this.pagination.value as FrontendPagination | undefined

            if (pagination) {
                return pagination.sliceRecords(this.state.records)
            }
        }

        return this.state.records
    }

    // noinspection JSUnusedGlobalSymbols
    public get listResource() {
        return this.state.listResource as ResourceListRef | undefined
    }

    //

    public constructor(
        modelController: ModelController<TModelName>,
        options?: TRequestOptions,
    ) {
        this.modelController = modelController

        this.modelName = modelController.modelName
        this.resourceContextOptions = modelController.resourceContextOptions

        //

        this.options = options ?? {} as TRequestOptions
        this.relations = normalizeWith(this.options?.with, this.options?.withOptional)
        this.preloadOptions = normalizePreloadOptions(this.options.pageFromPk, this.options.preload)
        this.dependancies = this.options?.depends ? ensureArray(this.options.depends) : []

        //

        this.refController = useRef(
            this.resourceContextOptions,
            this.relations,
            this.optionalRelationsStorage,
            this.options.withCustom,
            this.customRelationsStorage,
        )

        //

        this.setPersistedMemoryLinks()

        //

        if (this.shouldPaginate) {
            this.watchPaginationChanges()
        }

        if (this.shouldSort) {
            this.watchSortChanges()
        }

        //

        if (isDevelopment && this.isResourceList) {
            if (this.options.where) {
                throw new Error('You can\'t use "where" option with "resource" option')
            }

            const resource = (this.options as OptionsWithDefinedResource).resource

            if (!isRef(resource) && !useResourceDefinition(resource.name)) {
                throw new Error(`Resource "${resource.name}" is not defined in ~/api/resources/index.ts file`)
            }
        }
    }

    private get isResourceList() {
        return 'resource' in this.options
    }

    // Pagination
    // ==============================

    public pagination = ref<Pagination>()

    private get shouldPaginate() {
        return Boolean(this.options?.page || this.options?.pageSize)
    }

    public get isWithPossiblePageOffset() {
        return !isEmpty(this.options?.pageFromPk)
    }

    private get shouldPreloadPage() {
        return !this.preloadInProgress && this.preloadedRecords && Object.keys(this.preloadedRecords).length === 0 && this.pagination.value?.page.current && !this.isResourceList
    }

    private watchPaginationChanges() {
        if (this.isResourceList) {
            return
        }

        // Will be triggered on first fetch
        watchOnce(this.pagination, (pagination) => {
            // Watch pagination state change

            if (!pagination) {
                return
            }

            const fetch = (...args: Parameters<typeof this.fetch>) => {
                const promise = this.fetch(...args)

                // noinspection JSIgnoredPromiseFromCall
                return useProgress().attach(promise)
            }

            pagination.eventBus.on('pageChange', (page) => {
                return fetch({ page })
            })

            pagination.eventBus.on('loadNextPage', (page) => {
                return fetch({ page, insertStrategy: 'append' })
            })

            pagination.eventBus.on('loadPreviousPage', (page) => {
                return fetch({ page, insertStrategy: 'prepend' })
            })

            pagination.eventBus.on('pageSizeChange', (pageSize) => {
                return fetch({
                    resetPage: true,
                    pageSize,
                })
            })
        })
    }

    private get shouldSort() {
        return 'sort' in this.options && Boolean(this.options?.sort)
    }

    private getSortOptions(options: SimpleRequestOptions) {
        const state = options.sort instanceof SortController ? options.sort.state : toValue(options.sort)

        let defaultState = this.modelController.modelDefinition.defaultOrderBy

        if (defaultState === undefined && isDevelopment) {
            useLogger('resource-list').warn(`Model "${this.modelName}" doesn't have defaultOrderBy property`)
        }

        if (defaultState === undefined && state) {
            const pkFields = ensureArray(this.modelController.modelDefinition.pk)

            const newDefaultState: Record<string, SortDirection> = {}

            for (const pkField of pkFields) {
                newDefaultState[pkField] = 'asc'
            }

            defaultState = newDefaultState
        }

        const normalizedState: Record<string, SortDirection> = {}

        for (const field of [...Object.keys(state ?? {}), ...Object.keys(defaultState ?? {})]) {
            const direction = state?.[field] ?? (defaultState || undefined)?.[field]

            if (!direction) {
                continue
            }

            normalizedState[field] = direction
        }

        return normalizedState
    }

    private watchSortChanges() {
        watch(() => this.getSortOptions(this.options as SimpleRequestOptions), () => {
            // noinspection JSIgnoredPromiseFromCall
            useProgress().attach(this.fetch())
        }, { deep: true })
    }

    // To keep links to objects in memory to prevent garbage collection
    private memoryLinks: any[] = []
    private localMemoryLinks: any[] = []

    private clearMemoryLinks() {
        this.memoryLinks.splice(0, this.memoryLinks.length)

        this.setPersistedMemoryLinks()
    }

    private clearLocalMemoryLinks() {
        this.localMemoryLinks.splice(0, this.localMemoryLinks.length)
    }

    private setPersistedMemoryLinks() {
        this.memoryLinks.push(this.refController.memoryLinks)
    }

    // =======================================

    // noinspection JSUnusedGlobalSymbols
    public destructable() {
        return makeDestructurableClass(this)
    }

    // =======================================

    public async fetch(
        options: SimpleRequestOptions<Model.Relation.FieldRecursive<TModelName>, TModelName> & ContinuousResourceRequestOptions = {},
        throwError = false,
        // @ts-ignore
    ): Promise<ModelRef<TModelName, ExtractRelationsFromOptions<TRequestOptions>>[]> {
        this.loading.value = true

        if (options.resetPage) {
            options = {
                page: this.options.page,
                pageFromPk: this.options.pageFromPk,
                ...options,
            }
        }

        const useOptions = {
            insertStrategy: 'replace',
            withArchiveData: true,
            ...this.options,
            ...options,
        }

        // noinspection UnnecessaryLocalVariableJS
        const forcedOptions = options

        try {
            await this.waitForDependenciesReady()

            if (useOptions.insertStrategy === 'replace') {
                this.refController.clearCache()
                this.clearLocalMemoryLinks()
            }

            const data = this.isResourceList ?
                await this.fetchResourceList() :
                await this.fetchSimpleList(useOptions, forcedOptions)

            const resourceList = useOptions.keyModifier ?
                data.resourceList.map(useOptions.keyModifier) :
                data.resourceList

            if (data.pagination) {
                if (this.pagination.value) {
                    this.pagination.value.update(data.pagination, useOptions.insertStrategy)
                } else {
                    this.pagination.value = data.pagination
                }
            }

            // preload list

            if (this.shouldPreloadPage) {
                this.debouncePreloadList(useOptions)
            }

            // deduplicate records block

            const deduplicatedResourceList = useOptions.insertStrategy === 'replace' ? resourceList : []

            if (useOptions.insertStrategy !== 'replace') {
                for (let i = 0; i < resourceList.length; i++) {
                    const isDuplicate = this.records.find((record) => usePk(record) === resourceList[i])

                    if (!isDuplicate) {
                        deduplicatedResourceList.push(resourceList[i])
                    }
                }
            }

            //

            let isArchiveRequest = false

            if (useOptions.fromArchive && await resolveAwaitable(useOptions.fromArchive)) {
                isArchiveRequest = true
            } else if (await resolveAwaitable(useOptions.withArchiveData)) {
                isArchiveRequest = isArchivable(this.modelName)
            }

            const shouldCache = !useOptions.skipResourceCache?.includes(this.modelName)
            const cacheStrategy = shouldCache ? ResourceCacheStrategy.Cache : ResourceCacheStrategy.IgnoreAll

            const fetchedResources = await useResourceFetch(this.modelName, this.resourceContextOptions).fetchResources(deduplicatedResourceList, cacheStrategy, isArchiveRequest)

            const result = deduplicatedResourceList.map((pk: PrimaryKey) => fetchedResources[pk])

            this.localMemoryLinks.push(result)

            // Relations

            const fetches = []

            if (this.relations.length) {
                fetches.push(
                    this.loadStaticRelations(result, this.relations, this.options.withOptional),
                )
            }

            if (useOptions.withCustom) {
                fetches.push(
                    this.loadCustomRelations(result, useOptions.withCustom),
                )
            }

            if (fetches.length) {
                await Promise.all(fetches)
            }

            // Set records
            if (this.isResourceList) {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                const resource = this.options.resource!

                let list: ComputedRef<PrimaryKey[]>

                if (isRef(resource)) {
                    list = resource
                } else {
                    const resourceName = resource.name
                    const resourcePk = resource.pk

                    // @todo Refactor this
                    // @ts-ignore
                    if (this.options.aggregate && this.resourceContextOptions.http.workspace === AnyWorkspace) {
                        const workspaces = useService('workspace').availableWorkspaces.map((workspace) => workspace.pk)

                        const refControllers = workspaces.map((workspace) => {
                            return useRef({
                                http: { workspace },
                            }, this.relations)
                        })

                        list = computed(() => {
                            const result = refControllers.flatMap((refController) => {
                                return refController.resourceRef(resourceName, resourcePk).list
                            })

                            return this.options.keyModifier ? result.map(this.options.keyModifier) : result
                        })
                    } else {
                        const resourceProxy = this.refController.resourceRef(resourceName, resourcePk)

                        list = computed(() => {
                            return useOptions.keyModifier ? resourceProxy.list.map(useOptions.keyModifier) : resourceProxy.list
                        })
                    }
                }

                this.state.listResource = this.refController.linkResourceListToArray(
                    this.modelName,
                    list,
                    this.state.records,
                )

                // @todo Refactor this block
                if ('sort' in useOptions) {
                    const sort = toValue(useOptions.sort)

                    if (sort instanceof SortController) {
                        sort.sort(this.state.records, undefined, undefined, true)
                    } else if (sort) {
                        sortWithResolvers(this.state.records, sort, Object.fromEntries(Object.keys(sort).map(field => {
                            if (field === 'pk' || field.endsWith('_pk')) {
                                return [field, Number]
                            }

                            return
                        }).filter(Boolean)))
                    }
                }
            } else {
                if (useOptions.insertStrategy === 'replace') {
                    this.state.records.splice(0, this.state.records.length)
                }

                if (useOptions.insertStrategy === 'replace' || useOptions.insertStrategy === 'append') {
                    for (const item of result) {
                        this.state.records.push(this.refController.modelRef(this.modelName, item._pk))
                    }
                }

                if (useOptions.insertStrategy === 'prepend') {
                    for (let i = result.length - 1; i >= 0; i--) {
                        this.state.records.unshift(this.refController.modelRef(this.modelName, result[i]._pk))
                    }
                }
            }

            this.error.value = undefined

            if (deduplicatedResourceList.length === 0 && resourceList.length !== 0) {
                if (useOptions.insertStrategy === 'append') {
                    this.recursiveRequestsCounter++
                    await this.pagination.value?.loadNextPage()
                } else if (useOptions.insertStrategy === 'prepend') {
                    this.recursiveRequestsCounter++
                    await this.pagination.value?.loadPreviousPage()
                }
            } else if (this.recursiveRequestsCounter !== 0) {
                this.recursiveRequestsCounter = 0
            }
        } catch (e: any) {
            this.error.value = e

            if (e instanceof ApiError || throwError) {
                throw e
            }

            console.error(e)
        } finally {
            if (options.insertStrategy === 'replace') {
                this.clearMemoryLinks()
            }

            this.memoryLinks.push(...this.localMemoryLinks)

            this.clearLocalMemoryLinks()

            this.loading.value = false
            this.loaded.value = true
        }

        return this.state.records
    }

    private async fetchResourceList() {
        const options = this.options as OptionsWithDefinedResource

        const resource = options.resource

        let resourceList: PrimaryKey[]

        if (isRef(resource)) {
            resourceList = resource.value
        } else {
            const isArchiveRequest = await resolveAwaitable(resource.archived)

            const shouldCache = !options.skipResourceCache?.includes(resource.name)
            const cacheStrategy = shouldCache ? ResourceCacheStrategy.Cache : ResourceCacheStrategy.IgnoreAll

            if (options.aggregate && this.resourceContextOptions.http.workspace === AnyWorkspace) {
                const workspaces = useService('workspace').availableWorkspaces.map((workspace) => workspace.pk)

                const result = await Promise.all(
                    workspaces.map((workspace) => {
                        return useResourceFetch(resource.name, {
                            http: { workspace },
                        }).fetchResource(resource.pk, cacheStrategy, isArchiveRequest)
                    }),
                )

                this.localMemoryLinks.push(result)

                resourceList = unique(result.flatMap((item) => item.list))

                // @todo Aggregated lists
            } else {
                const result = await useResourceFetch(resource.name, this.resourceContextOptions).fetchResource(resource.pk, cacheStrategy, isArchiveRequest) as ListResource

                this.localMemoryLinks.push(result)

                resourceList = result.list
            }
        }

        let pagination: Pagination | undefined = undefined

        if (options.pageSize || options.page) {
            pagination = new FrontendPagination(resourceList, {
                pageSize: toValue(options.pageSize) || 10,
                page: toValue(options.page) || 1,
            })
        }

        return {
            resourceList,
            pagination,
        }
    }

    private async fetchSimpleList(options: SimpleRequestOptions, forcedOptions: SimpleRequestOptions) {
        const pks = options.pks && toValue(options.pks)

        if (pks) {
            return {
                resourceList: pks,
            }
        }

        const where = new ArrayConditionHelper()

        if (options.where) {
            options.where(where)
        }

        let limit = options?.limit

        if (!limit && limit !== false) {
            limit = undefined
        }

        const sort = this.getSortOptions(options)

        const requestPage = toValue(forcedOptions.page) ?? this.pagination.value?.page.current ?? toValue(options.page)

        let requestQuery = {
            page: requestPage,
            pageSize: toValue(forcedOptions.pageSize) ?? this.pagination.value?.page.size ?? toValue(options.pageSize),
            limit,
            where: options.where ? where.getParams() : undefined,
            orderBy: sort ? Object.entries(sort) : undefined,
            pageFromPk: options.pageFromPk && !this.loaded.value ? options.pageFromPk : undefined,
        }

        const customParams = toValue(options.customParams)

        if (isDevelopment && customParams) {
            for (const key of Object.keys(customParams)) {
                if (key in requestQuery) {
                    useLogger('resource-list').fatal(`There is duplicated custom param in options: "${key}"`)
                }
            }
        }

        requestQuery = {
            ...customParams,
            ...requestQuery,
        }

        const requestKey = this.generateRequestListKey(requestQuery)

        if (this.requestListKey !== requestKey) {
            this.requestListKey = requestKey
            this.preloadedRecords = {}
        }

        if (this.preloadedRecords && this.preloadedRecords[String(requestPage)]) {
            return this.preloadedRecords[String(requestPage)]
        }

        const requestCacheKey = JSON.stringify({
            modelName: this.modelName,
            requestQuery,
        })

        const {
            result: resourceList,
            pagination: paginationData,
        } = await this.searchCacheStorage.remember(requestCacheKey, async () => {
            return await useHttp(this.resourceContextOptions.http).get<ApiListResponse<PrimaryKey[]>>(useApiRoute().search(this.modelName), {
                query: requestQuery,
            })
        }, options.cacheForMs || 0, {
            onHit: (key, data) => {
                useLogger('resource-list:search').log('Search cache hit', key, data)
            },
        })

        return {
            resourceList,
            pagination: paginationData && !limit && !Array.isArray(paginationData) ? new BackendPagination(paginationData) : undefined,
            memoryLinks: [],
            requestKey,
        }
    }

    private preloadList(options: SimpleRequestOptions) {
        if (!this.preloadOptions || !this.preloadOptions.direction) {
            return
        }

        if (this.preloadOptions.pagesAmount && this.pagination.value?.page.current) {
            this.preloadInProgress = true
            const requestsToFetch: Promise<SimpleRequestResponse>[] = []

            if (['next', 'both'].includes(this.preloadOptions.direction) && this.preloadOptions.pagesAmount.next) {
                for (let i = 1; i <= this.preloadOptions.pagesAmount.next; i++) {
                    const requestedPage = this.pagination.value?.page.current + i

                    if (requestedPage > this.pagination.value?.page.count) {
                        continue
                    }

                    requestsToFetch.push(this.fetchRequestedPage(options, requestedPage))
                }
            }

            if (['previous', 'both'].includes(this.preloadOptions.direction) && this.preloadOptions.pagesAmount.previous) {
                for (let i = this.preloadOptions.pagesAmount.previous; i > 0; i--) {
                    const requestedPage = this.pagination.value?.page.current - i

                    if (requestedPage <= 0) {
                        continue
                    }

                    requestsToFetch.push(this.fetchRequestedPage(options, requestedPage))
                }
            }

            Promise.all(requestsToFetch).then((response) => {
                response.map(requestResponse => {
                    const fetchedPage = requestResponse.pagination ? requestResponse.pagination.page.current : undefined

                    if (fetchedPage && requestResponse.requestKey && requestResponse.requestKey === this.requestListKey) {
                        this.preloadedRecords[fetchedPage] = requestResponse
                    }
                })
                this.preloadInProgress = false
            })
        }
    }

    private fetchRequestedPage(options: SimpleRequestOptions, requestedPage: number) {
       return this.fetchSimpleList(options, { page: requestedPage, pageFromPk: undefined })
    }

    public isRelationDeclared(relation: string) {
        return this.options.with?.includes(relation as any) || (relation in (this.options.withOptional ?? {})) || (relation in (this.options.withCustom ?? {}))
    }

    public isRelationLoaded(relation: string) {
        return this.state.loadedRelations.has(relation)
    }

    public async loadRelations(relations: string[], data?: ModelIdentification[]) {
        const fetches = []

        data ||= this.records

        const declaredRelations = this.relations.filter((relation) => relations.includes(relation))

        if (this.relations.length) {
            fetches.push(
                this.loadStaticRelations(data, declaredRelations, this.options.withOptional),
            )
        }

        const customRelations = pluckObjectKeys(this.options.withCustom ?? {}, relations)

        if (customRelations) {
            fetches.push(
                this.loadCustomRelations(data, customRelations),
            )
        }

        if (fetches.length) {
            await Promise.all(fetches)
        }
    }

    private async loadStaticRelations(result: Model.Attributes[], relationFields: string[], conditions: Partial<Record<string, () => boolean>> = {}) {
        for (const relationField of relationFields) {
            if (conditions[relationField]) {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                this.optionalRelationsStorage[relationField] = conditions[relationField]!()
            }
        }

        await this.modelController.fetchRelations(
            this.modelName,
            result,
            relationFields,
            this.localMemoryLinks,
            undefined,
            this.optionalRelationsStorage,
            undefined,
            await resolveAwaitable(this.options.withArchiveData === undefined ? true : this.options.withArchiveData),
            this.options.skipResourceCache,
        )

        for (const relationField of relationFields) {
            const meetsConditions = conditions[relationField] ? this.optionalRelationsStorage[relationField] : true

            if (meetsConditions && !this.state.loadedRelations.has(relationField)) {
                this.state.loadedRelations.add(relationField)
            } else if (!meetsConditions) {
                this.state.loadedRelations.delete(relationField)
            }
        }
    }

    private async loadCustomRelations(result: Model.Attributes[], customRelations: Record<string, CustomRelationResolver>) {
        const pks = result.map((item) => item._pk)

        await this.modelController.fetchCustomRelations(pks, customRelations, this.customRelationsStorage)

        for (const relationName in customRelations) {
            this.state.loadedRelations.add(relationName)
        }
    }

    private async waitForDependenciesReady() {
        if (!this.dependancies.length) {
            return
        }

        const depends = this.dependancies.map((depend) => toValue(depend))

        await Promise.all(depends)
    }

    public depends(fn: MaybeRefOrGetter<Promise<any>>) {
        this.dependancies.push(fn)
    }

    private generateRequestListKey(requestQueryOptions: Record<string, unknown>) {
        const requestQuery = {
            pageSize: requestQueryOptions.pageSize || undefined,
            limit: requestQueryOptions.limit || undefined,
            where: requestQueryOptions.where || undefined,
            orderBy: requestQueryOptions.orderBy || undefined,
        }

        return JSON.stringify(requestQuery)
    }
}

export type CustomRelationResolver = (pks: PrimaryKey[], resourceContextOptions: ResourceContextOptions) => Promise<any[]>

type GeneralRequestOptions<TRelationField extends string = string, TModelName extends ModelName = never> = {
    // Relations to load. Supports nested relations via dot notation. Example: ['agent', 'agent.team']
    with?: TRelationField[],

    // Custom relations to load. Always performed for a set of primary keys, and should return an array of the same length and order
    withCustom?: {
        [relationName: string]: CustomRelationResolver
    },

    // Relations to load only if some condition is met
    withOptional?: {
        [Field in TRelationField]?: () => boolean
    },

    // Page number
    page?: MaybeRefOrGetter<number>,

    // If you need get active element page
    pageFromPk?: PrimaryKey,

    // Number of records per page. Enables pagination
    pageSize?: MaybeRefOrGetter<number>,

    // Limits the number of records without enabling pagination
    limit?: number | false, // 0, empty string will work as undefined

    // Will be called before fetch
    depends?: MaybeRefOrGetter<Promise<any>> | MaybeRefOrGetter<Promise<any>>[],

    // If you need to modify the primary key of received from search records
    // Useful when you need to fetch additional record that has composite primary key
    keyModifier?: (key: PrimaryKey) => PrimaryKey,

    // If you need to preload several pages
    preload?: PreloadOptions

    // Custom query parameters
    customParams?: MaybeRefOrGetter<AnyObject>

    // Archive
    withArchiveData?: MaybeRefOrGetter<Awaitable<boolean>>

    // Resource cache
    skipResourceCache?: ResourceName[],
}

export type SimpleRequestOptions<
    TRelationField extends string = string,
    TModelName extends ModelName = any
> = GeneralRequestOptions<TRelationField, TModelName> & {
    resource?: never

    // If you provide pks, one request will be skipped
    pks?: MaybeRefOrGetter<PrimaryKey[] | false>,

    // First search will be from archive
    fromArchive?: MaybeRefOrGetter<Awaitable<boolean>>,

    // Conditions to filter the list
    where?: (and: ArrayConditionAndOr<TModelName>) => void,

    // Order of the list
    sort?: SortController | MaybeRefOrGetter<{
        [field: string]: SortDirection
    }>,

    // If you need to add records to the end of the list
    insertRecordsToList?: 'append' | 'prepend' | undefined,

    // Cache
    cacheForMs?: number,
}

export type PreloadOptions = {
    pagesAmount: {
        next?: number,
        previous?: number
    } | number,
    direction?: 'next' | 'previous' | 'both' | 'auto'
}

export type PreloadNormalizedOptions = {
    pagesAmount: {
        next: number,
        previous: number
    },
    direction: 'next' | 'previous' | 'both'
} | undefined

export type SimpleRequestResponse = {
    resourceList: string[],
    pagination?: Pagination
    memoryLinks?: unknown,
    requestKey?: string
}

export type ResourceRequestOptions<
    TRelationField extends string = string
> = GeneralRequestOptions<TRelationField> & {
    where?: never

    // List Resource identifier. Can't be used with 'where' option
    resource?: {
        name: DefinedResourceName,
        pk: PrimaryKey,
        archived?: MaybeRefOrGetter<Awaitable<boolean>>,
    } | ComputedRef<PrimaryKey[]>,

    // If resource context has complex workspace (e.g. AnyWorkspace),
    // Many resource lists will be fetched for each workspace and merged into one list
    aggregate?: boolean
}

export type ContinuousResourceRequestOptions = {
    /**
     * Resets page to the options passed in constructor
     */
    resetPage?: boolean,

    // If you need to add records to the specific part of the list
    insertStrategy?: 'append' | 'prepend' | 'replace'
}

const normalizeWith = (withRelations: string[] | undefined, withOptionalRelations?: Partial<Record<string, () => boolean>>): string[] => {
    if (!withRelations && !withOptionalRelations) {
        return []
    }

    const result: string[] = []

    const relations = unique([
        ...(withRelations ?? []),
        ...(withOptionalRelations ? Object.keys(withOptionalRelations) : []),
    ])

    for (const relation of relations) {
        const parts = relation.split('.')

        for (let i = 0; i < parts.length; i++) {
            const current = parts.slice(0, i + 1).join('.')

            if (!result.includes(current)) {
                result.push(current)
            }
        }

        result.push(relation)
    }

    return [...new Set(result)]
}

const normalizePreloadOptions = (pageFromPk: PrimaryKey | undefined, preloadOptions: PreloadOptions | undefined): PreloadNormalizedOptions => {
    if (!preloadOptions) {
        return
    }

    let directionPagesAmount

    if (typeof(preloadOptions.pagesAmount) === 'object') {
        directionPagesAmount = {
            next: preloadOptions.pagesAmount.next ?? 0,
            previous: preloadOptions.pagesAmount.previous ?? 0,
        }
    } else {
        directionPagesAmount = {
            next: preloadOptions.pagesAmount ?? 0,
            previous: preloadOptions.pagesAmount ?? 0,
        }
    }

    if (!preloadOptions.direction || preloadOptions.direction === 'auto') {
        const normalizedDirection = pageFromPk ? 'both' : 'next'

        return {
            pagesAmount: directionPagesAmount,
            direction: normalizedDirection,
        }
    }

    return {
        pagesAmount: directionPagesAmount,
        direction: preloadOptions.direction,
    }
}

export async function resolveAwaitable(resolver: MaybeRefOrGetter<Awaitable<boolean>> | undefined): Promise<boolean> {
    if (!resolver) {
        return false
    }

    return toValue(resolver)
}

export const __test__normalizeWith = normalizeWith

type OptionsWithDefinedResource = ResourceRequestOptions & {
    resource: NonNullable<ResourceRequestOptions['resource']>
}

export type ExtractRelationsFromOptions<TRequestOptions> = TRequestOptions extends {
    with: infer A
} ? A extends Array<infer T extends string> ? Model.NormalizeRelationsList<T> : never : never

export type ExtractCustomRelationsFromOptions<TRequestOptions> = TRequestOptions extends {
    withCustom: infer A
} ? A extends Record<string, CustomRelationResolver> ? {
    [K in keyof A]: Awaited<ReturnType<A[K]>>[number]
} : never : never

export type ExtractOptionalRelationsFromOptions<TRequestOptions> = TRequestOptions extends {
    withOptional: infer A
} ? Model.NormalizeRelationsList<Extract<keyof A, string>> : never

// @todo Get rid of this shit when WebStorm will have good ts support. Maybe when Volar will be available in 2023.2
// @todo 16.07.2024. Volar sucks.
// type UnionToIntersection<U> =
//     (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never
// type LastOf<T> = UnionToIntersection<
//     T extends any ? () => T : never> extends () => (infer R) ? R : never
// type Dedupe<T, L = LastOf<T>, N = [T] extends [never] ? true : false> =
//     true extends N ? never : Dedupe<Exclude<T, L>> | L;

