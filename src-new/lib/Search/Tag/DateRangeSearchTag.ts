import type { SearchTagOptions } from '~/lib/Search/Tag/SearchTag'
import SearchTag from '~/lib/Search/Tag/SearchTag'
import DateRangeSearchTagComponent from '~/components/Search/Tag/DateRangeSearchTag.vue'
import type ArrayConditionAndOr from '~/lib/Helper/ArrayCondition/Conditions/ArrayConditionAndOr'
import type { DateRange, DateRangeName } from '@/lib/core/helper/DateHelper'
import {
    dateRangeToDateRangeString,
    getPredefinedRange,
    moveDateRangeFromTimeZone,
    moveDateRangeToTimeZone,
    parseDateStringToDateRange,
    predefinedRanges,
} from '@/lib/core/helper/DateHelper'
import { useAppTimezone } from '~/composables/useAppTimezone'

const regex = /\d{2}\/\d{2}\/\d{4}(-\d{2}\/\d{2}\/\d{4})?$/

interface DateRangeSearchTagOptions extends SearchTagOptions {
    datePresets?: DateRangeName[] |((defaultPresets: DateRangeName[]) => DateRangeName[])
}

type Value = DateRange | DateRangeName

/**
 * We keep date in query as is (in application timezone). When we hydrate the value, we convert it to UTC.
 * When we sync the value with query, we convert it back to application timezone.
 */
export default class DateRangeSearchTag extends SearchTag<Value> {
    public component = DateRangeSearchTagComponent

    declare protected appTimezone: string

    public constructor(protected label: string, protected options: DateRangeSearchTagOptions = {}) {
        super(label, options)
    }

    //

    public get datePresets() {
        return this.options.datePresets
    }

    //

    protected init() {
        super.init()

        this.appTimezone = useAppTimezone().value
    }

    protected hydrateValue(value: string): Value | undefined {
        for (const rangeName of Object.keys(predefinedRanges)) {
            const predefinedRange = predefinedRanges[rangeName as DateRangeName]

            if (value === predefinedRange.label) {
                return rangeName as DateRangeName
            }
        }

        if (!regex.test(value)) {
            return
        }

        return moveDateRangeFromTimeZone(parseDateStringToDateRange(value), this.appTimezone)
    }

    public toQueryValue(value: Value): string {
        if (typeof value === 'string') {
            const predefinedRange = predefinedRanges[value]

            return predefinedRange.label
        }

        return dateRangeToDateRangeString(moveDateRangeToTimeZone(value, this.appTimezone))
    }

    public getDateRangeValue(tagValue: Value): DateRange {
        if (typeof tagValue === 'string') {
            // We did not move timezone on set, so we need to move it now
            return moveDateRangeFromTimeZone(getPredefinedRange(tagValue, this.appTimezone), this.appTimezone)
        }

        // We moved timezone on set
        return tagValue
    }

    public get normalizedValues(): Value[] | undefined {
        return this.values?.map(value => {
            return this.getDateRangeValue(value)
        })
    }

    protected _applyCondition(field: string, and: ArrayConditionAndOr<any>) {
        and.or(or => {
            for (const range of this.values ?? []) {
                const value = this.getDateRangeValue(range)

                or.and(q => {
                    q.gte(field, value.start.unixTimestamp())
                    q.lte(field, value.end.unixTimestamp())
                })
            }
        })
    }
}
