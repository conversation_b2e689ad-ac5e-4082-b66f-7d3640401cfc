import type { RouteDefinition } from '~/router'
import { defineRoutePermission } from '~/utils/define'

import authRoutes from '~/router/routes/authRoutes'
import systemRoutes from '~/router/routes/systemRoutes'
import utilityRoutes from '~/router/routes/development/utilityRoutes'
import statisticsRoutes from '~/router/routes/statisticsRoutes'
import agentsRoutes from '~/router/routes/agentsRoutes'
// import bookkeepingRoutes from '~/router/routes/bookkeepingRoutes'
import clientsRoutes from '~/router/routes/clientsRoutes'
import designRoutes from '~/router/routes/development/designRoutes'
import leadsRoutes from '~/router/routes/leadsRoutes'
import salesRoutes from '~/router/routes/salesRoutes'
import settingsRoutes from '~/router/routes/settingsRoutes'
import stylesheetRoutes from '~/router/routes/development/stylesheetRoutes'
import teamsRoutes from '~/router/routes/teamsRoutes'
import transactionsRoutes from '~/router/routes/transactionsRoutes'
import releasePostRoutes from '~/router/routes/releasePostRoutes'
import issueRoutes from '~/router/routes/issueRoutes'
import invoicesRoutes from '~/router/routes/invoicesRoutes'
import awardAccountsRoutes from '~/router/routes/awardAccountsRoutes'
import airlineReportRoutes from '~/router/routes/airlineReportRoutes'
import performanceFeedbackRoutes from '~/router/routes/performanceFeedbackRoutes'
import contactRuleRoutes from '~/router/routes/contactRuleRoutes'
import agentReportRoutes from '~/router/routes/agentReportRoutes'
import summaryDashboardRoutes from '~/router/routes/summaryDashboardRoutes'
import leadManagementRoutes from '~/router/routes/leadManagementRoutes'
import expectedAmountsRoutes from '~/router/routes/expectedAmountsRoutes'
import companyContactRoutes from '~/router/routes/companyContactRoutes'
import priceDropRoutes from '~/router/routes/priceDropRoutes'
import creditVouchersRoutes from '~/router/routes/creditVouchersRoutes'
import erlFrtCheckRoutes from '~/router/routes/elrFrtCheckRoutes'
import gamblingToolRoutes from '~/router/routes/gamblingToolRoutes'
import callManagementRoutes from '~/router/routes/callManagementRoutes'
import pnrInfoRoutes from '~/router/routes/pnrInfoRoutes'
import vccManagementRoutes from '~/router/routes/vccManagementRoutes'

import markupRoutes from '@tmg/markup-tool-frontend/routes'
import gdsTerminalRoutes from '~modules/gds-terminal/src/routes'
import consolidatorRoutes from '@tmg/consolidator-tool-frontend/routes'
import pnrScheduleRoutes from '~/router/routes/pnrScheduleRoutes'

const routes: RouteDefinition[] = [
    ...authRoutes,
    ...systemRoutes,
    ...settingsRoutes,

    // Menu routes. If sortMenu is not set in meta, the route will be ordered by position in the array
    ...leadsRoutes,
    ...leadManagementRoutes,
    ...agentsRoutes,
    ...clientsRoutes,
    ...salesRoutes,
    ...transactionsRoutes,
    ...releasePostRoutes,
    ...invoicesRoutes,
    ...statisticsRoutes,
    // ...bookkeepingRoutes,
    ...issueRoutes,
    ...teamsRoutes,
    ...agentReportRoutes,
    ...airlineReportRoutes,
    ...performanceFeedbackRoutes,
    ...summaryDashboardRoutes,
    ...priceDropRoutes,
    ...pnrScheduleRoutes,
    ...contactRuleRoutes,
    ...companyContactRoutes,
    ...awardAccountsRoutes,
    ...creditVouchersRoutes,
    ...erlFrtCheckRoutes,
    ...gamblingToolRoutes,
    ...callManagementRoutes,
    ...vccManagementRoutes,

    // GDS terminal
    ...gdsTerminalRoutes,

    // Vendor routes
    ...markupRoutes,
    ...consolidatorRoutes,

    ...expectedAmountsRoutes,
    ...pnrInfoRoutes,

    // Development only routes
    ...(isDevelopment ? applyDeveloperPermissions([
        ...utilityRoutes,
        ...stylesheetRoutes,
        ...designRoutes,
    ]) : []),
]

export default applyOrder(routes)

function applyDeveloperPermissions(routes: RouteDefinition[]) {
    for (const route of routes) {
        route.meta ||= {}

        route.meta.permission = defineRoutePermission('all', 'manage')
    }

    return routes
}

function applyOrder(routes: RouteDefinition[]) {
    for (const i in routes) {
        const route = routes[i]

        route.meta ||= {}

        route.meta._defaultMenuSort = i
    }

    return routes
}
