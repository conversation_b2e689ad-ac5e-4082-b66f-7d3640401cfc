import AppService from '~/service/AppService'
import type { RemoveListenerCallback, SystemStorageWorkerData } from '~/service/StorageWorker/StorageWorker'
import { getSystemChannelName } from '~/lib/ResourceStorage/ResourceStorage'
import type { WatchStopHandle } from 'vue'
import { useStorage } from '@vueuse/core'
import { AnyWorkspace } from '~/service/WorkspaceService'
import { groupBy, mapAsyncParallel } from '~/lib/Helper/ArrayHelper'
import { ResourceCacheStrategy, useResourceFetch } from '~/composables/useResourceFetch'
import { defaultLabel } from '~/lib/Helper/ConsoleHelper'
import { hasResourceDefinition } from '~/composables/_core/useResourceDefinition'
import { numberRange } from '~/lib/Helper/NumberHelper'
import ApiForbiddenError from '~/lib/Error/Api/ApiForbiddenError'
import { useUniqueExecution } from '~/composables/useUniqueExecution'

const maxDisconnectTimeToSyncChangesMs = Timespan.millisecond().inMilliseconds
const intervalToSaveConnectionTimeMs = Timespan.seconds(2).inMilliseconds

const localStorageKeys = {
    lastConnectTime: 'resource-last-connect-time',
    lastSyncedBlock: 'resource-last-synced-block',
    lastUserPk: 'last-logged-in-user',
}

export type ResourceChangesResponse = {
    lastBlock: number,
    changes: Changes,
}

export type ResourceChangesBlockResponse = {
    changes: Changes,
}

type Changes = SystemStorageWorkerData['checksumChanged'][]

export default class ResourceCacheManager extends AppService {
    public authRequired = true

    private _state = reactive({
        connected: false,
    })

    public lastConnectTimeMs = useStorage(localStorageKeys.lastConnectTime, 0)
    public lastSyncedBlock = useStorage(localStorageKeys.lastSyncedBlock, 0)

    // =====

    private removeCallbacks: RemoveListenerCallback[] = []
    private declare unwatch: WatchStopHandle

    public async register(): Promise<void> {
        await this.invalidateCacheForDifferentUser()

        this.listenToSystemEvents()

        this.watchForDisconnect()
    }

    public unregister(): void | Promise<void> {
        for (const fn of this.removeCallbacks) {
            fn()
        }

        this.unwatch()
    }

    // ===================

    private get connected() {
        return this._state.connected
    }

    private set connected(value: boolean) {
        this._state.connected = value
    }

    private watchForDisconnect() {
        let interval: NodeJS.Timeout

        const unwatch = watch(() => this.connected, async (connected) => {
            if (connected) {
                useLogger('resource-cache-manager').success('Connected')
            } else {
                useLogger('resource-cache-manager').error('Disconnected')
            }

            if (!connected) {
                // Stop writing last connection time
                clearInterval(interval)

                // Set last connect time to now. Minus second for script evaluation
                this.lastConnectTimeMs.value = Date.now() - 1000

                return
            }

            const timePassedSinceLastConnectMs = Date.now() - this.lastConnectTimeMs.value

            useLogger('resource-cache-manager').info('Time from last connection:', timePassedSinceLastConnectMs / 1000, 'seconds')

            if (timePassedSinceLastConnectMs >= maxDisconnectTimeToSyncChangesMs) {
                useLogger('resource-cache-manager').log('Sync changes')

                await this.syncChanges(this.lastConnectTimeMs.value)
            }

            // Start writing last connection time
            interval = setInterval(() => {
                this.lastConnectTimeMs.value = Date.now()
            }, intervalToSaveConnectionTimeMs)
        })

        this.unwatch = () => {
            unwatch()
            clearInterval(interval)
        }
    }

    private async syncChanges(lastEventTime: number) {
        const {
            lastBlock,
            changes,
        } = await useHttp({
            workspace: AnyWorkspace,
        }).get<ResourceChangesResponse>(useApiRoute().changes(lastEventTime))

        const lastFullBlock = lastBlock - 1

        try {
            await this.ensureThereAreNotTooManyChangesToBlock(lastFullBlock)

            await this.handleChanges(changes)

            await this.syncChangesToBlock(lastFullBlock)
        } catch (e) {
            useLogger('resource-cache-manager').warn('Error while syncing changes', e)

            await this.invalidateAllCache()

            this.lastSyncedBlock.value = lastBlock
        }
    }

    protected async ensureThereAreNotTooManyChangesToBlock(block: number) {
        const lastSyncedBlock = this.lastSyncedBlock.value

        const blocksToFetch = block - lastSyncedBlock

        if (!lastSyncedBlock || blocksToFetch >= config.resource.maxCacheBlockCount) {
            throw new Error('Too many changes')
        }
    }

    protected async syncChangesToBlock(lastBlock: number) {
        const lastSyncedBlock = this.lastSyncedBlock.value

        const blockToFetchCount = lastBlock - lastSyncedBlock

        if (blockToFetchCount > config.resource.maxCacheBlockCount) {
            return
        }

        if (blockToFetchCount <= 0) {
            return
        }

        // We handle changes from last to first to optimize cache invalidation
        const blocksToFetch = numberRange(lastSyncedBlock + 1, lastBlock).reverse()

        await mapAsyncParallel(blocksToFetch, async (blockNumber) => {
            const { changes } = await useHttp({
                workspace: AnyWorkspace,
            }).get<ResourceChangesBlockResponse>(useApiRoute().changesBlock(blockNumber))

            await this.handleChanges(changes)
        })

        this.lastSyncedBlock.value = lastBlock
    }

    private async handleChanges(changes: Changes) {
        const groupedByResource = groupBy(changes, 'model')

        const service = useService('resource')

        // Parallel validation for every resource
        await mapAsyncParallel(Object.entries(groupedByResource), async ([resourceName, items]) => {
            if (!hasResourceDefinition(resourceName as ResourceName)) {
                return
            }

            const storage = service.getStorage(resourceName as ResourceName)

            if (!storage) {
                return
            }

            for (const item of items) {
                // Do not parallel validation, because we can hit the same storage for write at one moment
                await storage.validateDatabaseItem(item.pk, item.checksum)
            }
        })
    }

    public async invalidateAllCache() {
        useLogger('resource-cache-manager').warn('Invalidating all cache')

        await useService('resource').clearCache()
    }

    private async invalidateCacheForDifferentUser() {
        const pk = useService('workspace').getUserDefaultWorkspace().account_pk

        const lastUserPk = localStorage.getItem(localStorageKeys.lastUserPk)

        if (lastUserPk !== pk) {
            await this.invalidateAllCache()

            localStorage.setItem(localStorageKeys.lastUserPk, pk)
        }
    }

    //

    private listenToSystemEvents() {
        const service = useService('storageWorker')
        const resourceService = useService('resource')

        this.removeCallbacks.push(
            service.onConnect(() => {
                this.connected = true
            }),

            service.onSystemEvent(getSystemChannelName(), async (event, data) => {
                if (event === '_mockSeed') {
                    await this.invalidateAllCache()
                } else if (event === 'checksumChanged') {
                    data = data as SystemStorageWorkerData['checksumChanged']

                    const storage = useService('resource').getStorage(data.model)

                    if (!storage) {
                        return
                    }

                    const pk = data.pk

                    if (data.isTokenChanged && data.checksum?.length) {
                        await storage.deleteFromCache(pk)

                        storage.stopListeningToChanges(pk)

                        const wasFetchedInWorkspace = storage.getMemoryItemMeta(pk)?.workspace

                        if (wasFetchedInWorkspace) {
                            useLogger('resource-cache-manager').log(...defaultLabel(data.model), pk, '| Token changed, refetching item in workspace', wasFetchedInWorkspace)
                        } else {
                            useLogger('resource-cache-manager').log(...defaultLabel(data.model), pk, '| Token changed, but item was not fetched in workspace, skipping refetching')
                        }

                        if (wasFetchedInWorkspace) {
                            try {
                                await useResourceFetch(data.model, { http: { workspace: wasFetchedInWorkspace } }).fetchResource(pk, ResourceCacheStrategy.IgnoreFrontend)
                            } catch (e: unknown) {
                                useLogger('resource-cache-manager').log(...defaultLabel(data.model), pk, '| Error while fetching item after token change')

                                const memoryItem = storage.getItemInMemory(pk)

                                if (memoryItem) {
                                    await storage.updateItem(pk, {
                                        ...memoryItem,
                                        _token: undefined,
                                    }, undefined, false)
                                }
                            }
                        }
                    } else {
                        await storage.validateDatabaseItem(pk, data.checksum)
                    }

                    //

                    resourceService.eventBus.emit('checksumChanged', data.model, pk)
                } else if (event === 'incrementBlockNumber') {
                    data = data as SystemStorageWorkerData['incrementBlockNumber']

                    const lastBlock = data.current

                    try {
                        await useUniqueExecution(`incrementBlockNumber:${lastBlock}`, { proceedInCaseOfMissingWorker: true }).execute(async () => {
                            await this.ensureThereAreNotTooManyChangesToBlock(lastBlock)
                            await this.syncChangesToBlock(lastBlock)
                        })
                    } catch (e) {
                        useLogger('resource-cache-manager').error('Error while syncing changes', e)

                        await this.invalidateAllCache()

                        this.lastSyncedBlock.value = lastBlock
                    }
                }
            }),

            service.onDisconnect(() => {
                this.connected = false
            }),
        )
    }
}
