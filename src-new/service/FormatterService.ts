import type { MaybeRefOrGetter } from 'vue'
import AppService from '~/service/AppService'
import { useAppTimezone } from '~/composables/useAppTimezone'

/**
 * This service is used to format data in a specific way
 *
 * Every method should be a pure function (no side effects, no async)
 * Every method should have return type
 * If method can accept undefined value, it should accept property as (T | Empty) and should return (T & undefined)
 *
 * @important Make sure you don't change signature of any method in this class
 */
export default class FormatterService extends AppService {
    declare private appTimezone: string

    public register(): void | Promise<void> {
        // Reactivity is not needed here, because we reaload page on timezone change
        this.appTimezone = useAppTimezone().value
    }

    public unregister(): void | Promise<void> {
        //
    }

    /**
     * Datetime formatters
     * ======================
     */
    public datetime(timestamp: number | Empty, timezone?: MaybeRefOrGetter<string>, options?: {
        full?: boolean,
        format?: string
    }): string | undefined {
        const date = Date.fromUnixTimestampOrNull(timestamp)

        if (!date) {
            return
        }

        const format = options?.format ?? `dd MMM ${options?.full ? '' : '?'}yy HH:mm`

        if (!timezone) {
            timezone = this.appTimezone
        }

        return String(date.toFormatWithTimezone(format, toValue(timezone)))
    }

    public time(timestamp: number | Empty, timezone?: MaybeRefOrGetter<string>): string | undefined {
        const date = Date.fromUnixTimestampOrNull(timestamp)

        if (!date) {
            return
        }

        const format = `HH:mm`

        if (!timezone) {
            timezone = this.appTimezone
        }

        return String(date.toFormatWithTimezone(format, toValue(timezone)))
    }

    public date(timestamp: number | Empty, timezone?: MaybeRefOrGetter<string>, options?: {
        full?: boolean
        format?: string
    }): string | undefined {
        const date = Date.fromUnixTimestampOrNull(timestamp)

        if (!date) {
            return
        }

        const format = options?.format ?? `dd MMM ${options?.full ? '' : '?'}yy`

        if (!timezone) {
            timezone = this.appTimezone
        }

        return String(date.toFormatWithTimezone(format, toValue(timezone)))
    }

    /**
     * Money formatters
     * ======================
     */
    public money(amount: number, options?: {
        withCurrency?: 'USD' | 'EUR' | 'MILES' | string | true,
        fraction?: 2 | number
    }): string {
        const fraction = options?.fraction ?? 2
        const currency = options?.withCurrency === true ? 'USD' : options?.withCurrency

        return String(Number.formatMoney(amount, currency, fraction))
    }
}
