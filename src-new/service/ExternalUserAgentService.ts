import AppService from '~/service/AppService'
import { ExternalUserAgentStatus } from '~/api/models/ExternalUserAgent/ExternalUserAgent'
import AwardAccountExternalUserAgentApproveModal
    from '~/sections/AwardAccount/modals/AwardAccountExternalUserAgentApproveModal.vue'
import { toastError, toastSuccess, toastWarning } from '@/lib/core/helper/ToastHelper'
import { $fetch } from 'ofetch'
import type { ModelAttributes } from '~types/lib/Model'
import { $confirm } from '@/plugins/ConfirmPlugin'
import { MessageType } from '@/types/enums/MessageType'

export type City = { value: string, region: string }

export interface QuickProfileFormData {
    name: string,
    os_type: string
    browser_type: string
    count: number
    start_url: string
    additional_urls: string[]
    proxy: string

    //
    country: string | undefined
    region: string | undefined
    city: City | undefined
    session_type: string | undefined
    session_time: string | undefined
    ipttl: number | undefined

    //
    format: string | undefined
    protocol: string | undefined
    proxy_list: string | undefined
    connect_proxies: string | undefined
    ip_address: string | undefined
    port: number | undefined
    login: string | undefined
    password: string | undefined
}

export const localstorageAwardAccountPortKeyName = 'award-account:port'

export class ExternalUserAgentService extends AppService {
    public unprocessedExternalUserAgentPks = reactive<PrimaryKey[]>([])
    public port = '45001'

    public register() {
        const localStoragePort = localStorage.getItem(localstorageAwardAccountPortKeyName)

        if (localStoragePort) {
            this.port = localStoragePort
        }
    }

    public unregister() {
    }

    public async checkProxy(data: { type: string, host: string, port: number, username?: string, password?: string }) {
        await this.tryToDetectLauncherAndProcessCallback(async () => {
            const token = await this.generateToken()

            await this.checkTillBrowserCoresLoaded(token)

            await $fetch(`https://launcher.mlx.yt:${this.port}/api/v1/proxy/validate`, {
                method: 'POST',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
                body: {
                    host: data.host,
                    port: data.port,
                    type: data.type,
                    password: data.password,
                    username: data.username,
                },
                retry: false,
            })
        })
    }

    public async startQuickProfile(data: QuickProfileFormData) {
        let proxyRawList: string[] = []

        if (data.proxy === 'mlx') {
            try {
                proxyRawList = (await this.createProxy({
                    country: data.country ?? 'any',
                    city: data.city?.value ?? undefined,
                    region: data.region ?? undefined,
                    protocol: data.protocol!,
                    sessionType: data.session_type!,
                    count: data.count,
                    ipttl: data.ipttl,
                })).data
            } catch (e) {
                toastError("Can't create proxy")

                return
            }
        } else if (data.proxy === 'custom' && data.format === 'list' && data.proxy_list) {
            const parsedRawProxies = data.proxy_list.split(',')

            if (parsedRawProxies.length) {
                proxyRawList.push(...parsedRawProxies)
            } else {
                toastError('Incorrect proxy list format')

                return
            }
        }

        const proxyList: { host: string, password?: string, port: number, type: string, username?: string }[] = []

        if (proxyRawList.length > 0) {
            proxyRawList.forEach(proxy => {
                const parsedProxy = proxy.split(':')

                if (parsedProxy.length === 4) {
                    proxyList.push({
                        host: parsedProxy[0],
                        password: parsedProxy[3],
                        port: Number.parseInt(parsedProxy[1]),
                        type: data.protocol!,
                        username: parsedProxy[2],
                    })
                }
            })

            if (proxyList.length !== proxyRawList.length) {
                toastError('At less one proxy has incorrect format')

                return
            }
        } else if (data.ip_address && data.port && data.protocol) {
            proxyList.push({
                host: data.ip_address,
                password: data.password,
                port: data.port,
                type: data.protocol,
                username: data.login,
            })
        }

        const token = await this.generateToken()

        return await this.tryToDetectLauncherAndProcessCallback(async () => {
            await this.checkTillBrowserCoresLoaded(token)

            try {
                const proxy = proxyList.length ? proxyList[0] : undefined

                await $fetch(`https://launcher.mlx.yt:${this.port}/api/v1/profile/quick`, {
                    method: 'POST',
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                    retry: false,
                    body: {
                        browser_type: data.browser_type,
                        os_type: data.os_type,
                        parameters: {
                            custom_start_urls: [data.start_url, ...data.additional_urls],
                            fingerprint: {},
                            storage: {
                                is_local: true,
                                save_service_worker: true,
                            },
                            flags: {
                                audio_masking: 'natural',
                                canvas_noise: 'natural',
                                fonts_masking: 'mask',
                                geolocation_masking: 'mask',
                                geolocation_popup: 'prompt',
                                graphics_masking: 'mask',
                                graphics_noise: 'mask',
                                localization_masking: 'mask',
                                media_devices_masking: 'natural',
                                navigator_masking: 'mask',
                                ports_masking: 'mask',
                                proxy_masking: data.proxy === 'none' ? 'disabled' : 'custom',
                                screen_masking: 'mask',
                                startup_behavior: 'custom',
                                timezone_masking: 'mask',
                                webrtc_masking: 'mask',
                            },
                        },
                        name: data.name,
                        quickProfilesCount: data.count,
                        proxy,
                    },
                })

                const resourceContextOptions = { http: { workspace: useService('workspace').getUserDefaultWorkspace().pk } }
                const externalUserAgentQuickProfileLog = useModel('ExternalUserAgentQuickProfileLog', resourceContextOptions)

                // noinspection ES6MissingAwait
                externalUserAgentQuickProfileLog.actions.logQuickProfile({
                    name: data.name,
                    proxy: proxy ? {
                        username: proxy.username ?? null,
                        password: proxy.password ?? null,
                        host: proxy.host,
                        port: proxy.port,
                        protocol: proxy.type,
                    } : null,
                    browser_type: data.browser_type,
                    os_type: data.os_type,
                    custom_start_urls: [data.start_url, ...data.additional_urls],

                })
            } catch (error) {
                if (error?.status?.error_code === 'CORE_DOWNLOADING_STARTED') {
                    await $confirm({ type: MessageType.Warning, cancelButton: false, text: '', description: `Your Agent is getting a fresh update.<br>Please wait a moment and try again` })
                } else if (error?.data?.status?.message) {
                    toastError(error?.data?.status?.message)
                } else {
                    toastError('Something went wrong')
                }

                return false
            }

            return true
        })
    }

    private async createProxy(
        { country, protocol, sessionType, region, city, ipttl = 0, count = 1 }:
        {
            country: string,
            protocol: string,
            sessionType: string,
            region?: string,
            city?: string,
            ipttl?: number,
            count?: number
        },
    ) {
        const token = await this.generateToken()

        return $fetch<{
            status: number,
            data: string[]
        }>(`https://profile-proxy.multilogin.com/v1/proxy/connection_url`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
            },
            body: {
                country,
                region,
                city,
                protocol,
                sessionType,
                ipttl,
                count,
            },
            retry: false,
        })
    }

    private async generateToken() {
        const resourceContextOptions = { http: { workspace: useService('workspace').getUserDefaultWorkspace().pk } }
        const token = await useModel('ExternalUserAgent', resourceContextOptions).actions.getMultiLoginToken()

        return token.token
    }

    private async tryToDetectLauncherAndProcessCallback(callback: () => Promise<boolean | void>) {
        try {
            await $fetch(`https://launcher.mlx.yt:${this.port}/api/v1/version`, {
                method: 'GET',
                retry: false,
            })

            return await callback()
        } catch (e) {
            window.location.href = `mlx:///start?port=${this.port}`

            const appIsOpened = await this.checkAndOpenApp()

            if (appIsOpened) {
                return await callback()
            }
        }

        return
    }

    public async login(externalUserAgent: ModelAttributes<'ExternalUserAgent'>, options?: {
        skipApprove?: boolean,
        skipOpening?: boolean
    }) {
        await this.tryToDetectLauncherAndProcessCallback(() => this._login(externalUserAgent, options))
    }

    private async _login(externalUserAgent: ModelAttributes<'ExternalUserAgent'>, options?: {
        skipApprove?: boolean,
        skipOpening?: boolean
    }) {
        const token = await this.generateToken()

        await this.checkTillBrowserCoresLoaded(token)

        try {
            await $fetch(`https://launcher.mlx.yt:${this.port}/api/v2/profile/f/${externalUserAgent.folder_id}/p/${externalUserAgent.profile_id}/start?automation_type=selenium1`, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
                retry: false,
            })

            if (externalUserAgent.status === ExternalUserAgentStatus.Pending && !options?.skipApprove) {
                const resourceContextOptions = { http: { workspace: useService('workspace').getUserDefaultWorkspace().pk } }

                await useModal(AwardAccountExternalUserAgentApproveModal, resourceContextOptions)
                    .open({ externalUserAgentPk: usePk(externalUserAgent) }, { position: 'center' })
            }
        } catch (error: any) {
            if (!error) {
                return
            }

            if (error.status === 500) {
                toastWarning('VPN is used by another agent')
            } else if (error.status === 400) {
                if (error?.data?.status?.error_code === 'PROFILE_ALREADY_RUNNING') {
                    toastWarning('VPN is already running')
                } else if (error?.data?.status?.error_code === 'CORE_DOWNLOADING_ALREADY_STARTED') {
                    toastWarning('Please wait until drivers are downloaded and retry')
                }
            } else if (error.status === 401) {
                toastWarning('User is not authenticated')
            } else {
                toastWarning('Please install launcher')
            }
        }
    }

    private async checkTillBrowserCoresLoaded(token: string): Promise<boolean> {
        const interval = 5000
        const timeout = 60
        let attempts = 0

        return new Promise((resolve) => {
            const intervalId = setInterval(async () => {
                try {
                    attempts += 1

                    await $fetch(`https://launcher.mlx.yt:${this.port}/api/v1/loaded_browser_cores`, {
                        method: 'GET',
                        retry: false,
                        headers: {
                            Authorization: `Bearer ${token}`,
                        },
                    })

                    clearInterval(intervalId)
                    resolve(true)
                } catch (error) {
                    if (attempts >= timeout) {
                        clearInterval(intervalId)
                        resolve(false)
                    }
                }
            }, interval)
        })
    }

    private async checkAndOpenApp(): Promise<boolean> {
        const interval = 5000
        const timeout = 60000
        let attempts = 0

        return new Promise((resolve) => {
            const intervalId = setInterval(async () => {
                try {
                    attempts += interval
                    await $fetch(`https://launcher.mlx.yt:${this.port}/api/v1/version`, {
                        method: 'GET',
                        retry: false,
                    })

                    clearInterval(intervalId)
                    resolve(true)
                } catch (error) {
                    if (attempts >= timeout) {
                        clearInterval(intervalId)
                        resolve(false)
                    }
                }
            }, interval)
        })
    }
}
