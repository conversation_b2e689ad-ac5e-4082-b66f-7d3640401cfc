import ResourceStorageManager from '~/service/ResourceStorageManager'
import BrowserSupportService from '~/service/BrowserSupportService'
import WorkspaceService from '~/service/WorkspaceService'
import ErrorHandlerService from '~/service/ErrorHandlerService'
import PermissionsService from '~/service/Permissions/PermissionsService'
import WebSocketStorageWorker from '~/service/StorageWorker/WebSocketStorageWorker'
import WebSocketMockStorageWorker from '~/service/StorageWorker/WebSocketMockStorageWorker'
import VersionControlService from '~/service/VersionControlService'
import ActivityService from '~/service/ActivityService'
import DictionaryService from '~/service/Dictionary/DictionaryService'
import ResourceCacheManager from '~/service/ResourceCacheManager'
import AppEventBusService from '~/service/AppEventBusService'
import DevelopmentValidation from '~/service/DevelopmentValidation'
import BroadcastService from '~/service/BroadcastService'
import DebugService from '~/service/DebugService'
import { getHostnameFromPath } from '~/lib/Helper/UrlHelper'
import type StorageWorker from '~/service/StorageWorker/StorageWorker'
import type AppService from '~/service/AppService'
import type PermissionsServiceInterface from '~/service/Permissions/PermissionsServiceInterface'
import FormatterService from '~/service/FormatterService'
import ChatManagerService from '~/service/ChatManagerService'
import AfkService from '~/service/AfkService'
import LaravelEchoService from '~/service/_deprecated/LaravelEchoService'
import { until } from '@vueuse/core'
import GlobalModalsService from '~/service/GlobalModalsService'
import GlobalCommandsService from '~/service/GlobalCommandsService'
import AppSettingsService from '~/service/AppSettingsService'
import WebsocketBalancerService from '~/service/WebsocketBalancerService'
import SuspiciousActivityService from '~/service/SuspiciousActivityService'
import ReleaseCheckService from '~/service/ReleaseCheckService'
import { RingCentralCallService } from '~/service/RingCentralCallService'
import ServiceWorkerService from '~/service/ServiceWorkerService'
import { TelemetryService } from '~/service/Telemetry/TelemetryService'
import RingCentralManagerService from '~/service/RingCentral/RingCentralManagerService'
import { ExternalUserAgentService } from '~/service/ExternalUserAgentService'
import MaintenanceNotificationService from '~/service/MaintenanceNotificationService'
import TerminalService from '~/service/TerminalService'
import DocumentService from '~/service/DocumentService'
import RouterService from '~/service/RouterService'
import PerformanceFeedbackService from '~/service/PerformanceFeedbackService'
// import HotkeysService from '~/service/HotkeysService'

// ======================

// Services that are initialized on application start
// They always have reference to app instance
const appServices = {
    'websocketBalancer': () => new WebsocketBalancerService(),
    'resource': () => new ResourceStorageManager(),
    'activity': () => new ActivityService(),
    'workspace': () => new WorkspaceService(),
    'resourceCacheManager': () => new ResourceCacheManager(),
    'dictionary': () => new DictionaryService(),
    'settings': () => new AppSettingsService(),
    'formatter': () => new FormatterService(),
    'debug': () => new DebugService(),
    'chat': () => new ChatManagerService(),
    'afk': () => new AfkService(),
    'globalModals': () => new GlobalModalsService(),
    'globalCommands': () => new GlobalCommandsService(),
    'suspiciousActivityService': () => new SuspiciousActivityService(),
    'releaseCheckService': () => new ReleaseCheckService(),
    'ringCentralCallService': () => new RingCentralCallService(),
    // 'hotkeys': () => new HotkeysService(),
    'serviceWorker': () => new ServiceWorkerService(),
    'telemetry': () => new TelemetryService(),
    'ringcentralManager': () => new RingCentralManagerService(),
    'multilogin': () => new ExternalUserAgentService(),
    'maintenanceNotification': () => new MaintenanceNotificationService(),
    'document': () => new DocumentService(),
    'router': () => new RouterService(),
    'performanceFeedback': () => new PerformanceFeedbackService(),
} as const satisfies {
    [serviceKey: string]: () => AppService

    // Define service interfaces below
}

// Services that are initialized on first use
// They don't have reference to app instance
const systemServices = {
    'browserSupport': () => new BrowserSupportService(),
    'versionControl': () => new VersionControlService(),
    'broadcast': () => new BroadcastService(),
    'developmentValidation': () => new DevelopmentValidation(),
    'errorHandler': () => new ErrorHandlerService(),
    'permissions': () => new PermissionsService(),
    'event': () => new AppEventBusService(),
    'storageWorker': () => {
        if (isMock) {
            return new WebSocketMockStorageWorker({
                host: config.mock.ws.host,
                port: config.mock.ws.port,
            })
        }

        return new WebSocketStorageWorker({
            host: config.ws.host ?? getHostnameFromPath(config.api.base) ?? window.location.hostname,
            port: config.ws.port,
        })
    },
    'terminal': () => new TerminalService(),

    // @deprecated
    'laravelEcho': () => new LaravelEchoService(),
} as const satisfies {
    [serviceKey: string]: () => any

    // Define service interfaces below
    // Note that abstract classes can be used there as interfaces
    storageWorker: () => StorageWorker
    permissions: () => PermissionsServiceInterface
}

// @todo Create services that can be initialized with some data, and their initialization will be required before we use them

// ======================

const services = {
    ...systemServices,
    ...appServices,
}

export function useService<TServiceName extends ServiceName>(serviceName: TServiceName) {
    if (!services[serviceName]) {
        throw new Error(`Service "${serviceName}" not found`)
    }

    if (!storage[serviceName]) {
        // @ts-ignore
        storage[serviceName] = services[serviceName]()
    }

    return storage[serviceName] as Service<TServiceName>
}

export const appServicesNames = Object.keys(appServices) as (keyof typeof appServices)[]

export const appIsInitiated = ref(false)
export const authRequiredServicesAreInitiated = ref(false)

export async function waitForAuthRequiredServicesRegistration() {
    return until(() => authRequiredServicesAreInitiated.value).toBeTruthy()
}

export function assertAppIsInitiated() {
    if (!appIsInitiated.value) {
        throw new Error('Function can be called only after application is initiated')
    }
}

type Services = typeof services
export type ServiceName = keyof Services
type Service<TServiceName extends ServiceName> = ReturnType<Services[TServiceName]>

const storage: {
    [TServiceName in ServiceName]?: Service<TServiceName>
} = {}
