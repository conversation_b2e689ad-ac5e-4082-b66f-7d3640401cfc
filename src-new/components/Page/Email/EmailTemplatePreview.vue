<template>
    <SuspenseManual :state="suspense">
        <div class="flex flex-col" v-bind="$attrs">
            <div v-if="previewUrl" class="flex-grow overflow-x-hidden overflow-y-auto fancy-scroll">
                <iframe
                    :src="previewUrl"
                    class="w-full h-full border-none"
                />
            </div>
        </div>

        <template #fallback>
            <div class="p-4" v-bind="$attrs">
                <PlaceholderBlock class="h-full w-full" />
            </div>
        </template>

        <template #error>
            <div v-bind="$attrs" class="p-8 flex items-center justify-center text-danger fond-bold gap-2">
                <AlertCircleIcon />
                Can't load preview
            </div>
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import type { EmailSubject, EmailTemplateName, EmailType } from '~/api/models/Email/EmailTemplate'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'

defineOptions({
    name: 'EmailTemplatePreview',
    inheritAttrs: false,
})

//

const props = defineProps<{
    emailTemplate: EmailTemplateName,

    context?: any,
    emailType?: EmailType,
    emailSubject?: EmailSubject,
    emailSelectedPqPks?: PrimaryKey[],
    autoUpdate?: boolean,
}>()

//

const { useModel } = useContext()

const suspense = useSuspensableComponent(async () => {
    const { html, url } = await useModel('EmailTemplate').actions.preview({
        name: props.emailTemplate,
        context: props.context,
        type: props.emailType ?? null,
        subject: props.emailSubject ?? null,
        selected_pqs: props.emailSelectedPqPks?.map(Number) ?? null,
    })

    if (!html && !url) {
        throw new Error('No preview data')
    }

    if (html) {
        previewUrl.value = `data:text/html;charset=UTF-8,${encodeURIComponent(html)}`
    } else if (url) {
        previewUrl.value = url
    }
}, {
    throwOnError: false,
})

const selectedPqsKey = computed(() => {
    return JSON.stringify(props.emailSelectedPqPks)
})

watch([() => props.emailType, () => selectedPqsKey.value], (current, previous) => {
    if (previous) {
        suspense.fetch()
    }
}, { immediate: true })

const contextProperties = computed(() => {
    return JSON.stringify(props.context)
})

const updateTemplateDebounced = useDebounceFn(() => {
    return suspense.fetch()
}, 10000)

const onLoadTemplate = () => {
    return updateTemplateDebounced()
}

watch(contextProperties, (currentValue, previousValue) => {
    if (props.autoUpdate && currentValue !== previousValue) {
        onLoadTemplate()
    }
}, { immediate: true })

const previewUrl = ref<string>()

// manual template update

function updateTemplate() {
    suspense.fetch()
}

defineExpose({
    updateTemplate,
})
</script>
