<template>
    <SuspenseManual :state="suspense">
        <div class="-my-2 min-w-[20ch] flex items-center">
            <div class="mr-2">
                <div class="text-base font-medium text-secondary-900 dark:text-secondary-200 flex items-center">
                    <span
                        v-copy
                        class="cursor-[copy]"
                    >
                        {{ getClientFullName(client) }}
                    </span>

                    <RouterLink
                        v-if="hasPermission('openPage', 'Client')"
                        v-tooltip="'Open in new tab'"
                        target="_blank"
                        class="p-2 -m-2 ml-0 text-secondary-400"
                        :to="routeToClient(client.pk)"
                    >
                        <ExternalLinkIcon />
                    </RouterLink>
                </div>
                <div class="flex items-center gap-x-1.5">
                    <div class="text-secondary-400 text-xs font-medium">
                        #{{ leadPk }},
                    </div>
                    <div
                        v-if="leadContainsVouchers"
                        v-tooltip="{content: 'The client has an active voucher'}"
                        class="badge --success --ghost --outline"
                    >
                        Has Voucher
                    </div>
                    <div
                        v-if="clientStatus && !lead.is_deleted"
                        class="badge --soft"
                        :class="`--${clientStatus.variant}`"
                    >
                        {{ clientStatus.label }}
                    </div>
                    <div v-else-if="lead.is_deleted" class="badge --soft --danger">
                        Deleted
                    </div>
                    <div
                        v-if="lead.is_cs_lead"
                        v-tooltip="'Customer Support Lead'"
                        class="badge --pending"
                    >
                        CS
                    </div>
                    <span
                        v-if="showClientReachedBadge"
                        v-tooltip="{ content: 'Client reached' }"
                        class="--success --soft badge"
                    >
                        Reached
                    </span>
                    <template v-if="lead.from_iata_timezone">
                        <AppTicker
                            v-slot="{ date }"
                            :update-interval="30"
                        >
                            <div
                                v-tooltip="{ content: 'Customer local time' }"
                                class="--small --soft badge"
                                :class="isRestrictedTime(date) ? '--danger' : '--success'"
                            >
                                <ClockIcon />
                                {{ getClientTime(date) }}
                            </div>
                        </AppTicker>
                    </template>
                    <a
                        v-if="isLeadFromClientSide"
                        v-tooltip="{ content: utm.utm_ip }"
                        class="country-selector"
                        :class="utm.utm_ip_country"
                        :href="'https://ipinfo.io/'+utm.utm_ip"
                        target="_blank"
                        rel="nofollow"
                    />
                </div>
            </div>
        </div>

        <template #fallback>
            <PlaceholderBlock class="w-[20ch] h-[44px]" />
        </template>
    </SuspenseManual>
</template>

<script setup lang="ts">
import type { ModelAttributes } from '~types/lib/Model'
import { getPropFromConst } from '~/lib/Helper/ObjectHelper'
import { ClientStatusInfo } from '~/api/models/Client/ClientStatus'
import PlaceholderBlock from '@/components/Placeholder/PlaceholderBlock.vue'
import { getClientFullName } from '~/lib/Helper/PersonHelper'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import { useClientSessionActivity } from '~/composables/useClientSessionActivity'
import ClientEnrichedButton from '~/sections/Client/components/ClientEnrichedButton.vue'

defineOptions({
    name: 'PageLeadClientStatus',
})

const props = defineProps<{
    leadPk: PrimaryKey,
}>()

//

const { useModel, hasPermission } = useContext()
const { activitySession, fetch: fetchSession } = useClientSessionActivity()

//

const suspense = useSuspensableComponent(async () => {
    const promises: Promise<any>[] = [
        leadRecord.fetch(props.leadPk),
    ]

    if (isExpertMode) {
        promises.push((async () => {
            const taskPk = (await useModel('Lead').actions.getClientReachedTaskPk({ pk: props.leadPk })).task_pk
            await taskRecord.fetch(taskPk)
        })())
    }

    await Promise.all(promises)

    if (leadRecord.record.value.client_session_activity_pk) {
        await fetchSession(leadRecord.record.value.client_session_activity_pk)
    }
})

//
const externalResourceDictionary = useGeneralDictionary('ExternalResource')

const leadRecord = useModel('Lead').useRecord({
    with: ['clientPreview', 'utm'],
})

const lead = leadRecord.record
const client = computed(() => lead.value.clientPreview)
const utm = computed(() => lead.value.utm)

const isExpertMode = inject<boolean>('isExpertMode', false)

const isLeadFromClientSide = computed(() => {
    const externalResourceData = externalResourceDictionary.tryFind(lead.value.external_resource)

    return externalResourceData?.value === ExternalResource.WebSite
})

//

const taskRecord = useModel('Task').useRecord()
const showClientReachedBadge = computed(() => Boolean(taskRecord.record.value?.completed_at))

//

const getStatusInfo = (status: ModelAttributes<'ClientStatus'>) => {
    const info = getPropFromConst(ClientStatusInfo, status.system_name)

    if (!info) {
        useLogger('client-status').fatal(`Unknown client status: ${status}`)
    }

    return {
        label: status.name,
        variant: info?.variant ?? 'secondary',
    }
}

const clientStatus = computed(() => {
    const status = activitySession.value?.clientStatus

    if (!status) {
        return
    }

    return getStatusInfo(status)
})

const getClientTime = (now: Date) => {
    if (!lead.value.from_iata_timezone) {
        return
    }

    return new Intl.DateTimeFormat('en-GB', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: false,
        timeZone: lead.value.from_iata_timezone,
    }).format(now)
}

const isRestrictedTime = (now: Date) => {
    const formattedTime = Date.parseTimeParts(getClientTime(now))

    return Number(formattedTime.HH) >= 22 || Number(formattedTime.HH) < 8
}

// lead contains vouchers

const leadContainsVouchers = computed(() => {
    return leadRecord.record.value.voucher_pks && leadRecord.record.value.voucher_pks.length !== 0
})
</script>

