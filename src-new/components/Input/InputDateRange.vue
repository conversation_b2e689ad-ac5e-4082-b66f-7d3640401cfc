<template>
    <Dropdown
        ref="dropdownRef"
        :teleport="teleport"
    >
        <template #toggle>
            <div
                class="flex items-stretch w-full"
                @click="openIfPossible"
            >
                <InputText
                    ref="input"
                    :placeholder-icon="'date'"
                    v-bind="passableProps"
                    :model-value="value"
                    @keyup="handleKeyup"
                    @keydown="handleKeydown"
                    @focus="handleFocus"
                />
            </div>
        </template>

        <template #content>
            <div class="box border my-4 border-theme-38/20 dark:bg-dark-1 p-2 text-gray-800 flex">
                <div class="date-range-datepicker my-1">
                    <DatePicker
                        ref="calendar"
                        v-model="datePickerValue"
                        :is-dark="isDark"
                        :first-day-of-week="2"
                        :model-modifiers="{
                            range: true,
                        }"
                        :masks="settings"
                        :model-config="{}"
                        :popover="{visibility: 'click'}"
                        autocomplete="off"
                        timezone="UTC"
                    />
                </div>
                <div class="date-range-buttons flex flex-col flex-none space-y-1.5 ml-2">
                    <button
                        v-for="({ name, label }, $i) in dateRangePresets"
                        :key="$i"
                        class="btn btn-sm btn-outline-secondary box w-28"
                        type="button"
                        @click="selectRangePreset(name)"
                    >
                        {{ label }}
                    </button>
                </div>
            </div>
        </template>
    </Dropdown>
</template>

<script lang="ts">
import type { DateRangeName, DateRange, TimestampRange } from '@/lib/core/helper/DateHelper'

export const defaultRangePresets: DateRangeName[] = [
    'today',
    'yesterday',
    'thisWeek',
    'lastWeek',
    'thisMonth',
    'lastMonth',
    'thisYear',
    'lastYear',
]
</script>

<script setup lang="ts">
import { DatePicker } from 'v-calendar'
import { timestampRangeToDateRange } from '@/lib/core/helper/DateHelper'
import {
    dateRangeToTimestampRange,
    formatRange,
    moveDateRangeFromTimeZone,
    moveDateRangeToTimeZone,
    normalizeDateRange,
    parseDateStringToDateRange,
    predefinedRanges,
} from '@/lib/core/helper/DateHelper'
import type { Size } from '~/lib/UI'
import type { Emits, Props } from '~/lib/Input/useInputField'
import { useInputField } from '~/lib/Input/useInputField'
import { useAppTimezone } from '~/composables/useAppTimezone'

defineOptions({
    name: 'InputDateRange',
})

const props = withDefaults(defineProps<Omit<Props<DateRange | undefined | null>, 'type'> & {
    timestamp?: TimestampRange | undefined | null,
    dateFormat?: string,
    minDate?: Date,
    size?: Size
    teleport?: boolean | string,
    timezone?: 'UTC' | 'app',
    datePresets?: DateRangeName[] |((defaultPresets: DateRangeName[]) => DateRangeName[])
}>(), {
    // @default-props Can't export them from useInputField because of TS bug
    placeholder: undefined,
    size: 'normal',
    disabled: false,
    readonly: false,
    autofocus: false,
    icon: undefined,
    placeholderIcon: undefined,
    inputAttrs: undefined,

    //

    modelValue: undefined,
    timestamp: undefined,
    dateFormat: 'dd/MM/yyyy',
    minDate: undefined,
    teleport: false,
    timezone: 'app',
    datePresets: () => defaultRangePresets,
})

const emit = defineEmits<Emits<DateRange | undefined> & {
    'update:timestamp': [timestamp: TimestampRange | undefined],
    'update:preset': [presetName: DateRangeName],
    'keyup': [event: KeyboardEvent],
}>()

const settings = {
    weekdays: 'WW',
    title: 'MMM YYYY',
    input: 'DD/MM/YYYY',
}

//

const { passableProps } = useInputField(props)

//

const { isDark } = useDarkMode()
const appTimezone = useAppTimezone()

/**
 * See InputDate for explanation
 */
const shouldConvertTimezone = computed(() => {
    return props.timezone === 'app'
})

const datePickerValue = computed({
    get() {
        let range = props.modelValue ?? (props.timestamp ? timestampRangeToDateRange(props.timestamp) : undefined)

        // Check if JS mutation was applied
        if (range && (!range.start.unixTimestamp || !range.end.unixTimestamp)) {
            range = {
                start: new Date(range.start),
                end: new Date(range.end),
            }
        }

        if (range && shouldConvertTimezone.value) {
            range = moveDateRangeToTimeZone(range, appTimezone.value)
        }

        return range || undefined
    },

    set(range: DateRange | undefined) {
        if (range) {
            range = normalizeDateRange(range)
        }

        if (range && shouldConvertTimezone.value) {
            range = moveDateRangeFromTimeZone(range, appTimezone.value)
        }

        if (!range) {
            range = undefined
        }

        emit('update:modelValue', range)
        emit('update:timestamp', range ? dateRangeToTimestampRange(range) : undefined)

        close()
    },
})

const value = computed(() => {
    const value = datePickerValue.value

    if (!value) {
        return ''
    }

    return formatRange(value, props.dateFormat)
})

const input = ref<{
    input: HTMLInputElement
}>()
const dropdownRef = ref()

//

function handleKeyup(event: KeyboardEvent) {
    emit('keyup', event)

    if (event.key === 'Enter') {
        const value = input.value?.input.value

        // @important Works only with dd/MM/yyyy format
        if (value) {
            selectCustomRange(parseDateStringToDateRange(value))
        } else {
            selectCustomRange()
        }

        close()
    }
}

function handleKeydown(event: KeyboardEvent) {
    emit('keydown', event)

    if (event.key === 'Enter') {
        close()
    }
}

function handleFocus(event: FocusEvent) {
    emit('focus', event)

    openIfPossible()
}

function openIfPossible() {
    if (props.readonly || props.disabled) {
        return
    }

    open()
}

function open() {
    dropdownRef.value.open()
}

function close() {
    dropdownRef.value.close()
}

//

const dateRangePresets = computed(() => {
    const presets = typeof props.datePresets === 'function' ?
        props.datePresets(defaultRangePresets) :
        props.datePresets

    return presets.map((name) => {
        const range = predefinedRanges[name]

        return {
            label: range.label,
            name: name as DateRangeName,
        }
    })
})

const selectCustomRange = (rangeData?: DateRange) => {
    if (rangeData) {
        datePickerValue.value = normalizeDateRange({
            start: rangeData.start,
            end: rangeData.end,
        })
    } else {
        datePickerValue.value = undefined
    }
}

const selectRangePreset = (presetName: DateRangeName) => {
    const rangeData = predefinedRanges[presetName]

    // Today timezone for ranges are always app timezone
    selectCustomRange(rangeData.resolver(appTimezone.value))

    emit('update:preset', presetName)
}
</script>

