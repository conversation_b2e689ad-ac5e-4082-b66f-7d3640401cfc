<template>
    <th
        :colspan="headingsToRender?.length"
        v-bind="attrs"
        class="--paddingless"
    >
        <div v-if="group.label" class="flex justify-center pt-1">
            {{ group.label }}
        </div>
        <div
            class="list-table-v2__group"
            :class="{
                'justify-center': group.center,
                'pt-0 pb-1': group.label,
            }"
        >
            <template v-for="({ heading }, index) of headingsToRender" :key="index">
                <span
                    v-tooltip="heading.tooltip"
                    class="list-table-v2__group__label"
                    :class="{
                        '--sort': hasSort,
                        '--active': isActiveSort(heading),
                    }"
                    @click="sort(heading)"
                >
                    <Component
                        :is="heading.icon"
                        v-if="heading.icon"
                    />
                    {{ heading.label }}
                </span>

                <span v-if="index !== headingsToRender.length - 1">
                    /
                </span>
            </template>
            <div
                v-if="hasSort"
                class="list-table-v2__group__arrow"
                :class="{
                    '--sort': hasSort,
                    '--active': isActiveGroupSort,
                    '--asc': isActiveGroupSort && sortDirection === 'asc',
                }"
            >
                <ArrowDownIcon />
            </div>
        </div>
        <Dropdown v-if="hasSearchTags" class="list-table-v2__filter">
            <template #toggle="{ toggle }">
                <AppButton class="--small w-full text-secondary-400" @click="toggle">
                    Filter <FilterLinesIcon />
                </AppButton>
            </template>
            <template #content>
                <div class="flex flex-col card p-4 mt-2 gap-6">
                    <template v-for="({ heading, options }, index) of headingsToRender" :key="index">
                        <div
                            v-if="heading.searchTag?.component"
                            :key="index"
                            class="w-[200px]"
                        >
                            <div
                                class="flex flex-col gap-1.5"
                                :class="{
                                    '!flex-row items-center gap-2': options.inline,
                                }"
                            >
                                <div
                                    class="list-table-v2__group__label flex items-center justify-between gap-4"
                                    :class="{
                                        '--sort': hasSort,
                                        '--active': isActiveSort(heading),
                                    }"
                                    @click="sort(heading)"
                                >
                                    {{ options.label ?? heading.label }}

                                    <div
                                        v-if="heading.sortable"
                                        class="list-table-v2__group__arrow"
                                        :class="{
                                            '--sort': hasSort,
                                            '--active': isActiveSort(heading),
                                            '--asc': isActiveSort(heading) && getSortDirection(heading) === 'asc',
                                        }"
                                    >
                                        <ArrowDownIcon />
                                    </div>
                                </div>

                                <Component
                                    :is="heading.searchTag.component"
                                    :tag="heading.searchTag"
                                    :heading="heading"
                                    :model-value="heading.searchTag.isMultiple ? heading.searchTag.values : heading.searchTag.values?.[0]"
                                    @update:model-value="filter(heading, $event)"
                                />
                            </div>
                        </div>
                    </template>
                </div>
            </template>
        </Dropdown>
    </th>
</template>

<script setup lang="ts">
import type { TableGroupDefinition, TableHeading } from '~/composables/useTableColumns'
import type SortController from '~/lib/Search/SortController'
import { ensureArray } from '~/lib/Helper/ArrayHelper'
import FilterLinesIcon from '~assets/icons/pack/FilterLinesIcon.svg?component'
import { normalizeGroupColumns } from '~/components/Table/AppTable.vue'

const props = withDefaults(defineProps<{
    headings: TableHeading[]
    sortController?: SortController,
    group: TableGroupDefinition,
}>(), {
    headings: undefined,
    sortController: undefined,
})
const emit = defineEmits<{
    sort: [heading: TableHeading],
}>()

const attrs = useAttrs()

//

const headingsToRender = computed(() => {
    const normalizedColumns = normalizeGroupColumns(props.group)

    return props.headings.map((heading) => {
        return {
            heading,
            options: normalizedColumns[heading.field],
        }
    })
})

const hasSearchTags = computed(() => {
    return props.headings.filter((heading) => heading.searchTag).length
})

const hasSort = computed(() => {
    return props.headings.filter((heading) => heading.sortable).length
})

function filter(heading: TableHeading, value: any) {
    const searchTag = heading.searchTag

    if (!searchTag) {
        return
    }

    if (!searchTag.isMultiple) {
        value = [value]
    }

    searchTag.setValues(ensureArray(value).filter((v) => v !== undefined))
}

function isActiveSort(heading: TableHeading) {
    return props.sortController?.field === heading.field
}

function getSortDirection(heading: TableHeading) {
    return props.sortController?.direction
}

const isActiveGroupSort = computed(() => {
    return props.headings.some((heading) => props.sortController?.field === heading.field)
})

const sortDirection = computed(() => {
    return props.sortController?.direction
})

const sort = (heading: TableHeading) => {
    if (heading.sortable) {
        emit('sort', heading)
    }
}
</script>
