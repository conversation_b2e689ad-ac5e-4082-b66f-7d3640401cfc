<template>
    <div class="bg-theme-10 p-12 m-24">
        <div class="bg-red-400 h-12 w-[512px]" />

        <AppButton @click="loginInRingcentral">
            Login in Ringcentral
        </AppButton>
        <AppButton @click="subscribe">
            Subscribe
        </AppButton>

        {{ $format.money(2000) }}
    </div>
</template>

<script setup async lang="ts">
import type { Model, ModelAttributes } from '~types/lib/Model'

const rc = useService('ringcentralManager')

function subscribe() {
    rc.getService({ _pk: '5' }).subscribe()
}

function loginInRingcentral() {
    rc.getService({ _pk: '5' }).openLoginWindow()
}

const { useModel } = useNewContext('selected')

type A = Model.Relation.FieldRecursive<'Agent'>
const a1: A = 'exTeam.agents'
const b1: A = 'exTeamdd.agents'

const agentList = useModel('Lead').useList({
    with: ['executor', 'executor.team'],
})

const agents = agentList.records
const agent = {} as ModelAttributes<'Agent'>

const a = agents[0]
const b = a.department_pk
const team = agents[0].executor?.team?.department_pk
const pk = agent.ex_team_pk
</script>
