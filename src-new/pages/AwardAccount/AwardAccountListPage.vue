<template>
    <div class="layout__content layout__content--list">
        <WorkspaceWrapper :workspace="componentWorkspace">
            <div class="flex items-center justify-between gap-4 mb-4">
                <SearchFilter
                    v-if="searchController"
                    class="w-full max-w-[800px]"
                    :controller="searchController"
                />

                <div class="flex items-center gap-4">
                    <div v-if="workspaces.length > 1" class="button-group">
                        <AppButton
                            v-for="workspace in workspaces"
                            :key="workspace.pk"
                            :class="{
                                '--neutral': workspace.pk === componentWorkspace,
                            }"
                            @click="componentWorkspace = workspace.pk"
                        >
                            {{ workspace.title }}
                        </AppButton>
                    </div>

                    <AppButton
                        v-if="hasPermission('manageAwardDetails', 'Sale', undefined, {
                            workspace: componentWorkspace,
                        })"
                        class="ml-auto"
                        @click="addModal.open(undefined, { workspace: componentWorkspace })"
                    >
                        Create account
                        <PlusIcon />
                    </AppButton>

                    <AppButton @click="quickAltVpnModal.open()">
                        <CloudLightningIcon />Quick alt. vpn
                    </AppButton>

                    <AppButton
                        v-if="hasPermission('openQuickVpnLog', 'all', undefined, {
                            workspace: componentWorkspace,
                        })"
                        @click="quickAltVpnLogModal.open()"
                    >
                        <ChevronsLeftIcon />Quick alt. vpn Log
                    </AppButton>

                    <AppButton
                        @click="moveMilesModal.open()"
                    >
                        Move miles
                    </AppButton>

                    <AppButton
                        @click="logsModal.open()"
                    >
                        <ChevronsLeftIcon />
                        Show logs
                        <InfoIcon />
                    </AppButton>

                    <AppButton
                        @click="toggleSummaryBlock"
                    >
                        <PieChartIcon />
                        Show Summary
                    </AppButton>
                </div>
            </div>

            <AwardAccountSummarySection
                v-if="isSummaryBlockOpened && searchController"
                class="mb-4"
                :search-controller="searchController"
            />

            <AwardAccountListSection ref="listRef" />
        </WorkspaceWrapper>
    </div>
</template>

<script setup lang="ts">
import type SearchController from '~/lib/Search/SearchController'
import AwardAccountCreateModal from '~/sections/AwardAccount/modals/AwardAccountCreateModal.vue'
import AwardAccountLogsSideModal from '~/sections/AwardAccount/modals/AwardAccountLogsSideModal.vue'
import AwardAccountListSection from '~/sections/AwardAccount/List/AwardAccountListSection.vue'
import { useAwardAccountModal } from '~/lib/AwardAccount/composables/useAwardAccountModal'
import { hasPermission } from '~/composables/hasPermission'
import WorkspaceWrapper from '~/components/WorkspaceWrapper.vue'
import SearchFilter from '~/components/Search/SearchFilter.vue'
import AwardAccountMoveMilesModal from '~/sections/AwardAccount/modals/AwardAccountMoveMilesModal.vue'
import AwardAccountQuickAltVpnModal from '~/sections/AwardAccount/modals/AwardAccountQuickAltVpnModal.vue'
import AwardAccountQuickAltVpnLogModal from '~/sections/AwardAccount/modals/AwardAccountQuickAltVpnLogModal.vue'
import AwardAccountSummarySection from '~/sections/AwardAccount/Info/AwardAccountSummarySection.vue'

defineOptions({
    name: 'AwardAccountListPage',
})

const props = defineProps<{
    pk?: PrimaryKey
}>()

//

useAwardAccountModal({
    pk: computed(() => props.pk),
})

//

const addModal = useModal(AwardAccountCreateModal)
const logsModal = useModal(AwardAccountLogsSideModal)
const moveMilesModal = useModal(AwardAccountMoveMilesModal)
const quickAltVpnModal = useModal(AwardAccountQuickAltVpnModal)
const quickAltVpnLogModal = useModal(AwardAccountQuickAltVpnLogModal)

//

const listRef = ref<{
    searchController: SearchController,
}>()

const searchController = computed(() => listRef.value?.searchController)

//

const workspaces = useGeneralDictionary('Workspace').records

const componentWorkspace = ref(workspaces[0].pk)

const isSummaryBlockOpened = ref(false)

const toggleSummaryBlock = () => {
    isSummaryBlockOpened.value = !isSummaryBlockOpened.value
}
</script>
