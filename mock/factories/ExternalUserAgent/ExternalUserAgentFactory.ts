import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { ExternalUserAgentStatus } from '~/api/models/ExternalUserAgent/ExternalUserAgent'
import { faker } from '@faker-js/faker'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'ExternalUserAgent'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ExternalUserAgentFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const agents = await findModelRecords('Agent')

        const externalUserAgent = makeModelRecord(modelName, {
            status: randomEnumValue(ExternalUserAgentStatus),
            name: faker.word.adjective(),
            note: faker.word.words(10),
            profile_id: '5d5a042f-0d8a-4fda-aaed-238dbdb051d0', // faker.word.adjective(),
            folder_id: 'c6d1a576-649c-4725-9aed-292430b2f575', // faker.word.adjective(),
            created_by_pk: randomElementPk(agents),
            created_at: faker.date.past().getTime(),
            last_opened_at: null,
            last_opened_by_pk: null,
            advanced_settings: {
                user_agent: null,
                screen_resolution_custom: null,
                hardware_concurrency: null,
                oscpu: null,
                platform: null,
                video_input_count: 1,
                whitelisted_ports: '',

                screen_masking: 'mask',

                media_devices_masking: 'mask',
                audio_input_count: 1,
                audio_output_count: 1,

                graphics_noise: 'mask',
                graphics_masking: 'noise',
                canvas_noise: 'mask',
                audio_masking: 'noise',

                navigator_masking: 'mask',

                ports_masking: 'mask',

                fonts_masking: 'mask',
                browser_type: 'stealthfox',
            },
            ...attributes,
        })

        return externalUserAgent
    }
}
