import type { ModelFields } from '~types/lib/Model'
import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { ExternalUserAgentProxyType } from '~/api/models/ExternalUserAgent/ExternalUserAgentProxy'
import { randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { randomElement } from '~/lib/Helper/ArrayHelper'

const modelName = 'ExternalUserAgentProxy'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ExternalUserAgentProxyFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const type = attributes.type ?? randomEnumValue(ExternalUserAgentProxyType)

        return makeModelRecord(modelName, {
            type,
            location: {
                city: faker.location.city(),
                region: faker.location.state(),
                country: faker.location.country(),
            },
            credential: type === ExternalUserAgentProxyType.Multilogin ? null : {
                protocol: 'http',
                username: faker.internet.userName(),
                password: faker.internet.password(),
                host: faker.internet.ip(),
                port: faker.internet.port(),
            },
            ...attributes,
        })
    }
}
