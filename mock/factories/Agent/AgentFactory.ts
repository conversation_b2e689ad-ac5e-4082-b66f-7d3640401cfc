import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'
import { Gender } from '~types/enums/Gender'
import { usePk } from '~/composables/usePk'
import { DepartmentCategory } from '~/api/models/Agent/Agent'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { ShiftType } from '~/api/models/Shift/Shift'
import { HiringSource } from '~/api/dictionaries/Static/Agent/HiringSourceDictionary'

const modelName = 'Agent'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class AgentFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        const agent = await createModelRecord(modelName, await this.make(attributes))
        const agentPersonalFullName = agent.personal_fullname.split(' ')
        const agentPersonalFirstName = agentPersonalFullName[0]
        const agentPersonalLastName = agentPersonalFullName.slice(1).join('')

        // Create related models
        await createModelRecord('AgentAdditionalPersonalInfo', {
            id: agent.id,
            first_name: agentPersonalFirstName,
            last_name: agentPersonalLastName,
            phone: faker.phone.number(),
            email: agent.email,
            address: faker.location.streetAddress(),
            serial_nr: '200' + faker.string.numeric(10),
            idnp: 'B' + faker.string.numeric(8),
        })

        await createModelRecord('AgentAdditionalTransferClientsInfo', {
            id: agent.id,
            active_transfer_pk: null,
            last_completed_transfer_pk: null,
            can_start_take_transfer: true,
            can_start_restore_transfer: false,
        })

        await createModelRecord('AgentAdditionalState', {
            id: agent.id,
            is_online: randomBoolean(),
            last_login_at: withChance(dateToUnixTimestamp(faker.date.past())),
        })

        await createModelRecord('AgentAdditionalGDSInfo', {
            id: agent.id,
            sabre_id: faker.string.alphanumeric(6),
            sabre_lniata: faker.string.alphanumeric(3),
            sabre_initials: faker.string.alphanumeric(3),
        })

        await createModelRecord('AgentAdditionalBonusInfo', {
            id: agent.id,
            rate: randomInt(100, 200),
            working_period: randomInt(1, 10),
        })

        // await createModelRecord('AgentAdditionalSalaryInfo', {
        //     id: agent.id,
        //     value: randomInt(100, 200),
        // })

        return agent
    }

    public async make(
        attributes: Attributes = {},
    ) {
        /*
        * @todo
        * we're calling method create (which calls the make method) about 55 times in seeder.
        * Each time we call db 3 times (findModelRecords)
        * So how to optimize it?
        * */
        const teams = await findModelRecords('Team')
        const positions = await findModelRecords('Position')
        const departments = await findModelRecords('Department')

        return makeModelRecord(modelName, {
            first_name: faker.person.firstName(),
            last_name: faker.person.lastName(),
            personal_fullname: faker.person.fullName(),
            phone: faker.phone.number(),
            email: faker.internet.email(),
            sex: randomEnumValue(Gender),
            birthday_at: dateToUnixTimestamp(faker.date.birthdate()),
            voip_extension: '+123',

            avatar: faker.image.avatar(),

            is_enabled: randomBoolean(0.95),
            requested_hired_at: dateToUnixTimestamp(faker.date.past()),
            created_at: dateToUnixTimestamp(faker.date.past()),
            requested_dismissed_at: null,
            dismissed_at: null,

            team_pk: withChance(randomElementPk(teams), 0.8),
            position_pk: randomElementPk(positions),
            department_pk: randomElementPk(departments),
            project_pk: usePk('Project', MockProjectID.TBC),
            department_category: withChance([randomEnumValue(DepartmentCategory)]) || [],
            curator_pk: null,
            replaced_by_pk: null,
            is_beginner: randomBoolean(),
            shift_type: randomEnumValue(ShiftType),
            ringcentral_id: 1346894040, // andy RC debug other
            is_bot: false,
            hiring_source: withChance(randomEnumValue(HiringSource)),
            hiring_source_remark: withChance(faker.lorem.words(2)),
            //
            ...attributes,
        })
    }
}
