import type { ApiResponse } from '~types/lib/Api'
import { useModelDefinition } from '~/composables/_core/useModelDefinition'
import type { H3Event } from 'h3'
import { H3Error } from 'h3'
import type { ZodError } from 'zod'

export default defineAuthenticatedEventHandler<ApiResponse | void>(async (request) => {
    const model = request.context.params?.model as string
    const actionName = request.context.params?.action as string

    if (!model) {
        throw new Error('No model provided')
    }

    if (!actionName) {
        throw new Error('No action provided')
    }

    const query = getQuery(request)
    const waitsForResponse = query.waitsForResponse === 'true'

    const handler = useModelHandlerOrFail(model)

    const action = handler.actions[actionName]

    if (!action) {
        throw new Error(`Event ${actionName} does not exist`)
    }

    const payload = await readBody(request)

    const modelDefinition = useModelDefinition(model)

    if (!modelDefinition) {
        throw new Error(`Model "${model}" does not exist`)
    }

    let params = modelDefinition.actions[actionName].params

    if (params._def.typeName === 'ZodNull') {
        params = z.object({})
    }

    const validated = params.safeParse(payload)

    if (!validated.success) {
        throwPayloadError(request, validated)

        return
    }

    const result = await action(payload, waitsForResponse, request)

    if (!waitsForResponse) {
        return null as unknown as void
    }

    return {
        result: result.response as unknown,
    }
})

export const throwPayloadError = (request: H3Event, validated: { error: ZodError }) => {
    const error = new H3Error('Invalid action payload')

    const data: any = {}

    console.error(validated.error.errors)

    for (const value of Object.values(validated.error.errors)) {
        const key = value.path.join('.')

        data[key] = data[key] || []

        data[key].push(value.message + (value.code === 'invalid_type' ? ` (${value.expected})` : ''))
    }

    error.name = 'ApiFormError'

    error.data = {
        error: {
            type: 'Form',
            data,
        },
        message: 'Invalid action payload',
        success: false,
    }

    error.statusCode = 400
    error.statusMessage = 'Invalid action payload'

    sendError(request, error, true)
}
