import ModelDatabaseSeeder from '~mock/seeds/ModelDatabaseSeeder'
import { dateToUnixTimestamp, items, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import type { ModelAttributes, ModelFields } from '~types/lib/Model'
import ChatSeederSimple from '~mock/seeds/models/Chat/ChatSeederSimple'
import ChatGroupSeederSimple from '~mock/seeds/models/Chat/ChatGroupSeederSimple'
import { deleteModelRecords, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import ChatMessageSeederSimple from '~mock/seeds/models/Chat/ChatMessageSeederSimple'
import ChatAdditionalInfoSeederSimple from '~mock/seeds/models/Chat/ChatAdditionalInfoSeederSimple'
import SaleVersionSeeder from '~mock/seeds/models/Sale/SaleVersionSeeder'
import SaleAdditionalProfitsSeederSimple from '~mock/seeds/models/Sale/SaleAdditionalProfitsSeederSimple'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'
import { usePk } from '~/composables/usePk'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import type { ModelLinkIdentification } from '~types/lib/Model'
import { TaskCategory } from '~/api/models/Task/TaskGroup'
import TaskGroupFactory from '~mock/factories/Task/TaskGroupFactory'
import { faker } from '@faker-js/faker'
import { SaleType } from '~/api/models/Sale/Sale'
import TaskFactory from '~mock/factories/Task/TaskFactory'
import type { bookkeepingHandlers, disclaimerHandlers } from '~/sections/Tasks/composables/useTasksActions'
import SaleAdditionalInfoFactory from '~mock/factories/Sale/SaleAdditionalInfoFactory'
import CallContactAttemptFactory from '~mock/factories/Call/CallContactAttemptFactory'
import { randomInt } from '~/lib/Helper/NumberHelper'
import SaleMemberSeeder from '~mock/seeds/models/Sale/SaleMemberSeeder'
import { ExternalResource } from '~/api/dictionaries/Static/ExternalResourceDictionary'
import { TestChatPK } from '~/api/models/Chat/Chat'

const modelName = 'Sale'

export default class SaleSeeder extends ModelDatabaseSeeder<typeof modelName> {
    public modelName = modelName

    public readonly relatedSeeders = {
        profits: new SaleAdditionalProfitsSeederSimple(),

        //

        chat: new ChatSeederSimple(),
        chatGroup: new ChatGroupSeederSimple(),
        message: new ChatMessageSeederSimple(),
        chatInfo: new ChatAdditionalInfoSeederSimple(),
        saleVersions: new SaleVersionSeeder(),
        saleMembers: new SaleMemberSeeder(),
    }

    public factories = {
        taskGroup: new TaskGroupFactory(),
        task: new TaskFactory(),
        info: new SaleAdditionalInfoFactory(),
        callContactAttempt: new CallContactAttemptFactory(),
    }

    public async data() {
        const clients = await findModelRecords('Client')
        const agents = await findModelRecords('Agent')
        const saleVersionRecords = await findModelRecords('SaleVersion')
        const leads = await findModelRecords('Lead')

        const isClosed = randomBoolean()

        return items(24).map((id: number): ModelFields<typeof modelName> => ({
            id,

            is_split: randomBoolean(),
            client_pk: randomElementPk(clients),
            executor_pk: randomElementPk(agents),
            chat_pk: TestChatPK,
            old_bo_id: faker.number.int(),
            sale_version_pk: saleVersionRecords?.length ? randomElementPk(saleVersionRecords) : String(id),
            is_adjusted: randomBoolean(),
            sale_at: dateToUnixTimestamp(faker.date.past({ years: 1 })),
            type: randomEnumValue(SaleType),
            lead_pk: randomElementPk(leads),
            is_hidden: randomBoolean(),
            is_paid_fully: randomBoolean(),
            is_ticket_sent: randomBoolean(),
            is_paid_bsb: randomBoolean(),
            is_sale_closed: isClosed,
            is_sale_adjusted: randomBoolean(),
            is_finances_verified: randomBoolean(),
            is_pending_approval: randomBoolean(),
            is_product_description: randomBoolean(),
            is_pnr_provided: randomBoolean(),
            is_customer_verified: randomBoolean(),
            is_sale_approved: randomBoolean(),
            is_sale_issued: randomBoolean(),
            is_sale_charged: randomBoolean(),
            is_enable_automatic_transactions: randomBoolean(),
            external_resource: randomEnumValue(ExternalResource),
            is_test: randomBoolean(),
            closed_by_pk: isClosed ? randomElementPk(agents) : null,
            client_session_activity_pk: null,
            // Other
            project_pk: usePk('Project', MockProjectID.TBC),
        }))
    }

    public async clear(): Promise<void> {
        await super.clear()

        await this.registerClear([
            this.relatedSeeders.chat, // Cleared in observer
            this.relatedSeeders.profits, // Cleared in observer

            // Seeders below are handled in Chat observer
            this.relatedSeeders.chatGroup,
            this.relatedSeeders.message,
            this.relatedSeeders.chatInfo,
            this.relatedSeeders.saleVersions,
            this.relatedSeeders.saleMembers,
        ], async () => {
            return await deleteModelRecords('Chat', {
                model_name: modelName,
            })
        })
    }

    public async afterMake(item: ModelAttributes<typeof modelName>): Promise<void> {
        await this.relatedSeeders.profits.create()

        await this.relatedSeeders.saleVersions.createVersion({
            sale_pk: usePk(item),
        })

        if (item.is_split) {
            const agents = await findModelRecords('Agent')
            const members = items(randomInt(2, 5)).map(() => randomElementPk(agents))

            await this.relatedSeeders.saleMembers.createMembers({
                agents: members,
                sale_pk: item._pk,
            })
        }

        const modelInfo: ModelLinkIdentification = {
            model_name: modelName,
            model_pk: usePk(item),
        }

        await this.factories.taskGroup.create({
            category: TaskCategory.General,
            ...modelInfo,
        }, {
            withTasks: true,
        })

        await this.factories.taskGroup.create({
            category: TaskCategory.CustomerSupport,
            ...modelInfo,
        }, {
            withTasks: true,
        })

        const disclaimerGroup = await this.factories.taskGroup.create({
            category: TaskCategory.Disclaimer,
            ...modelInfo,
        })

        const disclaimers: {
            message: string
            handler: typeof disclaimerHandlers[number]
        }[] = [
                {
                    message: 'Upgrade',
                    handler: 'DisclaimerUpgradeTask',
                },
                {
                    message: 'FRT (fake return)',
                    handler: 'DisclaimerFakeReturnTask',
                },
                {
                    message: 'ELR (extra leg)',
                    handler: 'DisclaimerExtraLagTask',
                },
                {
                    message: 'Award',
                    handler: 'DisclaimerAwardTask',
                },
                {
                    message: 'Mixed Cabin',
                    handler: 'DisclaimerMixedCabinTask',
                },
                {
                    message: 'Mixed Baggage',
                    handler: 'DisclaimerMixedBaggageTask',
                },
            ]

        for (const item of disclaimers) {
            await this.factories.task.create({
                task_group_pk: usePk(disclaimerGroup),
                description: item.message,
                handler: item.handler,
                is_system: true,
                is_autocompleted: false,
                need_confirm: false,
                can_set_expiration_time: false,
                can_extend_expiration_time: false,
                completed_at: null,
                department_pk: null,
            })
        }

        const bookkeepingGroup = await this.factories.taskGroup.create({
            category: TaskCategory.Bookkeeping,
            ...modelInfo,
        })

        const bookkeepingRequests: {
            message: string
            handler: typeof bookkeepingHandlers[number]
        }[] = [
                {
                    message: 'Price Drop',
                    handler: 'BookkeepingPriceDropTask',
                },
                {
                    message: 'Alt. Extra',
                    handler: 'BookkeepingAltExtraTask',
                },
            ]

        for (const item of bookkeepingRequests) {
            await this.factories.task.create({
                task_group_pk: usePk(bookkeepingGroup),
                description: item.message,
                handler: item.handler,
                is_system: true,
                is_autocompleted: false,
                need_confirm: false,
                can_set_expiration_time: false,
                can_extend_expiration_time: false,
                completed_at: null,
                department_pk: null,
            })
        }

        await this.factories.info.create({
            id: item.id,
        })

        await this.factories.callContactAttempt.create({})
    }
}
